{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@medpro-libs/medpro-component-libs": ["libs/medpro-component-libs/src/index.ts"], "@medpro-sdk-v2": ["libs/medpro-sdk-v2/src"]}}, "exclude": ["node_modules", "tmp"]}
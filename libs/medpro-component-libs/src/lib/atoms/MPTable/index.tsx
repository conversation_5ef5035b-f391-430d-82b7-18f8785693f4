import React, { useContext, useEffect, useMemo } from 'react'
import { Button, Table } from 'antd'
import Image from 'next/image'
import { DndContext, type DragEndEvent } from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import { restrictToVerticalAxis } from '@dnd-kit/modifiers'
import { CSS } from '@dnd-kit/utilities'
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities'
import { HolderOutlined } from '@ant-design/icons'
import IconTableEmpty from '../../images/iconTableEmpty.png'
import styles from './styles.module.less'

export interface MPTableProps {
  type?: 'dragTable' | 'table'
  columns: any
  dataSource: any
  dynHeightContent?: number
  heightTbBody?: number
  loading?: boolean
  pagination?: any
  onChangePageEvent?: (value: any) => void
  onChangeSizeEvent?: (value: any) => void
  onDragSortTable?: (data: any) => void
  [key: string]: any
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void
  listeners?: SyntheticListenerMap
}

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string
}

const RowContext = React.createContext<RowContextProps>({})

export const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext)
  return (
    <Button
      type='text'
      size='small'
      icon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  )
}

const Row: React.FC<RowProps> = (props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: props['data-row-key'] })

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    touchAction: 'auto',
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {})
  }

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners]
  )

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  )
}

const MPTable = ({
  type,
  columns,
  dataSource,
  dynHeightContent,
  heightTbBody,
  loading,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onDragSortTable
}: MPTableProps) => {
  const [initDataSource, setInitDataSource] = React.useState<any[]>([])

  useEffect(() => {
    if (dataSource) {
      setInitDataSource(dataSource)
    }
  }, [dataSource])

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setInitDataSource((prevState) => {
        const activeIndex = prevState.findIndex(
          (record) => record.key === active?.id
        )
        const overIndex = prevState.findIndex(
          (record) => record.key === over?.id
        )
        if (onDragSortTable) {
          onDragSortTable(arrayMove(prevState, activeIndex, overIndex))
        }
        return arrayMove(prevState, activeIndex, overIndex)
      })
    }
  }
  return (
    <>
      {type === 'dragTable' ? (
        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            items={initDataSource.map((i: any) => i.key)}
            strategy={verticalListSortingStrategy}
          >
            <Table<any>
              columns={columns}
              dataSource={initDataSource}
              components={{ body: { row: Row } }}
              style={{ height: dynHeightContent }}
              className={styles[initDataSource.length ? 'table' : 'tableEmpty']}
              scroll={{ x: 'max-content', y: heightTbBody }}
              loading={{
                spinning: loading
              }}
              pagination={
                pagination
                  ? {
                      ...pagination,
                      showSizeChanger: true,
                      onChange: (p) =>
                        onChangePageEvent ? onChangePageEvent(p) : null,
                      onShowSizeChange: (current, size) => {
                        if (onChangeSizeEvent) {
                          onChangeSizeEvent(size)
                        }
                      },
                      locale: { items_per_page: '/ Trang' }
                    }
                  : false
              }
              locale={{
                emptyText: (
                  <div>
                    <Image src={IconTableEmpty} alt={''} priority />
                    <div style={{ color: '#656565' }}>Không có dữ liệu</div>
                  </div>
                )
              }}
            />
          </SortableContext>
        </DndContext>
      ) : (
        <Table<any>
          columns={columns}
          dataSource={initDataSource}
          style={{ height: dynHeightContent }}
          className={styles[initDataSource.length ? 'table' : 'tableEmpty']}
          scroll={{ x: 'max-content', y: heightTbBody }}
          loading={{
            spinning: loading
          }}
          pagination={
            pagination
              ? {
                  ...pagination,
                  showSizeChanger: true,
                  onChange: (p) =>
                    onChangePageEvent ? onChangePageEvent(p) : null,
                  onShowSizeChange: (current, size) => {
                    if (onChangeSizeEvent) {
                      onChangeSizeEvent(size)
                    }
                  },
                  locale: { items_per_page: '/ Trang' }
                }
              : false
          }
          locale={{
            emptyText: (
              <div>
                <Image src={IconTableEmpty} alt={''} priority />
                <div style={{ color: '#656565' }}>Không có dữ liệu</div>
              </div>
            )
          }}
        />
      )}
    </>
  )
}

export default MPTable

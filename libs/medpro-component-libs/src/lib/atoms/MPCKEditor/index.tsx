import React, { useEffect, useState } from 'react'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import { Skeleton } from 'antd'
import cx from 'classnames'
import { MdOutlineZoomOutMap, MdZoomInMap } from 'react-icons/md'
import { UploadAdapterPlugin } from './UploadAdapter'
import styles from './styles.module.less'

const MPCKEditor = ({
  uploadProps,
  value = '',
  onChange
}: {
  value?: string
  onChange?: (data: any) => void
  uploadProps?: any
}) => {
  const [Editor, setEditor] = useState<any>(null)
  const [isFullHeight, setIsFullHeight] = useState(false)

  useEffect(() => {
    // @ts-ignore
    import('ckeditor5-custom-build')
      .then((mod) => setEditor(() => mod.default))
      .catch((err) => console.error('Editor loading error:', err))
  }, [])

  if (!Editor)
    return (
      <Skeleton
        className={styles['skeletonWrapper']}
        active
        title={false}
        paragraph={{ rows: 5 }}
      />
    )

  return (
    <div
      className={cx(
        styles['editorWrapper'],
        isFullHeight ? styles['editorFullHeight'] : null
      )}
    >
      <CKEditor
        editor={Editor}
        data={value}
        config={{
          extraPlugins: [UploadAdapterPlugin(uploadProps)],
          removePlugins: ['Base64UploadAdapter', 'Link', 'Code', 'FindAndReplace']
        }}
        onReady={(editor: any) => {
          if (editor.ui?.view?.toolbar?.element) {
            editor.ui
              .getEditableElement()
              .parentElement?.insertBefore(
                editor.ui.view.toolbar.element,
                editor.ui.getEditableElement()
              )
          }
        }}
        onChange={(event: any, editor: any) => {
          const data = editor.getData()
          onChange?.(data)
        }}
      />
      <div
        onClick={() => setIsFullHeight(!isFullHeight)}
        className={styles['actionFullHeight']}
      >
        {isFullHeight ? (
          <span>
            <MdZoomInMap />
          </span>
        ) : (
          <span>
            <MdOutlineZoomOutMap />
          </span>
        )}
      </div>
    </div>
  )
}

export default MPCKEditor

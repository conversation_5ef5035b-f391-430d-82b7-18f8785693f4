import { showError } from '../../../helpers/notification'

class UploadAdapter {
  loader: any
  uploadProps: any

  constructor(loader: any, uploadProps: any) {
    this.loader = loader
    this.uploadProps = uploadProps
  }

  async upload() {
    return this.loader.file.then(async (file: File) => {
      if (!this.uploadProps) {
        showError('<PERSON>hiếu thông tin cấu hình uploadProps')
        return Promise.reject()
      }
      const { uploadPreset, action, headers } = this.uploadProps
      try {
        const isLt1M5 = file.size / 1024 / 1024 < 1.5
        if (!isLt1M5) {
          showError('<PERSON><PERSON><PERSON> thước hình ảnh vượt quá 1.5MB')
          return Promise.reject()
        }
        const formData = new FormData()
        formData.append('file', file)
        formData.append('upload_preset', uploadPreset)
        const response = await fetch(action, {
          method: 'POST',
          body: formData,
          headers
        })
        if (!response.ok) {
          showError('<PERSON><PERSON><PERSON> lên không thành công')
          return Promise.reject()
        }
        const result = await response.json()
        return { default: result.url }
      } catch (error) {
        showError(error)
        return Promise.reject()
      }
    })
  }

  abort() {
    console.log('Upload aborted')
  }
}

export function UploadAdapterPlugin(uploadProps: any) {
  return function (editor: any) {
    editor.plugins.get('FileRepository').createUploadAdapter = (
      loader: any
    ) => {
      if (!uploadProps) {
        console.log('@@@UploadAdapter: uploadProps is missing')
        return null
      }
      return new UploadAdapter(loader, uploadProps)
    }
  }
}

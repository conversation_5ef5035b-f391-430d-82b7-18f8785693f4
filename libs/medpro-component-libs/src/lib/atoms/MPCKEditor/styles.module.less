.skeletonWrapper {
  :global {
    .ant-skeleton-paragraph {
      li {
        width: 100% !important;
      }
    }
  }
}

.editorWrapper {
  position: relative;

  :global {
    .ck-content {
      min-height: 300px;
      max-height: 480px;
      border: 1px solid #d9d9d9 !important;
      margin-top: 5px;
      margin-bottom: 0.5rem;
    }
  }

  &.editorFullHeight {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 9999;
    top: 0;
    right: 0;
    background: #fff;
    padding-left: 2px;
    overflow: auto;

    :global {
      .ck-content {
        max-height: unset;
        border: none !important;
      }

      .ck.ck-toolbar {
        position: sticky;
        top: 0;
        z-index: 9999;
      }
    }

    .actionFullHeight {
      position: fixed;
      bottom: 15px;
      right: 30px;
      cursor: pointer;
      z-index: 9999;

      svg {
        font-size: 18px;
      }
    }
  }

  .actionFullHeight {
    position: absolute;
    bottom: 10px;
    right: 20px;
    cursor: pointer;
    z-index: 999;

    svg {
      font-size: 18px;
    }
  }
}

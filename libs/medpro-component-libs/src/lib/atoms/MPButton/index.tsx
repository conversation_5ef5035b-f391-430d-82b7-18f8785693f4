import React from 'react'
import { Button, ButtonProps } from 'antd'
import cx from 'classnames'
import styles from './styles.module.less'

export interface MPButtonProps extends ButtonProps {
  className?: any
  children: any
  full?: any
  icon?: any
  typeCustom?:
    | 'delete'
    | 'repair'
    | 'approval'
    | 'cancel'
    | 'edit'
    | 'primary'
    | 'remove'
    | 'upload'
    | 'tab'
}

export function MPButton({
  children,
  className: classStyles,
  icon,
  typeCustom,
  ...props
}: MPButtonProps) {
  return (
    <Button
      type={props?.type}
      htmlType={props?.htmlType}
      className={cx(
        styles['btnAntd'],
        props?.full ? styles['btnFull'] : '',
        classStyles,
        typeCustom ? styles[typeCustom] : ''
      )}
      {...props}
    >
      {icon ? icon : null}
      {children}
    </Button>
  )
}

export default MPButton

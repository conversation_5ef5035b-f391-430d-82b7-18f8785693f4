button.btnAntd {
  padding: 10px 15px;
  font-size: 13px;
  font-weight: 500;
  min-width: 90px;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  svg {
    fill: currentColor;
  }

  &.btnFull {
    width: 100%;
  }

  &.delete {
    background-color: #ffebf1;
    color: #fd397a;
    border: none;

    svg {
      fill: #fd397a;
    }

    &:hover {
      background-color: #fd397a !important;
      color: white !important;

      svg {
        fill: white;
      }
    }
  }

  &.repair {
    background-color: #ebf5ff;
    color: #62afff;
    border: none;

    svg {
      fill: #62afff;
    }

    &:hover {
      background-color: #62afff !important;
      color: white !important;

      svg {
        fill: white;
      }
    }
  }

  &.approval {
    color: #ffffff !important;
    background: linear-gradient(
      86.64deg,
      #06aed4 33.96%,
      #0e82fd 137.73%
    ) !important;
    transition: background-position 0.5s ease-in-out !important;
    background-size: 200% 100% !important;
    border: none;

    &:hover {
      background-position: -100% 0 !important;
    }
  }

  &.cancel {
    color: #000000e0 !important;
    background: linear-gradient(
      86.64deg,
      #e6e8ee 33.96%,
      #cfd2da 137.73%
    ) !important;
    transition: background-position 0.5s ease-in-out !important;
    background-size: 200% 100% !important;
    border: none;

    &:hover {
      background-position: -100% 0 !important;
    }
  }

  &.edit {
    color: #ffffff !important;
    background: linear-gradient(
      86.64deg,
      #fa8c16 33.96%,
      #ff9c6e 137.73%
    ) !important;
    transition: background-position 0.5s ease-in-out !important;
    background-size: 200% 100% !important;
    border: none;

    &:hover {
      background-position: -100% 0 !important;
    }
  }

  &.primary {
    color: #ffffff !important;
    background: linear-gradient(
      86.64deg,
      #007aff 33.96%,
      #3890ef 137.73%
    ) !important;
    transition: background-position 0.5s ease-in-out !important;
    background-size: 200% 100% !important;
    border: none;

    &:hover {
      background-position: -100% 0 !important;
    }
  }

  &.remove {
    color: #ffffff !important;
    background: linear-gradient(
      86.64deg,
      #ff0000 33.96%,
      #fd397a 137.73%
    ) !important;
    transition: background-position 0.5s ease-in-out !important;
    background-size: 200% 100% !important;
    border: none;

    &:hover {
      background-position: -100% 0 !important;
    }
  }

  &.upload {
    color: #ffffff !important;
    background: linear-gradient(
      86.64deg,
      #f07c63 33.96%,
      #fd7d61 137.73%
    ) !important;
    transition: background-position 0.5s ease-in-out !important;
    background-size: 200% 100% !important;
    border: none;

    &:hover {
      background-position: -100% 0 !important;
    }
  }

  &.tab {
    color: #003553 !important;
    background: #ffffff !important;
    transition: background-position 0.5s ease-in-out !important;
    background-size: 200% 100% !important;
    border: 1px solid #d9d9d9;

    &:hover {
      color: #007aff !important;
      background-position: -100% 0 !important;
    }
  }
}

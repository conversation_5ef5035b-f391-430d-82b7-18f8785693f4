import React from 'react'
import styles from './styles.module.less'

export interface LoadingProps {
  status: true | false | 'error'
  description?: string
}
const LoadingCard = (props: LoadingProps) => {
  if (props.status === false) {
    return null
  }
  if (props.status === 'error') {
    return (
      <div className={styles['loading']}>
        <div className={styles['loadingError']}>
          {props?.description ? props?.description : 'Lỗi hệ thống...'}
        </div>
      </div>
    )
  }
  return (
    <div className={styles['loading']}>
      <div>
        <div className={styles['loadingLogo']}>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width={184}
            viewBox='0 0 184 55'
            fill='none'
          >
            <path d='M21.2692 16.5526C23.6028 14.1128 26.5365 12.8719 30.0925 12.8719C33.4263 12.8719 36.2044 13.8184 38.4491 15.7323C39.8493 16.9732 40.8272 18.3614 41.4272 19.9388C42.0051 21.5373 42.4496 23.5985 42.4496 26.1855V41.2868C42.4496 41.4761 42.2718 41.6444 42.0717 41.6444H39.1825C37.3823 41.6444 35.9155 40.2562 35.9155 38.5526V26.6902C35.9155 23.935 35.5821 21.9579 34.8931 20.717C34.0486 19.2237 32.5595 18.4876 30.4481 18.4876C29.3813 18.4876 28.4034 18.7189 27.47 19.1816C26.5588 19.6444 25.8698 20.2543 25.4475 21.0115C24.7363 22.2314 24.3807 24.3136 24.3807 27.2792V41.3078C24.3807 41.4971 24.2029 41.6654 24.0029 41.6654H21.1803C19.4246 41.6654 17.9799 40.3193 17.9799 38.6367V26.6902C17.9799 23.935 17.6466 21.9579 16.9576 20.717C16.1131 19.2237 14.6018 18.4876 12.4904 18.4876C11.4014 18.4876 10.4013 18.7189 9.49004 19.1816C8.57881 19.6444 7.88984 20.2543 7.46757 21.0115C6.75637 22.2524 6.40077 24.3346 6.40077 27.2792V41.3078C6.40077 41.4971 6.22297 41.6654 6.02295 41.6654H3.20039C1.44462 41.6654 0 40.3193 0 38.6367V15.9006C0 14.239 1.42239 12.8719 3.20039 12.8719H6.00072C6.20075 12.8719 6.37855 13.0402 6.37855 13.2294V15.6692C8.26766 13.7973 10.5346 12.8719 13.1571 12.8719C16.2686 12.8719 18.9801 14.0918 21.2692 16.5526ZM65.7635 24.8184C65.2746 23.0937 64.6078 21.7055 63.7188 20.696C62.3853 19.0765 60.5406 18.2772 58.2293 18.2772C56.2512 18.2772 54.6066 18.8451 53.2731 19.9809C51.9396 21.1166 51.0284 22.7361 50.5617 24.8184H65.7635ZM50.7172 29.5927C51.0284 31.0229 51.584 32.2639 52.3619 33.2945C53.8287 35.1874 55.9401 36.1338 58.696 36.1338C60.185 36.1338 61.563 35.8184 62.8298 35.2084C63.2965 34.9771 63.8522 34.5354 64.5189 33.8834C65.7413 32.6635 67.6526 32.327 69.2528 33.0421L71.6975 34.1147C71.8976 34.1989 71.9642 34.4092 71.8753 34.5985C71.8753 34.6195 71.8531 34.6405 71.8531 34.6616C68.2971 39.5201 63.8966 41.8547 58.6738 41.6444C54.3177 41.4761 50.7395 40.1511 47.9836 37.1434C45.4499 34.3671 44.1831 31.065 44.1831 27.2371C44.1831 22.7782 45.8055 19.1396 49.0504 16.3002C51.6507 14.0076 54.7177 12.8719 58.2737 12.8719C62.2964 12.8719 65.6746 14.3021 68.3416 17.1625C69.7418 18.6558 70.7641 20.4857 71.4086 22.673C71.8976 24.3346 72.1643 26.0803 72.1643 27.8681V28.5201C72.1643 28.7935 72.1643 29.1511 72.142 29.5927H50.7172ZM89.0107 18.2562C86.6105 18.2562 84.6102 19.0975 82.9878 20.8011C81.4765 22.3786 80.7209 24.4818 80.7209 27.1109C80.7209 30.1185 81.6765 32.4321 83.5879 34.0516C85.1658 35.3767 87.0105 36.0287 89.1441 36.0287C91.5666 36.0287 93.5669 35.2084 95.1226 33.5468C96.6339 31.9694 97.3895 29.8031 97.3895 27.0478C97.3895 24.0822 96.4339 21.7686 94.5447 20.1702C93.1001 18.8872 91.2555 18.2562 89.0107 18.2562ZM103.546 41.6654H100.568C98.7008 41.6654 97.1895 40.2352 97.1895 38.4684C94.3003 40.5927 91.3888 41.6654 88.4551 41.6654C86.0993 41.6654 83.8323 41.0554 81.6321 39.8356C79.2762 38.5315 77.4538 36.7859 76.1425 34.5774C74.7868 32.327 74.1201 29.8662 74.1201 27.2161C74.1201 22.8623 75.7647 19.2658 79.0762 16.4264C81.8765 14.0287 84.988 12.8298 88.4551 12.8298C91.3666 12.8298 94.278 13.9025 97.1895 16.0268V6.0153C97.1895 2.69216 100.034 0 103.546 0V41.6654ZM120.392 18.2562C117.992 18.2562 116.014 19.0765 114.414 20.738C112.903 22.3155 112.147 24.4608 112.147 27.2161C112.147 30.2027 113.103 32.5163 114.992 34.1358C116.459 35.4187 118.303 36.0497 120.481 36.0497C121.993 36.0497 123.437 35.6711 124.793 34.914C127.504 33.3365 128.838 30.7706 128.838 27.195C128.838 24.1874 127.882 21.8738 125.971 20.2543C124.393 18.9082 122.526 18.2562 120.392 18.2562ZM112.125 12.6405V15.8375C115.014 13.6922 117.925 12.6405 120.859 12.6405C123.215 12.6405 125.482 13.2505 127.682 14.4704C130.038 15.7744 131.86 17.5201 133.172 19.7495C134.527 22 135.194 24.4608 135.194 27.1109C135.194 31.4857 133.549 35.0822 130.238 37.9216C127.438 40.3193 124.304 41.5182 120.859 41.5182C117.948 41.5182 115.036 40.4455 112.125 38.3212V48.9847C112.125 52.3078 109.28 55 105.768 55V18.6558C105.746 15.3327 108.591 12.6405 112.125 12.6405ZM140.573 13.6291L143.351 13.587C143.551 13.587 143.728 13.7342 143.751 13.9446V15.8795C144.995 14.912 145.995 14.3021 146.751 14.0287C147.507 13.7342 148.529 13.587 149.818 13.587C150.129 13.587 151.663 13.6711 152.152 13.7132V17.9407C152.152 18.6138 151.574 19.1606 150.863 19.1606C150.84 19.1606 150.84 19.1606 150.818 19.1606C150.463 19.1606 150.151 19.1396 149.929 19.1396C147.396 19.1396 145.662 19.9388 144.795 21.5583C144.084 22.8413 143.751 24.9235 143.751 27.826V41.3078C143.751 41.4971 143.573 41.6654 143.373 41.6654H140.595C138.839 41.6654 137.439 40.3193 137.439 38.6788V16.6788C137.394 15.0172 138.817 13.6711 140.573 13.6291ZM169.043 18.2562C166.642 18.2562 164.62 19.1185 162.953 20.8222C161.464 22.3786 160.708 24.4818 160.708 27.1109C160.708 30.0765 161.664 32.4111 163.575 34.0516C165.131 35.3767 166.998 36.0287 169.154 36.0287C171.687 36.0287 173.777 35.1663 175.399 33.4627C176.888 31.8853 177.644 29.74 177.644 27.0478C177.644 24.1243 176.688 21.8738 174.799 20.2543C173.265 18.9293 171.354 18.2562 169.043 18.2562ZM169.287 12.6405C173.621 12.6405 177.244 14.1969 180.155 17.2887C182.733 20.0229 184 23.2409 184 26.9847C184 31.4015 182.333 35.0191 179.022 37.8585C176.243 40.2352 172.91 41.434 169.065 41.434C164.598 41.434 160.886 39.8987 157.975 36.8069C155.374 34.0937 154.085 30.8547 154.085 27.0478C154.085 23.3671 155.285 20.2122 157.663 17.5832C160.664 14.2811 164.553 12.6405 169.287 12.6405Z' />
          </svg>
        </div>

        <div className={styles['loadingTitle']}>
          {props?.description ? props?.description : 'Vui lòng đợi...'}
        </div>
      </div>
    </div>
  )
}

export default LoadingCard

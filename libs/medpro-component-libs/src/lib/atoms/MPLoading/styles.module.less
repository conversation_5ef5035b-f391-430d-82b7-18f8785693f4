.loading {
  width: 100%;
  height: calc(100vh - 23em);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  .loadingLogo path {
    stroke-dasharray: 1000;
    stroke: #00b5f1;
    opacity: 1;
    animation: anmtLoadingLogo 1.2s cubic-bezier(0.54, 0.02, 1, 0.98) infinite;
    stroke-width: 1;
    @keyframes anmtLoadingLogo {
      0% {
        opacity: 0;
        fill: none;
        stroke-dashoffset: 1000;
      }
      5% {
        opacity: 1;
        fill: none;
        stroke-dashoffset: 1000;
      }
      60% {
        fill: #00b5f1;
      }
      95%,
      100% {
        opacity: 1;
        fill: #00b5f1;
        stroke-dashoffset: 0;
      }
    }
  }
  .loadingTitle {
    width: 100%;
    text-align: center;
    font-size: 17px;
    color: #00b5f1;
  }
  .loadingError {
    text-align: center;
    font-size: 20px;
    color: rgb(248, 17, 17);
  }
}

import { createJSONEditor } from 'vanilla-jsoneditor'
import { useEffect, useRef } from 'react'

export default function MPJSONEditor(props: any) {
  const refContainer = useRef<HTMLDivElement | null>(null)
  const refEditor = useRef<any>(null)

  useEffect(() => {
    if (!refContainer.current) return

    refEditor.current = createJSONEditor({
      target: refContainer.current,
      props: {}
    })

    return () => {
      if (refEditor.current) {
        refEditor.current.destroy()
        refEditor.current = null
      }
    }
  }, [])

  useEffect(() => {
    if (refEditor.current) {
      refEditor.current.updateProps(props)
    }
  }, [props])

  return <div ref={refContainer} style={{ height: '500px' }} />
}

import React, { useEffect, useState } from 'react'
import { Button, Upload, UploadProps, message } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import { checkImageType } from '../../helpers/func'

export interface MPImageFormItemSingleProps extends UploadProps {
  responseValuePropName?: string
  url?: string
  disable?: boolean
  typeUpload?: string
}

const MPImageFormItemSingle = (props: MPImageFormItemSingleProps) => {
  const [fileList, setFileList] = useState([]) as any[]
  const { url, responseValuePropName = 'url', onChange, ...rest } = props

  useEffect(() => {
    if (url) {
      const files = [{ uid: 'uuid', status: 'done', url: url }]
      setFileList(files)
    }
  }, [url])

  const onValueChange = (event: any) => {
    if (event.file.status === 'uploading') {
      setFileList(event.fileList)
    }
    if (event.file.status === 'done' || event.file.status === 'removed') {
      const { file, fileList = [] } = event
      const fileUrl = file?.response?.[responseValuePropName]
      if (typeof fileUrl !== 'string' && fileUrl) {
        console.error('ERROR: Invalid file URL', fileUrl)
        return
      }
      setFileList(event.fileList)
      onChange?.(fileUrl as any)
      setFileList(fileList)
    }
  }

  const beforeUpload = (file: any) => {
    const isLt1M5 = file.size / 1024 / 1024 < 1.5
    if (!isLt1M5) {
      message.error('Kích thước hình ảnh vượt quá 1.5MB !')
      return false
    }
    if (!checkImageType(file.type)) {
      message.error(`${file.name} không phải là hình ảnh`)
      return false
    }
    return true
  }

  return (
    <Upload
      {...rest}
      fileList={fileList}
      onChange={onValueChange}
      listType={'picture'}
      disabled={props.disable}
      beforeUpload={beforeUpload}
      accept='image/*'
    >
      <Button icon={<UploadOutlined />} disabled={props.disable}>
        Tải hình lên
      </Button>
    </Upload>
  )
}

export default MPImageFormItemSingle

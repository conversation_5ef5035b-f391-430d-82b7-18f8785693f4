import React, { useEffect, useState } from 'react'
import { Upload, UploadProps, message } from 'antd'
import { size } from 'lodash'
import ImgCrop from 'antd-img-crop'
import { checkImageType } from '../../helpers/func'

export interface MPImageFormItemProps extends UploadProps {
  url: string
  responseValuePropName?: string
  onChange: any
  length?: number
  multiple?: boolean
  maxCount: number
  aspect?: '16:9' | string
  cropImage?: boolean
  customBody?: any
}

export function MPImageFormItemMultiple (props: MPImageFormItemProps) {
  const {
    url,
    responseValuePropName = 'url',
    onChange,
    length = 1,
    multiple,
    maxCount,
    aspect = '4:3',
    cropImage = true,
    customBody,
    ...rest
  } = props
  const BodyBase = customBody || <div>+ Thêm hình</div>
  const [fileList, setFileList] = useState<any[]>([])

  useEffect(() => {
    if (!url || !url.length) {
      setFileList([])
      return
    }
    if (fileList.length > 0) {
      const urls = Array.isArray(url) ? url : [url]
      const isUrlInFileList = urls.every((u: any) =>
        fileList.some((file: any) => file.response?.url === u || file.url === u)
      )
      if (isUrlInFileList) {
        return
      }
    }
    const toFileObject = (u: string, i: number | string): any => ({
      uid: String(i),
      name: `image-${i}`,
      status: 'done',
      url: u,
      response: { url: u }
    })
    const files = Array.isArray(url)
      ? url.map(toFileObject)
      : [toFileObject(url, 'uuid')]
    setFileList(files)
  }, [url])

  const onValueChange = (event: any) => {
    if (event.file.status === 'uploading') {
      setFileList(event.fileList)
    }
    if (event.file.status === 'done' || event.file.status === 'removed') {
      const { file, fileList = [] } = event
      if (file?.response?.[responseValuePropName]) {
        setFileList(event.fileList)

        const newFileList = fileList.map((file: any) => file.response['url'])
        onChange({
          target: {
            url: newFileList
          }
        })
      }
      setFileList(multiple ? fileList : fileList.slice(0 - length))
    }
  }

  const onResizeAspect = (aspect: string) => {
    //1:1
    const [width, height] = aspect.split(':')
    return {
      width_aspect: width,
      height_aspect: height
    }
  }

  const beforeUpload = (file: any) => {
    const isLt1M5 = file.size / 1024 / 1024 < 1.5
    if (!isLt1M5) {
      void message.error('Kích thước hình ảnh vượt quá 1.5MB !')
      return false
    }
    if (!checkImageType(file.type)) {
      void message.error(`${file.name} không phải là hình ảnh`)
      return false
    }
    return true
  }

  const { width_aspect, height_aspect } = onResizeAspect(aspect)

  return !cropImage ? (
    <Upload
      fileList={fileList}
      onChange={onValueChange}
      multiple={multiple}
      listType='picture-card'
      maxCount={maxCount}
      showUploadList={{
        showPreviewIcon: true
      }}
      beforeUpload={beforeUpload}
      accept='image/*'
      {...rest}
    >
      {maxCount <= size(fileList) ? null : BodyBase}
    </Upload>
  ) : (
    <ImgCrop
      modalTitle='Điều chỉnh kích thước'
      showReset
      aspect={Number(width_aspect) / Number(height_aspect)}
      quality={0.85}
      aspectSlider
    >
      <Upload
        fileList={fileList}
        onChange={onValueChange}
        multiple={multiple}
        listType='picture-card'
        maxCount={maxCount}
        showUploadList={{
          showPreviewIcon: true
        }}
        beforeUpload={beforeUpload}
        accept='image/*'
        {...rest}
      >
        {maxCount <= size(fileList) ? '' : <div>+ Thêm hình</div>}
      </Upload>
    </ImgCrop>
  )
}

export default MPImageFormItemMultiple

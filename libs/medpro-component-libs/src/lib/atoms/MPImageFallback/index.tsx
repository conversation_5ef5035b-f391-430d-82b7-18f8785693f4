import Image from 'next/image'
import { useEffect, useState } from 'react'
import { ImageProps } from 'next/image'

interface MPImageFallbackProps extends Omit<ImageProps, 'src'> {
  src: string
  fallbackSrc: string
}

/**
 * Component ImageFallback hiển thị hình ảnh với cơ chế dự phòng (fallback).
 *
 * Nếu hình ảnh chính không thể tải (lỗi hoặc width bằng 0), component sẽ tự động hiển thị hình ảnh thay thế.
 *
 * @typedef {Object} ImageFallbackProps
 * @property {string} src - Đường dẫn ảnh chính.
 * @property {string} fallbackSrc - Đường dẫn ảnh thay thế nếu ảnh chính lỗi.
 * @property {string} [alt] - <PERSON><PERSON><PERSON> bản thay thế cho ảnh (alt), mặc định là 'Image' nếu không truyền.
 */
export function MPImageFallback({
  src,
  fallbackSrc,
  ...rest
}: MPImageFallbackProps) {
  const [imgSrc, set_imgSrc] = useState(src)

  useEffect(() => {
    set_imgSrc(src)
  }, [src])

  return (
    <Image
      {...rest}
      src={imgSrc}
      alt={rest.alt || 'Image'}
      onLoadingComplete={(result) => {
        if (result.naturalWidth === 0) {
          // Broken image
          set_imgSrc(fallbackSrc)
        }
      }}
      onError={() => {
        set_imgSrc(fallbackSrc)
      }}
    />
  )
}

export default MPImageFallback

import { size } from 'lodash'
import moment from 'moment'
import validator from 'validator'

class Valid {
  required = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập thông tin!'))
  }
  trimRequired = (_: any, value: any) => {
    if (value && value.trim()) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập thông tin!'))
  }
  numberRequired = (_: any, value: any) => {
    if (value || value === 0) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập thông tin!'))
  }
  requiredArray = (_: any, value: any) => {
    if (value && size(value) > 0) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập thông tin!'))
  }
  patientCode = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('<PERSON>ui lòng nhập mã bệnh nhân!'))
  }
  paymentCode = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập mã thanh toán!'))
  }
  name = (_: any, value: any) => {
    if (value) {
      const getFullname = value.split(' ').filter((item: any) => item !== '')

      if (!getFullname.find((item: any) => item.length > 1)) {
        return Promise.reject(new Error('Phải bao gồm họ và tên!'))
      }
      if (size(getFullname) < 2) {
        return Promise.reject(new Error('Phải bao gồm họ và tên!'))
      }
      if (size(value) > 150) {
        return Promise.reject(new Error('Họ và tên quá dài!'))
      }
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập họ và tên!'))
  }
  mobile = (_: any, value: any) => {
    if (value) {
      if (!validator.isMobilePhone(value, 'vi-VN')) {
        return Promise.reject(new Error('Số điện thoại không đúng!'))
      } else {
        return Promise.resolve()
      }
    }
    return Promise.reject(new Error('Vui lòng nhập số điện thoại!'))
  }
  mobileNoRequired = (_: any, value: any) => {
    if (value) {
      if (!validator.isMobilePhone(value, 'vi-VN')) {
        return Promise.reject(new Error('Số điện thoại không đúng!'))
      } else {
        return Promise.resolve()
      }
    }
    return Promise.resolve()
  }
  cmnd = (_: any, value: any) => {
    if (value) {
      if (value.length < 20) {
        return Promise.resolve()
      } else {
        return Promise.reject(
          new Error('Vui lòng nhập đúng số Mã định danh/CCCD/Passport !')
        )
      }
    }
    return Promise.resolve()
  }
  cccd = (_: any, value: any) => {
    if (value) {
      if (value.length < 20) {
        return Promise.resolve()
      } else {
        return Promise.reject(
          new Error('Vui lòng nhập đúng Mã định danh/CCCD/Passport !')
        )
      }
    }
    return Promise.reject(
      new Error('Vui lòng nhập Mã định danh/CCCD/Passport !')
    )
  }
  mobileCAM = (_: any, value: string) => {
    if (value) {
      if (value.startsWith('855')) {
        const sub = value.substring(3).replace(/^0/, '')
        if (sub.length === 9 && validator.isNumeric(sub)) {
          return Promise.resolve()
        } else {
          return Promise.reject('Số điện thoại CAM không đúng!')
        }
      } else {
        const sub = value.substring(2).replace(/^0/, '')
        if (!validator.isMobilePhone('0' + sub, 'vi-VN')) {
          return Promise.reject(new Error('Số điện thoại không đúng!'))
        } else {
          return Promise.resolve()
        }
      }
    }
    return Promise.reject(new Error('Vui lòng nhập số điện thoại!'))
  }
  question = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng chọn vấn đề của bạn!'))
  }
  day = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Chọn ngày sinh!'))
  }
  month = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Chọn tháng sinh!'))
  }
  year = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Chọn năm sinh!'))
  }
  sex = (_: any, value: any) => {
    if (value === 0 || value === 1) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng chọn giới tính!'))
  }
  province = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng chọn tỉnh thành!'))
  }
  district = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng chọn quận huyện!'))
  }
  ward = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng chọn phường xã!'))
  }
  address = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập địa chỉ!'))
  }
  profession = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng chọn Nghề nghiệp!'))
  }
  country = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng chọn Quốc gia!'))
  }
  inputText = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập nội dung cần trợ giúp!'))
  }
  codeBHYT = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập mã BHYT!'))
  }
  birthdate = (_: any, value: any) => {
    if (value) {
      if (value === '__/__/20__') {
        return Promise.reject(new Error('Vui lòng nhập ngày/tháng/năm!'))
      }

      const yearOfValue = value.split('/')?.[2]

      const separation = value.split('/').length - 1 //số lượng dấu xoẹt 19/07/2021 length = 2

      const date = moment(value, 'DD/MM/YYYY', true)

      if (!date.isValid()) {
        return Promise.reject(
          new Error(
            'Vui lòng nhập đúng định dạng ngày tháng! Ví dụ: 19/07/2021'
          )
        )
      } else if (separation !== 2) {
        return Promise.reject(
          new Error('Vui lòng nhập đúng định dạng ! Ví dụ: 19/07/2021')
        )
      } else if (size(yearOfValue) < 4 || size(yearOfValue) > 4) {
        return Promise.reject(new Error('Năm sinh không hợp lệ ! '))
      } else {
        const difAge = moment().diff(date, 'years', true)
        if (difAge > 120 || difAge < 0) {
          return Promise.reject(new Error('Vui lòng nhập đúng năm sinh !'))
        }

        if (moment().isBefore(date)) {
          return Promise.reject(new Error('Ngày sinh lớn hơn ngày hiện tại!'))
        }
        if (!date.isValid()) {
          return Promise.reject(
            new Error(
              'Vui lòng nhập đúng định dạng ngày tháng! Ví dụ: 19/07/2021'
            )
          )
        }
      }
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập ngày/tháng/năm!'))
  }
  birthdateNoRequired = (_: any, value: any) => {
    if (value) {
      if (value === '__/__/20__') {
        return Promise.reject(new Error('Vui lòng nhập ngày/tháng/năm!'))
      }

      const yearOfValue = value.split('/')?.[2]

      const separation = value.split('/').length - 1 //số lượng dấu xoẹt 19/07/2021 length = 2

      const date = moment(value, 'DD/MM/YYYY', true)

      if (!date.isValid()) {
        return Promise.reject(
          new Error(
            'Vui lòng nhập đúng định dạng ngày tháng! Ví dụ: 19/07/2021'
          )
        )
      } else if (separation !== 2) {
        return Promise.reject(
          new Error('Vui lòng nhập đúng định dạng ! Ví dụ: 19/07/2021')
        )
      } else if (size(yearOfValue) < 4 || size(yearOfValue) > 4) {
        return Promise.reject(new Error('Năm sinh không hợp lệ ! '))
      } else {
        const difAge = moment().diff(date, 'years', true)
        if (difAge > 120 || difAge < 0) {
          return Promise.reject(new Error('Vui lòng nhập đúng năm sinh !'))
        }

        if (moment().isBefore(date)) {
          return Promise.reject(new Error('Ngày sinh lớn hơn ngày hiện tại!'))
        }
        if (!date.isValid()) {
          return Promise.reject(
            new Error(
              'Vui lòng nhập đúng định dạng ngày tháng! Ví dụ: 19/07/2021'
            )
          )
        }
      }
      return Promise.resolve()
    }
    return Promise.resolve()
  }
  email = (_: any, value: any) => {
    if (value) {
      if (!validator.isEmail(value) && value) {
        return Promise.reject(new Error('Email không đúng định dạng!'))
      } else {
        return Promise.resolve()
      }
    }
    return Promise.reject(new Error('Vui lòng nhập email!'))
  }
  emailNoRequired = (_: any, value: any) => {
    if (value) {
      if (!validator.isEmail(value) && value) {
        return Promise.reject(new Error('Email không đúng định dạng!'))
      } else {
        return Promise.resolve()
      }
    }
    return Promise.resolve()
  }
  quill = (_: any, value: any) => {
    if (!value) {
      return Promise.reject(new Error('Vui lòng nhập thông tin!'))
    } else {
      if (value === '<p><br></p>') {
        return Promise.reject(new Error('Vui lòng nhập thông tin!'))
      } else {
        return Promise.resolve()
      }
    }
  }
  password = (_: any, value: any) => {
    if (!value) {
      return Promise.reject(new Error('Vui lòng nhập mật khẩu!'))
    } else {
      if (size(value) < 6) {
        return Promise.reject(new Error('Mật khẩu quá ngắn!'))
      }
    }
    return Promise.resolve()
  }
  validateDegree = (_: any, value: any) => {
    if (value) {
      const str = value.replace(' ', '')
      const patternDegree = /^[a-z0-9A-Z ]{4,}$/g
      if (!patternDegree.test(str)) {
        return Promise.reject(new Error('Vui lòng nhập đúng học hàm/học vị!'))
      } else {
        return Promise.resolve()
      }
    } else {
      return Promise.reject(new Error('Vui lòng nhập học hàm/học vị!'))
    }
  }
  validatorWorkPlace = (_: any, value: any) => {
    if (value) {
      const str = value.replace(' ', '')
      const patternWorkPlace = /^[a-z0-9A-Z ]{4,}$/g
      if (!patternWorkPlace.test(str)) {
        return Promise.reject(new Error('Vui lòng nhập đúng nơi công tác!'))
      } else {
        return Promise.resolve()
      }
    } else {
      return Promise.reject(new Error('Vui lòng nhập đúng nơi công tác!'))
    }
  }
  validatorHospitalName = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    } else {
      return Promise.reject(new Error('Vui lòng nhập tên cơ sở y tế!'))
    }
  }
  validatorChuyenKhoa = (_: any, value: any) => {
    if (value) {
      const str = value.replace(' ', '')
      const patternChuyenKhoa = /^[a-z0-9A-Z ]{4,}$/g
      if (!patternChuyenKhoa.test(str)) {
        return Promise.reject(new Error('Vui lòng nhập đúng chuyên khoa!'))
      } else {
        return Promise.resolve()
      }
    } else {
      return Promise.reject(new Error('Vui lòng nhập chuyên khoa!'))
    }
  }
  validatorClinicERC = (_: any, value: any) => {
    if (!value) {
      return Promise.reject(new Error('Vui lòng nhập giấy phép kinh doanh!'))
    } else {
      return Promise.resolve()
    }
  }
  clinicEmail = (_: any, value: any) => {
    if (value) {
      if (!validator.isEmail(value) && value) {
        return Promise.reject(new Error('Email không đúng định dạng!'))
      } else {
        return Promise.resolve()
      }
    } else {
      return Promise.reject(new Error('Vui lòng nhập email!'))
    }
  }
  date = (_: any, value: any, options: any = {}) => {
    const {
      format = 'DD/MM/YYYY',
      message = 'Ngày tháng không hợp lệ. Vui lòng kiểm tra lại!'
    } = options
    if (value) {
      const dateMM = moment(value, format, true)
      if (!dateMM.isValid() || dateMM.isSameOrBefore(moment())) {
        return Promise.reject(new Error(message))
      }
    }
    return Promise.resolve()
  }
  validatorImage = (_: any, value: any) => {
    if (!value) {
      return Promise.reject(new Error('Vui lòng bổ sung giấy phép !'))
    } else {
      return Promise.resolve()
    }
  }
  validatorTaxCode = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    } else {
      return Promise.reject(new Error('Vui lòng nhập mã số thuế!'))
    }
  }
  validatorInitName = (_: any, value: any) => {
    if (value) {
      return Promise.resolve()
    } else {
      return Promise.reject(new Error('Vui lòng nhập tên đơn vị!'))
    }
  }
  requiredEditor = (_: any, value: any) => {
    const regex = /(<([^>]+)>)/gi
    const hasText = !!value?.replace(regex, '').length
    if (hasText) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('Vui lòng nhập thông tin!'))
  }
}

export { Valid }

import { get } from 'lodash'

export const DEFAULT_ERROR_MESSAGE = 'Có lỗi xảy ra, vui lòng thử lại sau!'

export const getError = (err: any, detail: any = {}) => {
  if (err.isAxiosError) {
    const { status } = err.response || {}

    let message
    if (status >= 500) {
      message = DEFAULT_ERROR_MESSAGE
    } else {
      message =
        err.response?.data?.message ||
        err?.message ||
        err ||
        DEFAULT_ERROR_MESSAGE
    }

    if (Array.isArray(message)) {
      message = DEFAULT_ERROR_MESSAGE
    }

    return {
      message,
      detail: {
        ...detail,
        config: JSON.stringify(get(err, 'config'))
      }
    }
  } else {
    return {
      message:
        err?.data?.message || err?.message || err || DEFAULT_ERROR_MESSAGE,
      detail
    }
  }
}

import { get } from 'lodash'
import { kyTuDacBiet } from './constants'

export const checkImageType = (value: string) => {
  // Đ<PERSON>nh nghĩa mẫu regex cho image/*
  const pattern = /^image\/.*$/
  // Kiểm tra xem giá trị có khớp với mẫu không
  return pattern.test(value)
}

export const getReplaceUTF8 = (str = '') => {
  if (!str) {
    return str
  }
  str = str.replace(/[ăâáàảạãắẳẵằặấầẩẫậ]/gi, 'a')
  str = str.replace(/[éèẻẽẹêếềểễệ]/gi, 'e')
  str = str.replace(/đ/gi, 'd')
  str = str.replace(/[íìỉĩị]/gi, 'i')
  str = str.replace(/[óòỏõọôốồổỗộơớờởỡợ]/gi, 'o')
  str = str.replace(/[úùủũụưứừữửự]/gi, 'u')
  str = str.replace(/[ýỳỷỹỵ]/gi, 'y')
  return str
}

export const handlePermissionTree = (data?: any) => {
  return data?.map((item: any) => {
    return {
      ...item,
      permission: item?.permission?.map((e: any) => {
        return { ...e, _id: `${item?._id}-${e?._id}` }
      })
    }
  })
}

export const removePermissionCheckedResult = (checked: any[], target: any) => {
  let result: any = target
  const removePermissionId = checked?.filter((item: string) =>
    item.includes('-')
  )
  removePermissionId.forEach(function (item) {
    result = result?.map((e: any) => {
      return {
        ...e,
        permission: e.permission?.filter((p: any) => p._id !== item)
      }
    })
  })
  result = result.filter((e: any) => !!e.permission?.length)
  return result
}

export const filterPermissionLeftData = (
  permission?: any[],
  allPermission?: any[]
) => {
  const idPermission = permission?.map((item: any) => {
    return item.permission.map((p: any) => p._id)
  })
  const idModule = permission?.map((item: any) => item._id)
  const mergedArray = [...(idModule || []), ...(idPermission || [])].flat()
  return removePermissionCheckedResult([...mergedArray], allPermission)
}

export const filterPermissionTree = (
  keys: any,
  halfKeys: any,
  rootNode: any
) => {
  return rootNode
    ? rootNode
        .filter(
          (node: any) => halfKeys.includes(node._id) || keys.includes(node._id)
        )
        .map((nodeRoot: any) => ({
          ...nodeRoot,
          permission: filterPermissionTree(keys, halfKeys, nodeRoot.permission)
        }))
    : []
}

export const mergePermissionCheckedResult = (array: any[]) => {
  const output: any = []
  array.forEach(function (item) {
    const existing = output.filter((v: any, i: number) => {
      return v._id === item._id
    })
    if (existing.length) {
      const existingIndex = output.indexOf(existing[0])
      output[existingIndex].permission = output[
        existingIndex
      ].permission.concat(item.permission)
    } else {
      output.push(item)
    }
  })
  const keys = ['_id', 'name']
  const result = output.map((item: any) => {
    return {
      ...item,
      permission: item.permission.filter(
        (value: any, index: number, self: any) =>
          self.findIndex((v: any) => keys.every((k) => v[k] === value[k])) ===
          index
      )
    }
  })
  return result
}

export const handleFilterByKeyword = (value: any, data: any, key: any) => {
  return data.filter((item: any) => {
    const regex = new RegExp(getReplaceUTF8(value), 'i')
    return getReplaceUTF8(get(item, key)).match(regex)
  })
}

export const handleName = (evt: any) => {
  // const regex = new RegExp('^[a-zA-Z0-9]+$')

  const key = String.fromCharCode(!evt.charCode ? evt.which : evt.charCode)
  if (kyTuDacBiet.test(key)) {
    evt.preventDefault()
    return false
  }
  return true
}

export const checkJson = (values: any) => {
  try {
    JSON.parse(values)
    return true
  } catch (error) {
    return false
  }
}

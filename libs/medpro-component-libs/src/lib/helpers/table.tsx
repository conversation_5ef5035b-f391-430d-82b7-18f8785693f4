import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'antd'
import { ColumnType } from 'antd/es/table'
import { AiFillDelete, AiFillEdit, AiFillEye } from 'react-icons/ai'
import React from 'react'

export type ExtendedColumnType<T> = ColumnType<T> & {
  actionType?: 'actionButton' | 'actionText' | undefined
  actionDisplay?: ('view' | 'edit' | 'delete')[]
}

const actionItems = [
  {
    key: 'view',
    label: 'Chi tiết',
    icon: <AiFillEye />,
    classBtn: 'btnView btn',
    classText: 'textView actionText',
    color: '#f1c40f',
    handler: (row: any, onPressView?: any) => onPressView?.(row)
  },
  {
    key: 'edit',
    label: 'Chỉnh sửa',
    icon: <AiFillEdit />,
    classBtn: 'btnEdit btn',
    classText: 'textEdit actionText',
    color: '#40a9ff',
    handler: (row: any, onPressEdit?: any) => onPressEdit?.(row)
  },
  {
    key: 'delete',
    label: 'Xóa',
    icon: <AiFillDelete />,
    classBtn: 'btnDelete btn',
    classText: 'textDelete actionText',
    color: '#fd766a',
    handler: (row: any, onPressDelete?: any) => onPressDelete?.(row)
  }
]

function actionColumn(
  type: 'actionButton' | 'actionText' | undefined,
  display: string[] = [],
  row: any,
  handlers: {
    onPressView?: any
    onPressEdit?: any
    onPressDelete?: any
  }
) {
  const { onPressView, onPressEdit, onPressDelete } = handlers

  return type === 'actionButton' ? (
    <div className='groupAction'>
      {actionItems
        .filter((item) => display.includes(item.key))
        .map((item) => (
          <Tooltip
            key={item.key}
            title={item.label}
            placement='bottom'
            color={item.color}
          >
            <Button
              onClick={() =>
                item.handler(
                  row,
                  {
                    view: onPressView,
                    edit: onPressEdit,
                    delete: onPressDelete
                  }[item.key]
                )
              }
              className={item.classBtn}
            >
              {item.icon}
            </Button>
          </Tooltip>
        ))}
    </div>
  ) : (
    <div className='groupAction groupActionText'>
      {actionItems
        .filter((item) => display.includes(item.key))
        .map((item) => (
          <div
            key={item.key}
            onClick={() =>
              item.handler(
                row,
                {
                  view: onPressView,
                  edit: onPressEdit,
                  delete: onPressDelete
                }[item.key]
              )
            }
            className={item.classText}
          >
            {item.label}
          </div>
        ))}
    </div>
  )
}

export function normalizeTable<T>(
  columns: ExtendedColumnType<T>[],
  onPressEdit?: (row: any) => void,
  onPressDelete?: (row: any) => void,
  onPressView?: (row: any) => void
): ColumnType<T>[] {
  return columns.map((col) => {
    return {
      ...col,
      title:
        typeof col.title === 'string' ? (
          <Tooltip title={col.title}>
            <span>{col.title}</span>
          </Tooltip>
        ) : (
          col.title
        ),
      render:
        col?.actionType === 'actionButton' || col?.actionType === 'actionText'
          ? (row: any) =>
              actionColumn(col?.actionType, col.actionDisplay, row, {
                onPressView,
                onPressEdit,
                onPressDelete
              })
          : col.render
    }
  })
}

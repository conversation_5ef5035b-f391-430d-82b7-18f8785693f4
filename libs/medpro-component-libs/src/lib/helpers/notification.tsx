import { notification } from 'antd'
import { ArgsProps } from 'antd/lib/notification'
import { getError } from './error'

type NotificationType = 'success' | 'info' | 'warning' | 'error'

export const openNotification = (type: NotificationType, args: ArgsProps) => {
  notification[type]({
    ...args
  })
}

export const showMessage = (
  message: string,
  type: NotificationType = 'success'
) => {
  openNotification(type, { message })
}

export const showError = (err: any) => {
  const parsedError = getError(err)
  openNotification('error', { message: parsedError.message })
}

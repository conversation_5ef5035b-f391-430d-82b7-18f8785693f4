import { TablePaginationConfig } from 'antd'
import MPBrandPromotionComponent from '../../components/MPBrandPromotionComponent'
import MPBrandPromotionCard from '../../ui/MPBrandPromotionCard'

export interface BrandPromotionProps {
  onDragSortTable?: any
  heightContent: number
  hospitalListTab: any
  packageList: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  // TTHT và đồng hành
  dataPartner: any
  actionCompanionship: any
  // Cơ sở y tế yêu thích
  dataCSYTFavourite: any
  actionCSYTFavourite: any
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  onChangeTabEvent: (key: any, level: any) => void
  baseValueDoctorTelemed: any
  loadingDoctorTelemed: boolean
  methodsDoctorTelemed: any
  setTabActiveKey: any
  tabActiveKey: any
}

export const MPBrandPromotion = ({
  onDragSortTable,
  heightContent,
  hospitalListTab,
  packageList,
  loading,
  onPressViewDetail,
  // TTHT và đồng hành
  dataPartner,
  actionCompanionship,
  // Cơ sở y tế yêu thích
  dataCSYTFavourite,
  actionCSYTFavourite,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onChangeTabEvent,
  baseValueDoctorTelemed,
  loadingDoctorTelemed,
  methodsDoctorTelemed,
  setTabActiveKey,
  tabActiveKey
}: BrandPromotionProps) => {
  return (
    <MPBrandPromotionComponent
      renderItem={() => {
        return (
          <MPBrandPromotionCard
            onDragSortTable={onDragSortTable}
            heightContent={heightContent}
            hospitalListTab={hospitalListTab}
            packageList={packageList}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            dataPartner={dataPartner}
            actionCompanionship={actionCompanionship}
            dataCSYTFavourite={dataCSYTFavourite}
            actionCSYTFavourite={actionCSYTFavourite}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onChangeTabEvent={onChangeTabEvent}
            baseValueDoctorTelemed={baseValueDoctorTelemed}
            loadingDoctorTelemed={loadingDoctorTelemed}
            methodsDoctorTelemed={methodsDoctorTelemed}
            setTabActiveKey={setTabActiveKey}
            tabActiveKey={tabActiveKey}
          />
        )
      }}
    />
  )
}

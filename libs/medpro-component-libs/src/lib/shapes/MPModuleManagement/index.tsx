import React from 'react'
import { TablePaginationConfig } from 'antd'
import MPModuleManagementCard from '../../ui/MPModuleManagementCard'
import MPModuleManagementComponent from '../../components/MPModuleManagementComponent'

export interface ModuleManagementProps {
  heightContent: number
  moduleList: any
  permissionList: any
  moduleDetail: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitTransferModule: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

export const MPModuleManagement = ({
  heightContent,
  moduleList,
  permissionList,
  loading,
  moduleDetail,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onSubmitTransferModule,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: ModuleManagementProps) => {
  return (
    <MPModuleManagementComponent
      renderItem={() => {
        return (
          <MPModuleManagementCard
            heightContent={heightContent}
            moduleList={moduleList}
            permissionList={permissionList}
            loading={loading}
            moduleDetail={moduleDetail}
            onPressViewDetail={onPressViewDetail}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onSubmitTransferModule={onSubmitTransferModule}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      }}
    />
  )
}

import React from 'react'
import { TablePaginationConfig } from 'antd'
import MPHomeBannerComponent from '../../components/MPHomeBannerComponent'
import MPHomeBannerCard from '../../ui/MPHomeBannerCard'

export interface HomeBannerProps {
  heightContent: number
  homeBannerConfig: any
  uploadProps: any
  selectedPartner: any
  setSelectedPartner: any
  hospitalList: any
  featurePartnerList: any
  featureList: any
  doctorReferenceList: any
  serviceReferenceList: any
  subjectReferenceList: any
  servicePackageList: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onHandleChangeTab: (key: string, level: any) => void
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  formSelectPartner: (value: any) => void
  setTabActiveKey?: any
  tabActiveKey?: any
  setSelectedFilterTab?: any
  selectedFilterTab?: any
  onDragSortTable?: (data: any) => void
  onResetCache?: () => void
}

export const MPHomeBanner = ({
  heightContent,
  homeBannerConfig,
  uploadProps,
  selectedPartner,
  setSelectedPartner,
  hospitalList,
  featurePartnerList,
  featureList,
  doctorReferenceList,
  serviceReferenceList,
  subjectReferenceList,
  servicePackageList,
  loading,
  onPressViewDetail,
  onHandleChangeTab,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  formSelectPartner,
  setTabActiveKey,
  tabActiveKey,
  setSelectedFilterTab,
  selectedFilterTab,
  onDragSortTable,
  onResetCache
}: HomeBannerProps) => {
  return (
    <MPHomeBannerComponent
      renderItem={() => {
        return (
          <MPHomeBannerCard
            heightContent={heightContent}
            homeBannerConfig={homeBannerConfig}
            uploadProps={uploadProps}
            selectedPartner={selectedPartner}
            setSelectedPartner={setSelectedPartner}
            hospitalList={hospitalList}
            featurePartnerList={featurePartnerList}
            featureList={featureList}
            doctorReferenceList={doctorReferenceList}
            serviceReferenceList={serviceReferenceList}
            subjectReferenceList={subjectReferenceList}
            servicePackageList={servicePackageList}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            onHandleChangeTab={onHandleChangeTab}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            formSelectPartner={formSelectPartner}
            setTabActiveKey={setTabActiveKey}
            tabActiveKey={tabActiveKey}
            setSelectedFilterTab={setSelectedFilterTab}
            selectedFilterTab={selectedFilterTab}
            onDragSortTable={onDragSortTable}
            onResetCache={onResetCache}
          />
        )
      }}
    />
  )
}

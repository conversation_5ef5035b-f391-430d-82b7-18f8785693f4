import React from 'react'
import { TablePaginationConfig } from 'antd'
import MPUserManagementCard from '../../ui/MPUserManagementCard'
import MPUserManagementComponent from '../../components/MPUserManagementComponent'

export interface UserManagementProps {
  heightContent: number
  userList: any
  userDetail: any
  hospitalList?: any
  roleList?: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

export const MPUserManagement = ({
  heightContent,
  userList,
  userDetail,
  hospitalList,
  roleList,
  loading,
  onPressDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: UserManagementProps) => {
  return (
    <MPUserManagementComponent
      renderItem={() => {
        return (
          <MPUserManagementCard
            heightContent={heightContent}
            userList={userList}
            userDetail={userDetail}
            loading={loading}
            hospitalList={hospitalList}
            roleList={roleList}
            onPressDetail={onPressDetail}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      }}
    />
  )
}

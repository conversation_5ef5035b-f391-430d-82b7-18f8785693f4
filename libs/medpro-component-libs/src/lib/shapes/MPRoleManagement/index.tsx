import React from 'react'
import { TablePaginationConfig } from 'antd'
import MPRoleManagementCard from '../../ui/MPRoleManagementCard'
import MPRoleManagementComponent from '../../components/MPRoleManagementComponent'

export interface RoleManagementProps {
  heightContent: number
  roleList: any
  hospitalList: any
  permissionList: any
  rolePermissionList: any
  roleDetail: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitTransferModule: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

export const MPRoleManagement = ({
  heightContent,
  roleList,
  hospitalList,
  permissionList,
  rolePermissionList,
  loading,
  roleDetail,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onSubmitTransferModule,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: RoleManagementProps) => {
  return (
    <MPRoleManagementComponent
      renderItem={() => {
        return (
          <MPRoleManagementCard
            heightContent={heightContent}
            roleList={roleList}
            hospitalList={hospitalList}
            permissionList={permissionList}
            rolePermissionList={rolePermissionList}
            loading={loading}
            roleDetail={roleDetail}
            onPressViewDetail={onPressViewDetail}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onSubmitTransferModule={onSubmitTransferModule}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      }}
    />
  )
}

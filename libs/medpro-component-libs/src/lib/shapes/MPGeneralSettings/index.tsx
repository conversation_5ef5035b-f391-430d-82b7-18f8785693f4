import React from 'react'
import MPGeneralSettingsComponent from '../../components/MPGeneralSettingsComponent'
import MPGeneralSettingsCard from '../../ui/MPGeneralSettingsCard'

export interface GeneralSettingsProps {
  heightContent?: number
  session?: any
  partnerAnchorInfo?: any
  specialtyList?: any
  provinceList?: any
  districtList?: any
  wardList?: any
  data?: any
  bookingGuideList?: any
  hospitalList?: any
  onChangeCity: (value: any) => void
  onChangeDistrict: (value: any) => void
  onHandleBasicFormSubmit: (values: any) => void
  onHandleMediaFormSubmit: (values: any) => void
  onHandlePriceListAction: (values: any) => void
  onHandleIntroduceAction: (values: any) => void
  onHandleBenefitAction: (values: any) => void
  onHandleChangePartner: (value: any) => void
  onHandleBookingGuideAction: (values: any, cancelModal?: any) => void
  onHandleChangeTab: (key: string, level: any) => void
  debouncedFetchUpdateTab: (values: any) => void
  uploadProps: any
  loading: any
  priceList: any
  dataQuestion: any
  faqCategories: any
  onHandleQuestionAction: (values: any) => void
  onHandleActionSpecialty: (values: any) => void
}

export const MPGeneralSettings = ({
  heightContent,
  session,
  data,
  bookingGuideList,
  hospitalList,
  specialtyList,
  partnerAnchorInfo,
  provinceList,
  districtList,
  wardList,
  onChangeCity,
  onChangeDistrict,
  onHandleBasicFormSubmit,
  onHandleMediaFormSubmit,
  onHandlePriceListAction,
  onHandleIntroduceAction,
  onHandleBenefitAction,
  onHandleChangePartner,
  onHandleBookingGuideAction,
  debouncedFetchUpdateTab,
  uploadProps,
  loading,
  priceList,
  onHandleChangeTab,
  dataQuestion,
  faqCategories,
  onHandleQuestionAction,
  onHandleActionSpecialty
}: GeneralSettingsProps) => {
  return (
    <MPGeneralSettingsComponent
      renderItem={() => {
        return (
          <MPGeneralSettingsCard
            heightContent={heightContent}
            session={session}
            data={data}
            bookingGuideList={bookingGuideList}
            hospitalList={hospitalList}
            specialtyList={specialtyList}
            partnerAnchorInfo={partnerAnchorInfo}
            provinceList={provinceList}
            districtList={districtList}
            wardList={wardList}
            onChangeCity={onChangeCity}
            onChangeDistrict={onChangeDistrict}
            onHandleBasicFormSubmit={onHandleBasicFormSubmit}
            onHandleMediaFormSubmit={onHandleMediaFormSubmit}
            onHandlePriceListAction={onHandlePriceListAction}
            onHandleIntroduceAction={onHandleIntroduceAction}
            onHandleBenefitAction={onHandleBenefitAction}
            onHandleChangePartner={onHandleChangePartner}
            onHandleBookingGuideAction={onHandleBookingGuideAction}
            debouncedFetchUpdateTab={debouncedFetchUpdateTab}
            uploadProps={uploadProps}
            loading={loading}
            priceList={priceList}
            onHandleChangeTab={onHandleChangeTab}
            dataQuestion={dataQuestion}
            faqCategories={faqCategories}
            onHandleQuestionAction={onHandleQuestionAction}
            onHandleActionSpecialty={onHandleActionSpecialty}
          />
        )
      }}
    />
  )
}

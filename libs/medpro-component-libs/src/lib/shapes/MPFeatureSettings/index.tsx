import React from 'react'
import MPFeatureSettingsComponent from '../../components/MPFeatureSettingsComponent'
import MPFeatureSettingsCard, {
  FeatureSettingsProps
} from '../../ui/MPFeatureSettingsCard'

export const MPFeatureSettings = (props: FeatureSettingsProps) => {
  return (
    <MPFeatureSettingsComponent
      listFeature={props.listFeatureByPartner}
      renderItem={({ dataResult, handleChangeSearch }: any) => {
        return (
          <MPFeatureSettingsCard
            {...props}
            dataResult={dataResult}
            handleChangeSearch={handleChangeSearch}
          />
        )
      }}
    />
  )
}

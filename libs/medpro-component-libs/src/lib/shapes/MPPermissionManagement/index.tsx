import React from 'react'
import { TablePaginationConfig } from 'antd'
import MPPermissionManagementCard from '../../ui/MPPermissionManagementCard'
import MPPermissionManagementComponent from '../../components/MPPermissionManagementComponent'

export interface PermissionManagementProps {
  heightContent: number
  permissionList: any
  permissionListFilter: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

export const MPPermissionManagement = ({
  heightContent,
  permissionList,
  permissionListFilter,
  loading,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: PermissionManagementProps) => {
  return (
    <MPPermissionManagementComponent
      renderItem={() => {
        return (
          <MPPermissionManagementCard
            heightContent={heightContent}
            permissionList={permissionList}
            permissionListFilter={permissionListFilter}
            loading={loading}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      }}
    />
  )
}

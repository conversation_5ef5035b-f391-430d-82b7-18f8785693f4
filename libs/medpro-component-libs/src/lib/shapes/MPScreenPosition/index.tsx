import React from 'react'
import { TablePaginationConfig } from 'antd'
import MPScreenPositionComponent from '../../components/MPScreenPositionComponent'
import MPScreenPositionCard from '../../ui/MPScreenPositionCard'

export interface ScreenPositionProps {
  heightContent: number
  hospitalList: any
  catListingList: any
  listingTabInfo: any
  selectedPartner: any
  setSelectedPartner: any
  setTabActiveKey?: any
  tabActiveKey?: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  onHandleChangeTab: (key: string, level: any) => void
  formSelectPartner: (value: any) => void
  onDragSortTable?: (data: any) => void
}

export const MPScreenPosition = ({
  heightContent,
  hospitalList,
  catListingList,
  listingTabInfo,
  setTabActiveKey,
  tabActiveKey,
  selectedPartner,
  setSelectedPartner,
  loading,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onHandleChangeTab,
  formSelectPartner,
  onDragSortTable
}: ScreenPositionProps) => {
  return (
    <MPScreenPositionComponent
      renderItem={() => {
        return (
          <MPScreenPositionCard
            heightContent={heightContent}
            hospitalList={hospitalList}
            catListingList={catListingList}
            listingTabInfo={listingTabInfo}
            selectedPartner={selectedPartner}
            setSelectedPartner={setSelectedPartner}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onHandleChangeTab={onHandleChangeTab}
            setTabActiveKey={setTabActiveKey}
            tabActiveKey={tabActiveKey}
            formSelectPartner={formSelectPartner}
            onDragSortTable={onDragSortTable}
          />
        )
      }}
    />
  )
}

import { TablePaginationConfig } from 'antd'
import MPOtherManagementComponent from '../../components/MPOtherManagementComponent'
import MPOtherManagementCard from '../../ui/MPOtherManagementCard'

export interface OtherManagementProps {
  heightContent: number
  popupList: any
  hospitalList: any
  pagination?: TablePaginationConfig
  loading?: boolean
  uploadProps: any
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  methodsPopup: any
  methodsBase: any
  baseValuePopupConfig: any
}

export const MPOtherManagement = ({
  heightContent,
  popupList,
  hospitalList,
  loading,
  uploadProps,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  methodsPopup,
  methodsBase,
  baseValuePopupConfig
}: OtherManagementProps) => {
  return (
    <MPOtherManagementComponent
      renderItem={() => {
        return (
          <MPOtherManagementCard
            heightContent={heightContent}
            popupList={popupList}
            hospitalList={hospitalList}
            loading={loading}
            uploadProps={uploadProps}
            onPressViewDetail={onPressViewDetail}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            methodsPopup={methodsPopup}
            methodsBase={methodsBase}
            baseValuePopupConfig={baseValuePopupConfig}
          />
        )
      }}
    />
  )
}

import React from 'react'
import { TablePaginationConfig } from 'antd'
import MPBannerHomePageBlogComponent from '../../components/MPBannerHomePageBlogComponent'
import MPBannerHomePageBlogCard from '../../ui/MPBannerHomePageBlogCard'

export interface BannerHomePageBlogProps {
  heightContent: number
  hospitalList: any
  homeBannerBlog: any
  pagination?: TablePaginationConfig
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

export const MPBannerBlog = ({
  heightContent,
  hospitalList,
  homeBannerBlog,
  loading,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: BannerHomePageBlogProps) => {
  return (
    <MPBannerHomePageBlogComponent
      renderItem={() => {
        return (
          <MPBannerHomePageBlogCard
            heightContent={heightContent}
            hospitalList={hospitalList}
            homeBannerBlog={homeBannerBlog}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            onSubmitSearch={onSubmitSearch}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      }}
    />
  )
}

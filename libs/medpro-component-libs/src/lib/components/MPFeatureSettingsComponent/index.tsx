import React, { useEffect, useState } from 'react'
import { handleFilterByKeyword } from '../../helpers/func'

export interface Props {
  listFeature?: any[]
  renderItem: any
}

export const MPFeatureSettingsComponent = ({
  listFeature,
  renderItem
}: Props) => {
  const [search, setSearch] = useState()
  const [dataResult, setDataResult] = useState(listFeature)

  useEffect(() => {
    handleFilter()
  }, [search, listFeature])

  const handleFilter = () => {
    setDataResult(handleFilterByKeyword(search, listFeature, 'name'))
  }

  const handleChangeSearch = (value: any) => {
    setSearch(value)
  }

  return (
    <>
      {renderItem({
        dataResult,
        handleChangeSearch
      })}
    </>
  )
}

export default MPFeatureSettingsComponent

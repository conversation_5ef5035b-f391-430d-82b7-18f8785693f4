import React, { useState } from 'react'
import { Card, Avatar, Rate, DatePicker, Pagination, Skeleton } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import dayjs, { Dayjs } from 'dayjs'
import cx from 'classnames'
import styles from './styles.module.less'

interface ReviewData {
  isPrioritize: boolean
  status: number
  customerName: string
  rating: number
  comment: string
  bookingCode: string
  booking: string
  hospital: {
    partnerId: string
    name: string
    address: string
    circleLogo: string
    id: string
  }
  hospitalId: string
  user?: {
    username: string
    email: string
    fullname: string
    id: string
  }
  createdAt: string
  updatedAt: string
  id: string
}

interface MPReviewCardProps {
  evaluatePartnerList?: ReviewData[]
  pageSize?: number
  fetchLoading?: boolean
}

const MPReviewCard: React.FC<MPReviewCardProps> = (props) => {
  const { evaluatePartnerList = [], pageSize = 5, fetchLoading = false } = props
  const [currentPage, setCurrentPage] = useState<number>(1)

  /**
   * T<PERSON>h toán điểm đ<PERSON>h giá trung bình từ danh sách evaluatePartnerList.
   * Nếu danh sách rỗng, trả về 0.
   */
  const overallRating =
    evaluatePartnerList.length > 0
      ? evaluatePartnerList.reduce((sum, item) => sum + item.rating, 0) /
        evaluatePartnerList.length
      : 0

  /**
   * Phân tích chuỗi định dạng ngày dạng "MM/DD/YYYY - MM/DD/YYYY"
   * thành một tuple chứa 2 đối tượng Dayjs hoặc null.
   *
   * @param {string} [rangeStr] - Chuỗi khoảng thời gian, ví dụ "04/01/2025 - 05/01/2025".
   * @returns {[Dayjs | null, Dayjs | null]} - Một tuple gồm ngày bắt đầu và ngày kết thúc.
   */
  const parseRange = (rangeStr?: string): [Dayjs | null, Dayjs | null] => {
    if (!rangeStr) return [null, null]
    const [start, end] = rangeStr.split(' - ')
    return [
      start ? dayjs(start, 'MM/DD/YYYY') : null,
      end ? dayjs(end, 'MM/DD/YYYY') : null
    ]
  }

  /**
   * Khoảng thời gian mặc định, tính từ 30 ngày trước đến hôm nay.
   * Định dạng chuỗi: "MM/DD/YYYY - MM/DD/YYYY".
   *
   * @type {string}
   */
  const defaultDateRange = `${dayjs()
    .subtract(30, 'day')
    .format('MM/DD/YYYY')} - ${dayjs().format('MM/DD/YYYY')}`

  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null]>(
    parseRange(defaultDateRange)
  )

  // Tính toán chỉ số bắt đầu và kết thúc của trang hiện tại
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const currentReviews = evaluatePartnerList.slice(startIndex, endIndex)

  // Xử lý sự kiện thay đổi trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  /**
   * Hàm hiển thị skeleton loading cards dùng để placeholder trong khi chờ dữ liệu đánh giá.
   * Mỗi card bao gồm avatar giả, thông tin người đánh giá và nội dung đánh giá giả lập.
   *
   * @returns {JSX.Element[]} - Mảng gồm phần tử JSX đại diện cho các skeleton cards.
   */
  const renderSkeletons = () => {
    return Array(pageSize)
      .fill(null)
      .map((_, index) => (
        <Card key={`skeleton-${index}`} className={styles['reviewCard']}>
          <div className={styles['reviewerRow']}>
            <Skeleton.Avatar active size={48} className={styles['avatar']} />
            <div className={styles['reviewerInfo']} style={{ width: '100%' }}>
              <Skeleton.Input active style={{ width: 120 }} size='small' />
              <Skeleton.Input
                active
                style={{ width: 150, marginTop: 8 }}
                size='small'
              />
              <Skeleton.Input
                active
                style={{ width: 80, marginTop: 8 }}
                size='small'
              />
            </div>
          </div>
          <Skeleton active paragraph={{ rows: 2 }} title={false} />
        </Card>
      ))
  }

  return (
    <div className={cx(styles['pageReview'], 'fade-in')}>
      <Card className={styles['ratingBox']}>
        <div className={styles['overallHeader']}>
          <div>
            <div className={styles['overallLabel']}>
              Điểm đánh giá trung bình
            </div>
            <div className={styles['overallScoreSection']}>
              <span className={styles['overallScore']}>
                {overallRating.toFixed(1)}
              </span>
              <Rate
                disabled
                allowHalf
                value={overallRating}
                className={styles['overallRate']}
              />
            </div>
          </div>
          <div className={styles['dateRange']}>
            <DatePicker.RangePicker
              value={dateRange}
              format='MM/DD/YYYY'
              onChange={(range) =>
                setDateRange(range as [Dayjs | null, Dayjs | null])
              }
              allowClear={false}
              style={{ width: 240 }}
            />
          </div>
        </div>
      </Card>
      {fetchLoading ? (
        renderSkeletons()
      ) : (
        <>
          {currentReviews.map((review, index) => (
            <Card key={review.id} className={styles['reviewCard']}>
              <div className={styles['reviewerRow']}>
                <Avatar
                  src={
                    review.user?.fullname ? null : review.hospital.circleLogo
                  }
                  icon={<UserOutlined />}
                  size={48}
                  className={styles['avatar']}
                />
                <div className={styles['reviewerInfo']}>
                  <div className={styles['name']}>{review.customerName}</div>
                  <Rate
                    disabled
                    value={review.rating}
                    className={styles['reviewRate']}
                  />
                  <div className={styles['date']}>
                    {dayjs(review.createdAt).format('DD/MM/YYYY')}
                  </div>
                </div>
              </div>
              <div className={styles['reviewText']}>{review.comment}</div>
            </Card>
          ))}

          {evaluatePartnerList.length > pageSize && (
            <div className={styles['paginationContainer']}>
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={evaluatePartnerList.length}
                onChange={handlePageChange}
                showSizeChanger={false}
              />
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default MPReviewCard

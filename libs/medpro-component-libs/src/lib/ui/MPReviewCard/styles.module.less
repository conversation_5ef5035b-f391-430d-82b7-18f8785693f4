.pageReview {
  margin: 0 auto;

  .ratingBox {
    margin-bottom: 24px;
    border-radius: 12px;
    background: #fff;
    border: none;

    .overallHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .overallLabel {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
    }
    .overallScoreSection {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .overallScore {
      font-size: 32px;
      font-weight: 600;
    }
    .overallRate {
      font-size: 20px;
    }
    .dateRange {
      // background: #f5f7fa;
      border-radius: 8px;
      padding: 4px 16px;
      color: #4a4a4a;
      font-size: 14px;
      display: flex;
      align-items: center;
    }
  }

  .reviewCard {
    border-radius: 12px;
    background: #fff;
    margin-bottom: 16px;
    border: 1px solid #d6d7d8;
    .reviewerRow {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
    }
    .avatar {
      margin-right: 16px;
      border: 2px solid #f5f7fa;
    }
    .reviewerInfo {
      display: flex;
      flex-direction: column;
      .name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      .date {
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
      }
    }
    .reviewRate {
      font-size: 16px;
      margin-bottom: 4px;
    }
    .reviewText {
      color: #253858;
      font-size: 16px;
      line-height: 1.5;
      margin-top: 8px;
      white-space: pre-line;
    }
  }

  .paginationContainer {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    margin-bottom: 24px;
  }
}

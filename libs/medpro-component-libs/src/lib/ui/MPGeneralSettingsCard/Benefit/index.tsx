import React, { useEffect } from 'react'
import cx from 'classnames'
import { Form } from 'antd'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputItems'
import styles from './styles.module.less'

export interface Props {
  data?: any
  dataTab?: any
  loading?: boolean
  uploadProps: any
  onHandleBenefitAction: (values: any) => void
}

const Benefit = ({
  loading,
  data,
  dataTab,
  uploadProps,
  onHandleBenefitAction
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        benefit: data?.websiteInformation?.benefit,
        tabTitle: dataTab?.title
      })
    }
  }, [data, form])

  const onFinish = (values: any) => {
    onHandleBenefitAction({ ...values, tabId: dataTab?._id })
  }

  return (
    <div className={styles['contentWrapper']}>
      <Form
        form={form}
        onFinish={onFinish}
        scrollToFirstError={{ behavior: 'smooth' }}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems(form, uploadProps)
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div key={rowIndex} className={styles['inputRow']}>
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton
              loading={loading}
              htmlType='submit'
              typeCustom={'approval'}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </div>
  )
}

export default Benefit

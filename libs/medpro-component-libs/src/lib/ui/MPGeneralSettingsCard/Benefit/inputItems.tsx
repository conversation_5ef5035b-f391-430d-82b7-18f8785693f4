import { Form, Input } from 'antd'
import React from 'react'
import MPCKEditor from '../../../atoms/MPCKEditor'
import { Valid } from '../../../helpers/valid'

const valid = new Valid()

export const inputItems = (form: any, uploadProps: any) => {
  return [
    {
      id: 'tabTitle',
      type: 'text',
      label: 'Tiêu đề',
      placeholder: 'Vui lòng nhập tiêu đề',
      require: true,
      enter: ({ id, require, type, placeholder, label, width }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
            style={{ maxWidth: width }}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false,
      width: '300px'
    },
    {
      id: 'benefit',
      type: 'text',
      label: 'Nội dung',
      placeholder: 'Nhập nội dung',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            {uploadProps && (
              <MPCKEditor
                uploadProps={uploadProps}
                value={form.getFieldValue(id)}
                onChange={(data: any) => {
                  form.setFieldValue(id, data)
                }}
              />
            )}
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

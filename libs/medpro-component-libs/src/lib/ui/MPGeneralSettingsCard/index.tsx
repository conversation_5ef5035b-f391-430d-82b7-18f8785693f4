import React, { useEffect, useState } from 'react'
import { Select, Tabs } from 'antd'
import cx from 'classnames'
import { getReplaceUTF8 } from '@medpro-libs/medpro-component-libs'
import { size } from 'lodash'
import { adminPartner } from '../../helpers/constants'
import { useSearchParams } from 'next/navigation'
import BasicInformation from './BasicInformation'
import MediaInformation from './MediaInformation'
import WebsiteInformation from './WebsiteInformation'
import styles from './styles.module.less'

export interface Props {
  heightContent?: number
  session?: any
  partnerAnchorInfo?: any
  specialtyList?: any
  provinceList?: any
  districtList?: any
  wardList?: any
  data?: any
  bookingGuideList?: any
  hospitalList?: any
  onChangeCity: (value: any) => void
  onChangeDistrict: (value: any) => void
  onHandleBasicFormSubmit: (values: any) => void
  onHandleMediaFormSubmit: (values: any) => void
  onHandlePriceListAction: (values: any) => void
  onHandleIntroduceAction: (values: any) => void
  onHandleBenefitAction: (values: any) => void
  onHandleChangePartner: (value: any) => void
  onHandleBookingGuideAction: (values: any, cancelModal?: any) => void
  onHandleChangeTab: (key: string, level: any) => void
  debouncedFetchUpdateTab: (values: any) => void
  onHandleQuestionAction: (values: any) => void
  onHandleActionSpecialty: (values: any) => void
  uploadProps: any
  loading: any
  priceList: any
  dataQuestion: any
  faqCategories: any
}

const MPGeneralSettingsCard = ({
  heightContent,
  session,
  data,
  partnerAnchorInfo,
  specialtyList,
  bookingGuideList,
  hospitalList,
  provinceList,
  districtList,
  wardList,
  onChangeCity,
  onChangeDistrict,
  onHandleBasicFormSubmit,
  onHandleMediaFormSubmit,
  onHandlePriceListAction,
  onHandleIntroduceAction,
  onHandleBenefitAction,
  onHandleChangePartner,
  onHandleBookingGuideAction,
  debouncedFetchUpdateTab,
  uploadProps,
  loading,
  priceList,
  onHandleChangeTab,
  dataQuestion,
  faqCategories,
  onHandleActionSpecialty,
  onHandleQuestionAction
}: Props) => {
  const { Option } = Select
  const searchParams = useSearchParams()
  const [tabActiveKey, setTabActiveKey] = useState<any>('1')
  const [partnerId, setPartnerId] = useState(undefined)

  useEffect(() => {
    setPartnerId(data?.partnerId)
  }, [data])

  useEffect(() => {
    if (searchParams && searchParams.get('generalTab')) {
      setTabActiveKey(searchParams.get('generalTab'))
    }
  }, [searchParams])

  const onChangePartner = (value: any) => {
    setPartnerId(value)
    onHandleChangePartner(value)
  }

  const items = [
    {
      key: '1',
      label: 'Thông tin hành chính',
      forceRender: true,
      children: null
    },
    {
      key: '2',
      label: 'Thông tin Website',
      forceRender: true,
      children: null
    },
    {
      key: '3',
      label: 'Media',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case '1':
        return (
          <BasicInformation
            data={data}
            provinceList={provinceList}
            districtList={districtList}
            wardList={wardList}
            onChangeCity={onChangeCity}
            onChangeDistrict={onChangeDistrict}
            onFormSubmit={onHandleBasicFormSubmit}
            uploadProps={uploadProps}
            loading={loading}
          />
        )
      case '2':
        return (
          <WebsiteInformation
            hospitalInfo={data}
            partnerAnchorInfo={partnerAnchorInfo}
            specialtyList={specialtyList}
            heightContent={heightContent}
            bookingGuideList={bookingGuideList}
            onHandlePriceListAction={onHandlePriceListAction}
            onHandleIntroduceAction={onHandleIntroduceAction}
            onHandleBenefitAction={onHandleBenefitAction}
            onHandleBookingGuideAction={onHandleBookingGuideAction}
            priceList={priceList}
            debouncedFetchUpdateTab={debouncedFetchUpdateTab}
            uploadProps={uploadProps}
            onHandleChangeTab={onHandleChangeTab}
            dataQuestion={dataQuestion}
            faqCategories={faqCategories}
            onHandleQuestionAction={onHandleQuestionAction}
            onHandleActionSpecialty={onHandleActionSpecialty}
            loading={loading}
          />
        )
      case '3':
        return (
          <MediaInformation
            data={data}
            onFormSubmit={onHandleMediaFormSubmit}
            uploadProps={uploadProps}
            loading={loading}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className={cx(styles['pageWrapper'], 'fade-in')}>
      <div className={styles['header']}>
        <div className={styles['title']}>
          <h2>Cấu hình thông tin</h2>
        </div>
        <div className={styles['actionHeader']}>
          {adminPartner.includes(session?.partnerId) && (
            <div className={styles['inputItem']}>
              <div className={styles['label']}>CSYT:</div>
              <Select
                placeholder={'Chọn cơ sở y tế'}
                onChange={onChangePartner}
                value={partnerId}
                showSearch
                filterOption={(input, option: any) =>
                  getReplaceUTF8(
                    (option?.children as unknown as string).toLowerCase()
                  ).includes(getReplaceUTF8(input.toLowerCase()))
                }
              >
                {hospitalList && size(hospitalList) > 0
                  ? hospitalList?.map((item: any, index: number) => (
                      <Option key={index} value={item.partnerId}>
                        {item.name}
                      </Option>
                    ))
                  : undefined}
              </Select>
            </div>
          )}
        </div>
      </div>
      <div className={cx(styles['body'])}>
        <div className={styles['bigTabWrapper']}>
          <Tabs
            onChange={(key) => {
              setTabActiveKey(key)
              onHandleChangeTab(key, 'generalTab')
            }}
            activeKey={tabActiveKey}
            defaultActiveKey='1'
            tabPosition={'top'}
            items={items}
            type={'card'}
          />
        </div>
        {renderTabPane()}
      </div>
    </div>
  )
}
export default MPGeneralSettingsCard

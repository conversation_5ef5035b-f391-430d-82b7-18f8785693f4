import { Form } from 'antd'
import React from 'react'
import DynamicVideoInput from '../DynamicVideoInput'
import MPImageFormItemMultiple from '../../../atoms/MPImageFormItemMultiple'

export const imgInputItems = (
  form: any,
  uploadProps: any,
  onChangeImage: (key: any, value: any) => void
) => {
  return [
    {
      id: 'images',
      type: 'upload',
      label: 'Hình ảnh slideshow',
      placeholder: '',
      require: false,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={true}
              maxCount={30}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'banner',
      type: 'upload',
      label: '<PERSON><PERSON><PERSON> ảnh quảng cáo',
      placeholder: '',
      require: false,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={true}
              maxCount={5}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'iframeVideo',
      type: 'text',
      label: 'Video quảng bá',
      placeholder: 'Iframe video',
      require: false,
      enter: (props: any) => <DynamicVideoInput {...props} />,
      hidden: false,
      group: false
    }
  ]
}

import React, { useEffect } from 'react'
import { Collapse, CollapseProps, Form } from 'antd'
import cx from 'classnames'
import { get, isArray } from 'lodash'
import { MPButton } from '../../../atoms/MPButton'
import { imgInputItems } from './inputItems'
import styles from '../styles.module.less'

export interface Props {
  data?: any
  handleSubmit?: (values: any) => void
  partnerId?: any
  uploadProps?: any
  loading: any
  onFormSubmit: (values: any) => void
}

const MediaInformation = ({
  data,
  uploadProps,
  onFormSubmit,
  loading
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data,
        images: data?.websiteInformation?.images,
        banner: data?.websiteInformation?.banner
      })
    }
  }, [data, form])

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onFinish = (values: any) => {
    onFormSubmit(values)
  }

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: 'Hình ảnh',
      collapsible: 'disabled',
      showArrow: false,
      children: imgInputItems(form, uploadProps, onChangeImage)
        .reduce((rows: any, item: any, index: number, array: any) => {
          if (!item.group) {
            rows.push([item])
          } else if (index % 2 === 0) {
            rows.push(array.slice(index, index + 2))
          }
          return rows
        }, [])
        .map((row: any, rowIndex: number) => (
          <div key={rowIndex} className={styles['inputRow']}>
            {row.map((item: any, itemIndex: number) => (
              <div key={itemIndex} className={styles['inputItem']}>
                {typeof item?.enter === 'function' ? item.enter(item) : null}
              </div>
            ))}
          </div>
        ))
    }
  ]

  return (
    <Form
      form={form}
      onFinish={onFinish}
      scrollToFirstError={{ behavior: 'smooth' }}
      layout={'vertical'}
      className={cx(styles['formGroupWrapper'], styles['two'])}
    >
      <Collapse
        items={items}
        defaultActiveKey={['1']}
        expandIconPosition={'end'}
      />
      <div className={styles['groupAction']}>
        <Form.Item>
          <MPButton loading={loading} htmlType='submit' typeCustom={'approval'}>
            Lưu
          </MPButton>
        </Form.Item>
      </div>
    </Form>
  )
}

export default MediaInformation

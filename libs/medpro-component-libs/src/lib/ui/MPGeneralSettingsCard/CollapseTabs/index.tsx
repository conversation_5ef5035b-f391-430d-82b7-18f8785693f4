import { Col, Collapse, Form, Input, Modal, Row, Space, Tabs } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
// import MPButton from '../../ui/MPButton'
import styles from './styles.module.less'
import MPButton from '../../../atoms/MPButton'
import { debounce, size } from 'lodash'
import ActionItem from './common/ActionItem'
import { handleDetails } from './common/handleDetails'
import QuestionList from './common/QuestionList'
// import { handleDetails } from './common/handleDetails'
// import QuestionList from './common/QuestionList'

interface Props {
  dataQuestion: any
  faqCategories?: any[]
  handleChangeQuestion?: (id: string, index?: number) => void
  uploadToCloudinary?: any
  handleAction?: any
  uploadProps?: any
  dataTab?: any
  onHandleQuestionAction: (values: any) => void
  debouncedFetchUpdateTab: (values: any) => void
}

// interface DataType {
//   type: string
//   name: string
// }

const CollapseTabs = (props: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [dataTabText, setDataTabText] = useState(undefined)
  const [edit, setEdit] = useState() as any

  const [categoryId] = useState('9c8461c7ceaf4405aa49b1b87ec32746')
  const [form] = Form.useForm()

  const debouncedHandleChangeTabText = useCallback(
    debounce((value: string) => {
      props.debouncedFetchUpdateTab({
        tabTitle: value,
        tabId: props?.dataTab?._id
      })
    }, 1000),
    []
  )

  useEffect(() => {
    setDataTabText(props?.dataTab?.title)
  }, [props?.dataTab])

  const toggleModal = () => {
    setIsModalOpen(!isModalOpen)
  }

  const handleAction = (data: any) => {
    if (data.item.title === 'delete') {
      props.onHandleQuestionAction({
        _id: data.item.info._id,
        action: 'delete'
      })
    } else if (data.item.title === 'edit') {
      setEdit(data.item.info)
      form.setFieldsValue({
        category_id: data.item.info.category_id,
        question: data.item.info.question,
        sortOrder: data.item.info.sortOrder,
        answer: data.item.info.answer
      })
      toggleModal()
    }
  }

  const toggleModalBaseFAQ = () => {
    form.resetFields()
    setEdit(undefined)
    toggleModal()
  }

  const onFinish = (values: any) => {
    if (edit) {
      const dataResult = {
        ...edit,
        question: values?.question,
        sortOrder: values?.sortOrder,
        answer: values?.answer
      }
      props.onHandleQuestionAction({ ...dataResult, action: 'update' })
    } else {
      props.onHandleQuestionAction({
        ...values,
        status: 1,
        isDeleted: false,
        action: 'create'
      })
    }
    toggleModalBaseFAQ()
  }

  const handleChangeTabText = (e: any) => {
    setDataTabText(e.target.value)
    debouncedHandleChangeTabText(e.target.value)
  }

  return (
    <>
      <div className={styles['HomeBannerCard']}>
        <div className={styles['inputTabWrapper']}>
          <div className={styles['label']}>Tiêu đề</div>
          <Input
            placeholder='Vui lòng nhập tiêu đề'
            value={dataTabText}
            onChange={handleChangeTabText}
            className={styles['input']}
          />
        </div>
        <div className={styles['titleWrapper']}>
          <div className={styles['label']}>Nội dung</div>
        </div>
        <div className={styles['headerButton']}>
          <Space direction='horizontal' className={styles['groupButton']}>
            <MPButton
              typeCustom={'cancel'}
              onClick={() =>
                props.onHandleQuestionAction({ action: 'refetch' })
              }
            >
              Tải lại
            </MPButton>
            <MPButton
              typeCustom={'primary'}
              onClick={toggleModalBaseFAQ}
            >
              Thêm mới
            </MPButton>
          </Space>
        </div>
        <div>
          <div className={styles['tabs']}>
            <QuestionList
              handleAction={handleAction}
              data={props.dataQuestion}
            />
          </div>
        </div>
      </div>
      <Modal
        title={edit ? 'Cập nhật câu hỏi' : 'Thêm mới câu hỏi'}
        open={isModalOpen}
        onCancel={() => toggleModal()}
        className={styles['modalForm123']}
        footer={null}
      >
        <div>
          <Form
            layout='vertical'
            form={form}
            onFinish={onFinish}
            className={styles['listContact']}
          >
            <Row gutter={50}>
              {handleDetails(props.faqCategories, props.uploadProps, form).map(
                (item, index) => {
                  return (
                    <Col
                      key={index}
                      span={24}
                      lg={item.width === 'fuild' ? 24 : 12}
                    >
                      <div className={styles['inputItem']}>
                        {item?.enter && item?.enter(item)}
                      </div>
                    </Col>
                  )
                }
              )}
            </Row>
            <div className={styles['footerButton']}>
              <MPButton
                className={styles['btnFooter']}
                type='default'
                onClick={toggleModalBaseFAQ}
              >
                Đóng
              </MPButton>
              <MPButton
                className={styles['btnFooter']}
                type='primary'
                htmlType='submit'
              >
                Cập nhật
              </MPButton>
            </div>
          </Form>
        </div>
      </Modal>
    </>
  )
}

export default CollapseTabs

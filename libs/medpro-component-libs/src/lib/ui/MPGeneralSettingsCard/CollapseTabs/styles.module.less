.HomeBannerCard {
  padding-bottom: 20px;

  .title {
    font-size: 20px;
    font-weight: 600;
    text-align: left;
    margin-bottom: 0;
  }

  .inputTabWrapper {
    display: flex;
    flex-direction: column;

    .label {
      display: flex;
      height: 35px;
      font-weight: 500;
      position: relative;
      align-items: center;
      max-width: 100%;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      padding: 0 0 8px;

      &::after {
        display: inline-block;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
        margin-left: 5px !important;
      }
    }

    .input {
      box-sizing: border-box;
      margin: 0;
      padding: 4px 11px;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      list-style: none;
      position: relative;
      display: inline-block;
      width: 100%;
      min-width: 0;
      transition: all 0.2s;
      max-width: 300px;
      border-radius: 8px;
      min-height: 38px;
    }
  }

  .titleWrapper {
    margin-top: 10px;

    .label {
      display: flex;
      height: 35px;
      font-weight: 500;
      position: relative;
      align-items: center;
      max-width: 100%;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      padding: 0 0 8px;
    }
  }

  .headerButton {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .btnCreate {
      height: fit-content;
    }

    .btnReload {
      height: fit-content;
      background-color: #fd7e14;

      &:hover {
        background-color: #fd7e14d9;
      }
    }
  }

  .tabs {
    width: 100%;

    :global {
      .ant-collapse-header {
        align-items: center;
      }

      .ant-collapse-content {
        border-radius: 0 0 12px 12px !important;
      }

      .ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          font-weight: 400;
        }
      }

      // .ant-tabs-nav {
      //   display: none;
      // }
    }

    .panel {
      border: 1px solid #d9d9d9;
      margin-bottom: 7px;

      border-radius: 12px !important;

      .headerPanel {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
      }
    }

    .emptyWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
      min-height: 650px;
    }
  }

  .btn {
    min-width: 30px !important;
    max-width: 30px !important;
    max-height: 30px;

    border: none;
  }

  .btnEdit {
    &:hover,
    &:focus,
    &:active {
      border-color: #f1c40f;

      svg {
        fill: #f1c40f;
      }
    }
  }

  .btnDelete {
    &:hover,
    &:focus,
    &:active {
      border-color: #fd766a;

      svg {
        fill: #fd766a;
      }
    }
  }

  .btnEdit,
  .btnDelete {
    border-radius: 50%;
    padding: 5px;

    svg {
      fill: grey;
      width: 17px;
      height: 17px;
    }
  }
}

.modalForm {
  .titleTranslate {
    font-weight: 600;
  }

  :global {
    .ant-modal-body {
      padding-bottom: 15px;
    }

    .ant-form-vertical .ant-form-item-label > label {
      font-weight: 600;
    }

    .ant-input-number {
      width: 100%;
    }

    .ant-modal-header {
      color: #ffffff;
      background: #0288d1;
    }

    .ant-modal-title,
    .ant-modal-close {
      color: #ffffff;
    }
  }

  .btnUploadImage {
    svg {
      fill: #000000;
    }
  }
}

.footerButton {
  width: 100%;
  padding-top: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.formInputItem {
  label {
    font-weight: 600;
  }

  :global {
    .ql-toolbar.ql-snow + .ql-container.ql-snow {
      min-height: 150px;
      max-height: 300px;
      overflow-y: scroll;
      overflow-x: hidden;
    }
  }

  .requireInput {
    font-size: 100%;
    top: -0.2em;
    left: 3px;
    color: red;
  }
}

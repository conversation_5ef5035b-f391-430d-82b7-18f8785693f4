import React from 'react'
import { Collapse, Space } from 'antd'
import Image from 'next/image'
import { size } from 'lodash'
import ActionItem from './../ActionItem'
import IconTableEmpty from '../../../../../images/iconTableEmpty.png'
import styles from './../../styles.module.less'

interface Props {
  data: any[]
  handleAction: (data: any) => void
}

const QuestionList = (props: Props) => {
  const collapseItems = props.data.map((item, index) => ({
    key: String(index + 1),
    label: (
      <div className={styles['headerPanel']}>
        <div>
          {index + 1}. &nbsp; {item.question}
        </div>
      </div>
    ),
    children: (
      <div
        dangerouslySetInnerHTML={{
          __html: item.answer
        }}
      />
    ),
    extra: <ActionItem item={item} handleAction={props.handleAction} />,
    className: styles['panel']
  }))

  return (
    <Space direction='vertical' style={{ width: '100%' }} size={24}>
      {size(props?.data) > 0 ? (
        <Collapse items={collapseItems} />
      ) : (
        <div className={styles['emptyWrapper']}>
          <Image src={IconTableEmpty} alt={''} priority />
          <span>Không có dữ liệu</span>
        </div>
      )}
    </Space>
  )
}

export default QuestionList

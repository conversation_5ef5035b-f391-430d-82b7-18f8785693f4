import { Mo<PERSON>, Too<PERSON><PERSON> } from 'antd'
import { Space } from 'antd'
import React from 'react'
import cx from 'classnames'
import { AiFillDelete, AiFillEdit } from 'react-icons/ai'
import styles from './../../styles.module.less'
import MPButton from '../../../../../../../src/lib/atoms/MPButton'

interface Props {
  item: any
  handleAction: (data: any) => void
}

const ActionItem = (props: Props) => {
  const confirm = (event: any) => {
    props.handleAction({ item: { title: 'delete', info: props.item } })
    event.stopPropagation()
  }

  const onPressDelete = (event: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return confirm(event)
      }
    })
  }

  return (
    <Space size='middle'>
      <MPButton
        onClick={(event) => {
          props.handleAction({ item: { title: 'edit', info: props.item } })
          event.stopPropagation()
        }}
        className={cx(styles['btn'], styles['btnEdit'])}
        type='default'
      >
        <Tooltip title='Chỉnh sửa' placement='bottom' color='#f1c40f'>
          <div style={{ display: 'flex' }}>
            <AiFillEdit />
          </div>
        </Tooltip>
      </MPButton>
      <MPButton
        className={cx(styles['btn'], styles['btnDelete'])}
        type='default'
        onClick={(event) => {
          onPressDelete(event)
        }}
      >
        <Tooltip title='Xóa' placement='bottom' color='#fd766a'>
          <div style={{ display: 'flex' }}>
            <AiFillDelete />
          </div>
        </Tooltip>
      </MPButton>
    </Space>
  )
}

export default ActionItem

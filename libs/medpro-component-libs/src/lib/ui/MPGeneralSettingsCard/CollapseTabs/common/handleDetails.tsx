import { Form, Input, InputNumber, Select, Upload } from 'antd'
import { Valid } from '../../../../helpers/valid'
import { UploadOutlined } from '@ant-design/icons'

import styles from './../styles.module.less'
import { size } from 'lodash'
import { handleRequireInput } from '../../../common/componentGlobal'
import MPCKEditor from '../../../../../../src/lib/atoms/MPCKEditor'
// import { MPReactQuill } from '@medpro-libs/libs/client'

const valid = new Valid()
const { Option } = Select
const normFile = (e: any) => {
  if (Array.isArray(e)) {
    return e
  }
  return e?.fileList
}
export const handleDetails = (data: any, uploadProps: any, form: any) => {
  const list = [
    {
      id: 'question',
      type: 'text',
      label: 'Câu hỏi',
      placeholder: 'Tiêu đề câu hỏi',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'answer',
      type: 'text',
      label: 'Trả lời',
      placeholder: 'Câu trả lời...',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.requiredEditor }]}
            className={styles['formInputItem']}
          >
            <MPCKEditor
              uploadProps={uploadProps}
              value={form.getFieldValue(id)}
              onChange={(data: any) => {
                form.setFieldValue(id, data)
              }}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'sortOrder',
      type: 'number',
      label: 'Trọng số',
      placeholder: 'Trọng số',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <InputNumber type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    }
  ]
  return list
}

export const position = [
  {
    title: 'IN',
    value: 'IN'
  },
  {
    title: 'OUT',
    value: 'OUT'
  }
]

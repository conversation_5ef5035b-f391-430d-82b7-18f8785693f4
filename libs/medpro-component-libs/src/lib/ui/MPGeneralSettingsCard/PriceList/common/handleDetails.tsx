import { Form, Input, InputNumber, Switch } from 'antd'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import TextArea from 'antd/es/input/TextArea'
import { Valid } from '../../../../helpers/valid'
import styles from './../styles.module.less'

const valid = new Valid()

export const handleDetails = () => {
  return [
    {
      id: 'id',
      type: 'text',
      enter: ({ id, hidden }: any) => {
        return (
          <Form.Item
            name={id}
            valuePropName='checked'
            className={styles['formInputItem']}
            hidden={hidden}
          />
        )
      },
      hidden: true,
      group: false
    },
    {
      id: 'name',
      type: 'text',
      label: 'Tên bảng giá',
      placeholder: 'Nhập tên bảng giá',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'price',
      type: 'number',
      label: 'Gi<PERSON> tiền',
      placeholder: '<PERSON>hập giá tiền',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            rules={[{ validator: valid.required, required: require }]}
            name={id}
          >
            <InputNumber
              placeholder={placeholder}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
              style={{ width: '100%' }}
              min={0}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'status',
      type: '',
      label: 'Hiển thị bảng giá',
      placeholder: '',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id} valuePropName='checked'>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
              defaultChecked
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'description',
      type: 'text',
      label: 'Mô tả bảng giá',
      placeholder: 'Mô tả bảng giá',
      require: false,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
          >
            <TextArea
              placeholder={placeholder}
              autoSize={{ minRows: 3, maxRows: 5 }}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

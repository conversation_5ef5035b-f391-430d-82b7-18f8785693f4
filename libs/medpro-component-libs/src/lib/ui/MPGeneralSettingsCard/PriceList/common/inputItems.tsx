import { Form, Input } from 'antd'
import React from 'react'
import { Valid } from '../../../../helpers/valid'

const valid = new Valid()

export const inputItems = (form: any) => {
  return [
    {
      id: 'tabName',
      type: 'text',
      label: 'Tiêu đề',
      placeholder: '<PERSON>ui lòng nhập tiêu đề',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

import { useState } from 'react'
import { Button, Form, Input } from 'antd'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import styles from '../styles.module.less'

const DynamicVideoInput = ({ id, type, placeholder, label }: any) => {
  const [inputs, setInputs] = useState([{ key: 0 }])

  const addInput = () => {
    setInputs([...inputs, { key: inputs.length }])
  }

  const removeInput = (index: number) => {
    setInputs(inputs.filter((_, i) => i !== index))
  }

  return (
    <>
      {inputs.map((input, index) => (
        <Form.Item
          key={input.key}
          label={`${label} ${index + 1}`}
          name={`${id}_${index}`}
          className={styles['moreVideo']}
        >
          <Input type={type} placeholder={placeholder} />
          {index > 0 && (
            <Button
              className={styles['btnRemoveInput']}
              type='text'
              danger
              onClick={() => removeInput(index)}
              icon={<MinusCircleOutlined />}
            />
          )}
        </Form.Item>
      ))}

      {/* Plus Button to Add More Inputs */}
      <Button type='dashed' onClick={addInput} icon={<PlusOutlined />}>
        Thêm video
      </Button>
    </>
  )
}

export default DynamicVideoInput

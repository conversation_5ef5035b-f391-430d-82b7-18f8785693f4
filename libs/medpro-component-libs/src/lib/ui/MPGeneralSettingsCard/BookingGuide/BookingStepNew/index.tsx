import React from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import { get, isArray } from 'lodash'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputItems'
import styles from '../styles.module.less'

export interface Props {
  loading?: boolean
  contentModal?: any
  isOpenNew: boolean
  setIsOpenNew: any
  onSubmitCreate: (values: any, cancelModal?: any) => void
  uploadProps: any
}

const BookingStepNew = ({
  loading,
  contentModal,
  isOpenNew,
  setIsOpenNew,
  onSubmitCreate,
  uploadProps
}: Props) => {
  const [form] = Form.useForm()

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onCancel = () => {
    setIsOpenNew(!isOpenNew)
  }

  const onFinish = (values: any) => {
    return onSubmitCreate(
      { ...values, action: 'create', locale: 'vi', type: 'guide-booking' },
      onCancel
    )
  }

  return (
    <Modal {...contentModal} open={isOpenNew} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems(form, uploadProps, onChangeImage)
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div key={rowIndex} className={styles['inputRow']}>
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Hủy
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}
export default BookingStepNew

import { Form, Input, InputNumber } from 'antd'
import React from 'react'
import { Valid } from '../../../../helpers/valid'
import MPImageFormItemMultiple from '../../../../atoms/MPImageFormItemMultiple'
import MPCKEditor from '../../../../atoms/MPCKEditor'

const valid = new Valid()
export const inputItems = (form: any, uploadProps: any, onChangeImage: (key: any, value: any) => void) => {
  return [
    {
      id: 'name',
      type: 'text',
      label: 'Tên bước',
      placeholder: '<PERSON>ui lòng nhập tên bước',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'stepName',
      type: 'text',
      label: 'Tên hướng dẫn',
      placeholder: '<PERSON>ui lòng nhập tên hướng dẫn',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'sortOrder',
      type: 'text',
      label: 'Thứ tự',
      placeholder: 'Vui lòng nhập thứ tự',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.numberRequired, required: require }]}
          >
            <InputNumber type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'content',
      type: 'text',
      label: 'Nội dung',
      placeholder: 'Nhập nội dung',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            {uploadProps && (
              <MPCKEditor
                uploadProps={uploadProps}
                value={form.getFieldValue(id)}
                onChange={(data: any) => {
                  form.setFieldValue(id, data)
                }}
              />
            )}
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'imageUrl',
      type: 'upload',
      label: 'Hình ảnh',
      placeholder: '',
      require: false,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
  ]
}

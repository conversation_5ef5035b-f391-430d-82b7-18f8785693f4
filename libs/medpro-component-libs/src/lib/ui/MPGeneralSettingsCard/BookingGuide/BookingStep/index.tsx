import { Form, Modal } from 'antd'
import React, { useEffect } from 'react'
import cx from 'classnames'
import { get, isArray } from 'lodash'
import MPButton from '../../../../atoms/MPButton'
import { ckInputItems, inputItems } from './inputItems'
import styles from './styles.module.less'

interface Props {
  data: any
  loading?: boolean
  uploadProps: any
  onHandleBookingGuideAction: (values: any) => void
}

const BookingStep = ({
  data,
  loading,
  uploadProps,
  onHandleBookingGuideAction
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    form.setFieldsValue(data)
  }, [data, form])

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onPressDelete = () => {
    Modal.confirm({
      title: '<PERSON><PERSON>c nhận xóa!',
      content: `<PERSON>ạn có chắc chắn muốn xóa ${data.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onHandleBookingGuideAction({ id: data._id, action: 'delete' })
      }
    })
  }

  const onFinish = (values: any) => {
    onHandleBookingGuideAction({
      ...values,
      _id: data?._id,
      id: data?.id,
      partnerId: data?.partnerId,
      locale: data?.locale,
      type: data?.type,
      action: 'update'
    })
  }

  return (
    <div className={styles['bookingStepWrapper']}>
      <div className={styles['header']}>
        <div className={styles['title']}>Thông tin quy trình</div>
        <div>
          <MPButton
            onClick={onPressDelete}
            typeCustom={'remove'}
            loading={loading}
          >
            Xóa
          </MPButton>
        </div>
      </div>
      <div className={styles['body']}>
        <Form
          layout='vertical'
          onFinish={onFinish}
          className={cx(styles['formGroupWrapper'], styles['two'])}
          form={form}
        >
          <div className={styles['inputItems']}>
            <div className={styles['formLeft']}>
              {inputItems(form, uploadProps, onChangeImage)
                .reduce((rows: any, item: any, index: number, array: any) => {
                  if (!item.group) {
                    rows.push([item])
                  } else if (index % 2 === 0) {
                    rows.push(array.slice(index, index + 2))
                  }
                  return rows
                }, [])
                .map((row: any, rowIndex: number) => (
                  <div key={rowIndex} className={styles['inputRow']}>
                    {row.map((item: any, itemIndex: number) => (
                      <div key={itemIndex} className={styles['inputItem']}>
                        {typeof item?.enter === 'function'
                          ? item.enter(item)
                          : null}
                      </div>
                    ))}
                  </div>
                ))}
            </div>
            <div className={styles['formRight']}>
              {ckInputItems(form, uploadProps)
                .reduce((rows: any, item: any, index: number, array: any) => {
                  if (!item.group) {
                    rows.push([item])
                  } else if (index % 2 === 0) {
                    rows.push(array.slice(index, index + 2))
                  }
                  return rows
                }, [])
                .map((row: any, rowIndex: number) => (
                  <div key={rowIndex} className={styles['inputRow']}>
                    {row.map((item: any, itemIndex: number) => (
                      <div key={itemIndex} className={styles['inputItem']}>
                        {typeof item?.enter === 'function'
                          ? item.enter(item)
                          : null}
                      </div>
                    ))}
                  </div>
                ))}
            </div>
          </div>
          <div className={styles['groupAction']}>
            <Form.Item>
              <MPButton
                htmlType='submit'
                typeCustom={'approval'}
                loading={loading}
              >
                Lưu
              </MPButton>
            </Form.Item>
          </div>
        </Form>
      </div>
    </div>
  )
}

export default BookingStep

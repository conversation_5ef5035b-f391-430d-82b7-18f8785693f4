.bookingStepWrapper {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-weight: 600;
      font-size: 1.1rem;
    }
  }

  .body {
    margin-top: 5px;
    padding-top: 15px;
    border-top: 1px solid #b2b2b2;

    .formGroupWrapper {
      height: 100%;

      &.two {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .inputRow {
          display: flex;
          gap: 10px;
          margin-bottom: 10px;

          @media only screen and (max-width: 768px) {
            flex-direction: column;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .inputItem {
            width: 100%;

            :global {
              .ant-form-item {
                margin-bottom: 10px;
              }
            }

            .moreVideo {
              position: relative;

              .btnRemoveInput {
                position: absolute;
                right: 0;
                height: 100%;
              }
            }
          }
        }
      }

      .inputItems {
        display: flex;
        gap: 10px;

        @media only screen and (max-width: 991px) {
          flex-direction: column;
        }

        .formLeft {
          flex: 1;
        }

        .formRight {
          flex: 2;
          width: 70%;

          @media only screen and (max-width: 991px) {
            width: 100%;
          }
        }
      }

      .tooltipInputItem {
        display: flex;
        gap: 5px;
        align-items: center;

        svg {
          cursor: pointer;

          path {
            fill: #3498db;
          }
        }
      }

      .groupAction {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 10px;

        :global {
          .ant-form-item {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

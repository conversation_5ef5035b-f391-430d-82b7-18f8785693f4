import React, { useCallback, useEffect, useState } from 'react'
import { Input, Tabs } from 'antd'
import { debounce, size } from 'lodash'
import Image from 'next/image'
import MPButton from '../../../atoms/MPButton'
import BookingStep from './BookingStep'
import IconTableEmpty from '../../../images/iconTableEmpty.png'
import BookingStepNew from './BookingStepNew'
import styles from './styles.module.less'

const contentModal = {
  create: {
    title: 'Thêm bước hướng dẫn',
    centered: true,
    footer: false,
    className: styles['modalNewWrapper'],
    width: '100%'
  }
}

export interface Props {
  loading?: boolean
  bookingGuideList?: any
  dataTab?: any
  uploadProps: any
  onHandleBookingGuideAction: (values: any, cancelModal?: any) => void
  debouncedFetchUpdateTab: (values: any) => void
}

const BookingGuide = ({
  loading,
  bookingGuideList,
  dataTab,
  uploadProps,
  onHandleBookingGuideAction,
  debouncedFetchUpdateTab
}: Props) => {
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [dataTabText, setDataTabText] = useState(undefined)

  const debouncedHandleChangeTabText = useCallback(
    debounce((value: string) => {
      debouncedFetchUpdateTab({
        tabTitle: value,
        tabId: dataTab?._id
      })
    }, 1000),
    []
  )

  useEffect(() => {
    setDataTabText(dataTab?.title)
  }, [dataTab])

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const handleChangeTabText = (e: any) => {
    setDataTabText(e.target.value)
    debouncedHandleChangeTabText(e.target.value)
  }

  const renderItem = () => {
    let resultArr: any[] = []
    size(bookingGuideList) > 0 &&
      bookingGuideList.map((item: any) => {
        const el = {
          label: item.stepName,
          key: item.id,
          children: (
            <BookingStep
              loading={loading}
              data={item}
              uploadProps={uploadProps}
              onHandleBookingGuideAction={onHandleBookingGuideAction}
            />
          )
        }
        return (resultArr = [...resultArr, el])
      })
    return resultArr
  }

  return (
    <>
      <div className={styles['contentWrapper']}>
        <div className={styles['inputTabWrapper']}>
          <div className={styles['label']}>Tiêu đề</div>
          <Input
            placeholder='Vui lòng nhập tiêu đề'
            value={dataTabText}
            onChange={handleChangeTabText}
            className={styles['input']}
          />
        </div>
        <div className={styles['titleWrapper']}>
          <div className={styles['label']}>Nội dung</div>
        </div>
        <div className={styles['header']}>
          <MPButton onClick={onPressNew} typeCustom={'primary'}>
            Thêm bước
          </MPButton>
        </div>
        <div className={styles['body']}>
          {size(bookingGuideList) > 0 ? (
            <Tabs
              defaultActiveKey='1'
              className={styles['tabs']}
              tabPosition='left'
              items={renderItem()}
            />
          ) : (
            <div className={styles['emptyWrapper']}>
              <Image src={IconTableEmpty} alt={''} priority />
              <span>Không có dữ liệu</span>
            </div>
          )}
        </div>
      </div>
      {isOpenNew && (
        <BookingStepNew
          loading={loading}
          contentModal={contentModal.create}
          isOpenNew={isOpenNew}
          setIsOpenNew={setIsOpenNew}
          onSubmitCreate={onHandleBookingGuideAction}
          uploadProps={uploadProps}
        />
      )}
    </>
  )
}

export default BookingGuide

.contentWrapper {
  position: relative;
  padding-right: 5px;

  .inputTabWrapper {
    display: flex;
    flex-direction: column;

    .label {
      display: flex;
      height: 35px;
      font-weight: 500;
      position: relative;
      align-items: center;
      max-width: 100%;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      padding: 0 0 8px;

      &::after {
        display: inline-block;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
        margin-left: 5px !important;
      }
    }

    .input {
      box-sizing: border-box;
      margin: 0;
      padding: 4px 11px;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      list-style: none;
      position: relative;
      display: inline-block;
      width: 100%;
      min-width: 0;
      transition: all 0.2s;
      max-width: 300px;
      border-radius: 8px;
      min-height: 38px;
    }
  }

  .titleWrapper {
    margin-top: 10px;

    .label {
      display: flex;
      height: 35px;
      font-weight: 500;
      position: relative;
      align-items: center;
      max-width: 100%;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      padding: 0 0 8px;
    }
  }

  .separate {
    border-bottom: 1px solid #cbcbcb;
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .header {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .body {

    :global {
      .ant-tabs {
        .ant-tabs-tab.ant-tabs-tab-active {
          border-radius: 0 !important;
          border: none !important;
        }
      }
    }

    .emptyWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
      min-height: 650px;
    }
  }
}

.modalNewWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 999;
      padding-top: 5px;
      padding-bottom: 5px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

import React, { useEffect } from 'react'
import { Collapse, CollapseProps, Form } from 'antd'
import cx from 'classnames'
import { MPButton } from '../../../atoms/MPButton'
import { generalInputItems } from './inputItems'
import { get, isArray } from 'lodash'
import styles from '../styles.module.less'

export interface Props {
  data?: any
  handleSubmit?: (values: any) => void
  provinceList?: any
  districtList?: any
  wardList?: any
  partnerId?: any
  uploadProps?: any
  loading: any
  onChangeCity: (value: any) => void
  onChangeDistrict: (value: any) => void
  onFormSubmit: (values: any) => void
}

const BasicInformation = ({
  data,
  provinceList,
  districtList,
  wardList,
  uploadProps,
  onChangeCity,
  onChangeDistrict,
  onFormSubmit,
  loading
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data,
        newHospitalTypes: data?.newHospitalTypes
          ? data.newHospitalTypes.map((num: any) => num.toString())
          : [],
        description: data?.websiteInformation?.description
      })
    }
  }, [data, form])

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onFinish = (values: any) => {
    onFormSubmit(values)
  }

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chung',
      collapsible: 'disabled',
      showArrow: false,
      children: generalInputItems(
        form,
        provinceList,
        districtList,
        wardList,
        onChangeCity,
        onChangeDistrict,
        uploadProps,
        onChangeImage
      )
        .reduce((rows: any, item: any, index: number, array: any) => {
          if (!item.group) {
            rows.push([item])
          } else if (index % 2 === 0) {
            rows.push(array.slice(index, index + 2))
          }
          return rows
        }, [])
        .map((row: any, rowIndex: number) => (
          <div key={rowIndex} className={styles['inputRow']}>
            {row.map((item: any, itemIndex: number) => (
              <div key={itemIndex} className={styles['inputItem']}>
                {typeof item?.enter === 'function' ? item.enter(item) : null}
              </div>
            ))}
          </div>
        ))
    }
  ]

  return (
    <Form
      form={form}
      onFinish={onFinish}
      scrollToFirstError={{ behavior: 'smooth' }}
      layout={'vertical'}
      className={cx(styles['formGroupWrapper'], styles['two'])}
    >
      <Collapse
        items={items}
        defaultActiveKey={['1']}
        expandIconPosition={'end'}
      />
      <div className={styles['groupAction']}>
        <Form.Item>
          <MPButton
            loading={loading}
            htmlType='submit'
            typeCustom={'approval'}
          >
            Lưu
          </MPButton>
        </Form.Item>
      </div>
    </Form>
  )
}

export default BasicInformation

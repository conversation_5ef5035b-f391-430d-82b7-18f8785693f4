import { Form, Input, Select } from 'antd'
import { size } from 'lodash'
import React from 'react'
import { Valid } from '../../../helpers/valid'
import MPCKEditor from '../../../atoms/MPCKEditor'
import MPImageFormItemMultiple from '../../../atoms/MPImageFormItemMultiple'
import { hospitalType } from './data'

const valid = new Valid()
const { Option } = Select

export const generalInputItems = (
  form: any,
  provinceList: any,
  districtList: any,
  wardList: any,
  onChangeCity: (value: any) => void,
  onChangeDistrict: (value: any) => void,
  uploadProps: any,
  onChangeImage: (key: any, value: any) => void
) => {
  const onPressChangeCity = (value: any, form: any) => {
    onChangeCity(value)
    form.setFieldsValue({ district_id: null })
  }

  const onPressChangeDistrict = (value: any, form: any) => {
    onChangeDistrict(value)
    form.setFieldsValue({ ward_id: null })
  }

  return [
    {
      id: 'circleLogo',
      type: 'upload',
      label: 'Logo CSYT',
      placeholder: '',
      require: false,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'name',
      type: 'text',
      label: 'Tên cơ sở y tế',
      placeholder: 'Vui lòng nhập tên cơ sở y tế (có dấu) ...',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'newHospitalTypes',
      type: 'text',
      label: 'Loại hình cơ sở y tế',
      placeholder: 'Chọn loại hình bệnh viện ...',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id} rules={[{ required: require }]}>
            <Select
              mode={'tags'}
              placeholder={placeholder}
              options={hospitalType}
              optionFilterProp='children'
              filterOption={(input: string, option: any) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'city_id',
      type: 'select',
      label: 'Tỉnh/Thành phố',
      placeholder: 'Nhập tỉnh / thành phố',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ required: require, validator: valid.province }]}
          >
            <Select
              showSearch={true}
              placeholder={placeholder}
              filterOption={(input: any, option: any) =>
                (option?.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={(value: any) => onPressChangeCity(value, form)}
            >
              {provinceList && size(provinceList?.data) > 0
                ? provinceList?.data?.map((item: any, index: number) => {
                    return (
                      <Option key={index} value={item.id}>
                        {item.name}
                      </Option>
                    )
                  })
                : undefined}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'district_id',
      type: 'select',
      label: 'Quận/Huyện',
      placeholder: 'Nhập Quận/Huyện',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ required: require, validator: valid.district }]}
          >
            <Select
              placeholder={placeholder}
              filterOption={(input: any, option: any) =>
                (option?.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={(value: any) => onPressChangeDistrict(value, form)}
            >
              {districtList && size(districtList?.data) > 0
                ? districtList?.data?.map((item: any, index: number) => (
                    <Option key={index} value={item.id}>
                      {item.name || ''}
                    </Option>
                  ))
                : undefined}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'ward_id',
      type: 'select',
      label: 'Phường/Xã',
      placeholder: 'Nhập Phường/Xã',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ required: require, validator: valid.ward }]}
          >
            <Select placeholder={placeholder}>
              {wardList && size(wardList?.data) > 0
                ? wardList?.data?.map((item: any, index: number) => (
                    <Option key={index} value={item.id}>
                      {item.name}
                    </Option>
                  ))
                : undefined}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'address',
      type: 'text',
      label: 'Địa chỉ',
      placeholder: 'Chỉ nhập số nhà, tên đường, ấp thôn xóm,...',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.address, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'workingTime',
      type: 'text',
      label: 'Thời gian làm việc',
      placeholder: 'Ví dụ: Từ thứ 2 đến thứ 7',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'addressOther',
      type: 'text',
      label: 'Địa chỉ (Khác)',
      placeholder: 'Chỉ nhập số nhà, tên đường, ấp thôn xóm,...',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'website',
      type: 'text',
      label: 'Website',
      placeholder: 'Ví dụ: https://www.domain.vn',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'hotline',
      type: 'text',
      label: 'Hotline',
      placeholder: 'Ví dụ: 1900 2115',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    { group: true },
    {
      id: 'description',
      type: 'text',
      label: 'Mô tả ngắn',
      placeholder: 'Nhập mô tả ngắn',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            {uploadProps && (
              <MPCKEditor
                uploadProps={uploadProps}
                value={form.getFieldValue(id)}
                onChange={(data: any) => {
                  form.setFieldsValue({ [id]: data })
                }}
              />
            )}
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

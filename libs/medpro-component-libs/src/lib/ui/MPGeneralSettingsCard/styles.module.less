.pageWrapper {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: white;
    z-index: 999;
    padding: 10px 0;

    @media only screen and (max-width: 768px) {
      flex-wrap: wrap;
      gap: 10px;
    }

    .title {
      display: flex;
      align-items: center;

      :global {
        .ant-ribbon {
          margin-top: -30px;
        }
      }

      h2 {
        font-size: 20px;
      }
    }

    .actionHeader {
      .inputItem {
        display: flex;
        align-items: center;
        gap: 8px;

        :global {
          .ant-select {
            width: 300px;
          }
        }

        .label {
          font-weight: 500;
        }
      }
    }
  }

  .body {
    :global {
      .ant-collapse {
        .ant-collapse-item {
          border-radius: 0;
          background-color: #9da3b1;

          &:first-child {
            border-radius: 0;
          }

          &:last-child > .ant-collapse-content {
            border-radius: 0;
          }

          .ant-collapse-expand-icon {
            svg {
              path {
                fill: #fff;
              }
            }
          }

          .ant-collapse-header-text {
            color: #fff;
          }
        }
      }
    }

    .bigTabWrapper {
      :global {
        .ant-tabs
        .ant-tabs-nav
        .ant-tabs-nav-wrap
        .ant-tabs-nav-list
        .ant-tabs-tab-active {
          font-weight: 400;
        }

        .ant-tabs-nav {
          margin: 0;
        }
      }
    }

    .compTabWrapper {
      margin-top: 5px;

      :global {
        .ant-tabs-nav-list {
          .ant-tabs-tab {
            border-radius: 0;
            background: transparent;
            border: 0;

            &.ant-tabs-tab-active {
              border-radius: 8px 8px 0 0;
              border: 1px solid #f0f0f0;
              border-bottom-color: #fff !important;
            }
          }
        }
      }
    }
  }
}

.formGroupWrapper {
  &.two {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .inputRow {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;

      @media only screen and (max-width: 768px) {
        flex-direction: column;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .inputItem {
        width: 100%;

        :global {
          .ant-form-item {
            margin-bottom: 10px;
          }
        }

        .moreVideo {
          position: relative;

          .btnRemoveInput {
            position: absolute;
            right: 0;
            height: 100%;
          }
        }
      }
    }
  }

  .groupAction {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 999;
    padding-top: 5px;
    padding-bottom: 5px;

    :global {
      .ant-form-item {
        margin-bottom: 0;
      }
    }

    .btnEdit {
      background: #ffb54a;
      color: #fff;

      &:hover {
        border-color: #ffb54a;
        background: #ffb54a;
        color: #fff;
      }
    }
  }
}

@keyframes fadeInOut {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

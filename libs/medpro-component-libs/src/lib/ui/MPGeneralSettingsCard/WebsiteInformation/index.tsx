import React, { useMemo, useState } from 'react'
import { Tabs } from 'antd'
import PriceList from '../PriceList'
import BookingGuide from '../BookingGuide'
import CollapseTabs from '../CollapseTabs'
import Introduce from '../Introduce'
import Benefit from '../Benefit'
import styles from '../styles.module.less'
import Specialty from '../Specialty'

export interface Props {
  loading?: boolean
  heightContent?: number
  partnerAnchorInfo?: any
  specialtyList?: any
  hospitalInfo?: any
  bookingGuideList?: any
  onHandlePriceListAction: (values: any) => void
  onHandleIntroduceAction: (values: any) => void
  onHandleBenefitAction: (values: any) => void
  onHandleBookingGuideAction: (values: any, cancelModal?: any) => void
  priceList: any
  uploadProps: any
  debouncedFetchUpdateTab: (values: any) => void
  onHandleChangeTab: (key: string, level: any) => void
  dataQuestion: any
  faqCategories: any
  onHandleQuestionAction: (values: any) => void
  onHandleActionSpecialty: (values: any) => void
}

const WebsiteInformation = ({
  loading,
  partnerAnchorInfo,
  specialtyList,
  heightContent,
  hospitalInfo,
  bookingGuideList,
  onHandlePriceListAction,
  onHandleIntroduceAction,
  onHandleBenefitAction,
  onHandleBookingGuideAction,
  priceList,
  uploadProps,
  debouncedFetchUpdateTab,
  onHandleChangeTab,
  dataQuestion,
  faqCategories,
  onHandleQuestionAction,
  onHandleActionSpecialty
}: Props) => {
  const [activeKey, setActiveKey] = useState<any>(undefined)
  const items = useMemo(() => {
    return partnerAnchorInfo.length
      ? partnerAnchorInfo.map((tab: any, index: number) => {
          let component
          switch (tab.href) {
            case '#gioi-thieu':
              component = (
                <Introduce
                  dataTab={tab}
                  loading={loading}
                  uploadProps={uploadProps}
                  data={hospitalInfo}
                  onHandleIntroduceAction={onHandleIntroduceAction}
                />
              )
              break
            case '#chuyen-khoa':
              component = (
                <Specialty
                  dataTab={tab}
                  specialtyList={specialtyList}
                  uploadProps={uploadProps}
                  heightContent={heightContent}
                  onHandleAction={onHandlePriceListAction}
                  priceList={priceList}
                  debouncedFetchUpdateTab={debouncedFetchUpdateTab}
                  onHandleActionSpecialty={onHandleActionSpecialty}
                  loading={loading}
                />
              )
              break
            case '#loi-ich':
              component = (
                <Benefit
                  dataTab={tab}
                  loading={loading}
                  uploadProps={uploadProps}
                  data={hospitalInfo}
                  onHandleBenefitAction={onHandleBenefitAction}
                />
              )
              break
            case '#bang-gia':
              component = (
                <PriceList
                  dataTab={tab}
                  heightContent={heightContent}
                  onHandleAction={onHandlePriceListAction}
                  priceList={priceList}
                  debouncedFetchUpdateTab={debouncedFetchUpdateTab}
                  loading={loading}
                />
              )
              break
            case '#huong-dan':
              component = (
                <BookingGuide
                  dataTab={tab}
                  loading={loading}
                  uploadProps={uploadProps}
                  bookingGuideList={bookingGuideList}
                  onHandleBookingGuideAction={onHandleBookingGuideAction}
                  debouncedFetchUpdateTab={debouncedFetchUpdateTab}
                />
              )
              break
            case '#cau-hoi':
              component = (
                <div className={styles['faqWrapper']}>
                  <CollapseTabs
                    dataTab={tab}
                    dataQuestion={dataQuestion}
                    uploadProps={uploadProps}
                    onHandleQuestionAction={onHandleQuestionAction}
                    faqCategories={faqCategories}
                    debouncedFetchUpdateTab={debouncedFetchUpdateTab}
                    // handleChangeQuestion
                    // uploadToCloudinary={uploadToCloudinary}
                  />
                </div>
              )
              break
            default:
              component = null
          }

          return {
            key: tab.href,
            label: tab.tabName,
            forceRender: true,
            children: component,
            closable: false
          }
        })
      : []
  }, [
    bookingGuideList,
    dataQuestion,
    debouncedFetchUpdateTab,
    faqCategories,
    heightContent,
    hospitalInfo,
    loading,
    onHandleActionSpecialty,
    onHandleBenefitAction,
    onHandleBookingGuideAction,
    onHandleIntroduceAction,
    onHandlePriceListAction,
    onHandleQuestionAction,
    partnerAnchorInfo,
    priceList,
    specialtyList,
    uploadProps
  ])

  return (
    <div className={styles['compTabWrapper']}>
      <Tabs
        activeKey={activeKey}
        onChange={(key) => {
          setActiveKey(key)
          onHandleChangeTab(key, 'moduleAnchorTab')
        }}
        tabPosition={'top'}
        items={items}
        type={'editable-card'}
      />
    </div>
  )
}

export default WebsiteInformation

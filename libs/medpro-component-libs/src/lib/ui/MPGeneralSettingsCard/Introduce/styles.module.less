.contentWrapper {
  position: relative;
  padding-right: 5px;

  .formGroupWrapper {
    &.two {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 999;
      padding-top: 5px;
      padding-bottom: 5px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }

      .btnEdit {
        background: #FFB54A;
        color: #fff;

        &:hover {
          border-color: #FFB54A;
          background: #FFB54A;
          color: #fff;
        }
      }
    }
  }
}

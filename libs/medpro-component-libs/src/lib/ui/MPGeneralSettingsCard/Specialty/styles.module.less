.priceListCard {
  position: relative;
  padding-right: 5px;

  .inputTabWrapper {
    display: flex;
    flex-direction: column;

    .label {
      display: flex;
      height: 35px;
      font-weight: 500;
      position: relative;
      align-items: center;
      max-width: 100%;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      padding: 0 0 8px;

      &::after {
        display: inline-block;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
        margin-left: 5px !important;
      }
    }

    .input {
      box-sizing: border-box;
      margin: 0;
      padding: 4px 11px;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      list-style: none;
      position: relative;
      display: inline-block;
      width: 100%;
      min-width: 0;
      transition: all 0.2s;
      max-width: 300px;
      border-radius: 8px;
      min-height: 38px;
    }
  }

  .titleWrapper {
    margin-top: 10px;

    .label {
      display: flex;
      height: 35px;
      font-weight: 500;
      position: relative;
      align-items: center;
      max-width: 100%;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      padding: 0 0 8px;
    }
  }

  .searchContainer {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .searchInput {
      width: 100%;
      margin-right: 10px;
      border-radius: 5px;
      padding: 5px 10px;
      height: 40px !important;
    }

    .btnAdd {
      background-color: #4dbefb;
      color: #ffffff;
      border-radius: 5px;
      padding: 5px 15px;
      height: 40px !important;
      cursor: pointer;

      &:hover,
      &:focus,
      &:active {
        background-color: #4dbefb;
        color: #ffffff;
      }
    }
  }
}

.modalFormWrapper {
  .formGroupWrapper {
    &.two {
      display: flex;
      flex-direction: column;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &.hidden {
          display: none !important;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 999;
      padding-top: 5px;
      padding-bottom: 5px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }

      .btnEdit {
        background: #ffb54a;
        color: #fff;

        &:hover {
          border-color: #ffb54a;
          background: #ffb54a;
          color: #fff;
        }
      }
    }
  }
}

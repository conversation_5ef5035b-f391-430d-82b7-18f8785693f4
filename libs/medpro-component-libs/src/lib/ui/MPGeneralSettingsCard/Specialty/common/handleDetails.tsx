import { Form, Input, InputNumber, Switch } from 'antd'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import TextArea from 'antd/es/input/TextArea'
import { MPImageFormItemMultiple } from '@medpro-libs/medpro-component-libs'
import { Valid } from '../../../../helpers/valid'
import styles from './../styles.module.less'

const valid = new Valid()

export const handleDetails = (
  uploadProps: any,
  form: any,
  onChangeImage: any
) => {
  return [
    {
      id: 'id',
      type: 'text',
      enter: ({ id, hidden }: any) => {
        return (
          <Form.Item
            name={id}
            valuePropName='checked'
            className={styles['formInputItem']}
            hidden={hidden}
          />
        )
      },
      hidden: true,
      group: false
    },
    {
      id: 'name',
      type: 'text',
      label: 'Tên chuyên khoa',
      placeholder: 'Nhập tên chuyên khoa',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'icon',
      type: 'image',
      label: 'Icon',
      placeholder: 'Nhập icon',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            name={id}
            label={label}
            valuePropName={'url'}
            extra=''
            rules={[{ validator: valid.required, required: require }]}
          >
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'status',
      type: 'switch',
      label: 'Hiển thị chuyên khoa',
      placeholder: '',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id} valuePropName='checked'>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

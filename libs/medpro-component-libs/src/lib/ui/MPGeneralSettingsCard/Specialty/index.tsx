import { CheckOutlined, CloseOutlined, SearchOutlined } from '@ant-design/icons'
import { Button, Form, Input, Modal, Switch } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import { Image as AntdImage } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import cx from 'classnames'
import { useDynamicContentHeight } from '@medpro-libs/medpro-component-libs'
import { debounce, get, isArray } from 'lodash'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { MdAdd } from 'react-icons/md'
import { normalizeTable } from '../../../helpers/table'
import { handleDetails } from './common/handleDetails'
import MPTable from '../../../atoms/MPTable'
import MPButton from '../../../atoms/MPButton'
import styles from './styles.module.less'

interface Props {
  onHandleAction: (values: any) => void
  uploadProps: any
  debouncedFetchUpdateTab: (values: any) => void
  onHandleActionSpecialty: (values: any) => void
  priceList: any
  specialtyList?: any
  dataTab?: any
  heightContent?: number
  loading?: boolean
}

const Specialty = (props: Props) => {
  const [form] = useForm()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [dataTabText, setDataTabText] = useState(undefined)
  const [editingItem, setEditingItem] = useState<string>('')
  const [dataSource, setDataSource] = useState<any[]>([])
  const [searchText, setSearchText] = useState('')
  const [filteredData, setFilteredData] = useState<any>([])
  const headerRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent } = useDynamicContentHeight(
    [headerRef.current],
    {
      key: 'table',
      heightContent: props?.heightContent ? props?.heightContent - 145 : 0
    }
  )

  const debouncedHandleChangeTabText = useCallback(
    debounce((value: string) => {
      props.debouncedFetchUpdateTab({
        tabTitle: value,
        tabId: props?.dataTab?._id
      })
    }, 1000),
    []
  )

  useEffect(() => {
    setDataSource(props?.specialtyList)
  }, [props?.specialtyList])

  useEffect(() => {
    setDataTabText(props?.dataTab?.title)
  }, [props?.dataTab])

  useEffect(() => {
    const dataSourceWithKey = dataSource.length
      ? dataSource.map((item, index) => ({
          ...item,
          key: item._id || index
        }))
      : []

    if (searchText) {
      const filtered = dataSourceWithKey.filter((item) =>
        item?.name.toLowerCase().includes(searchText.toLowerCase())
      )
      setFilteredData(filtered)
    } else {
      setFilteredData(dataSourceWithKey)
    }
  }, [searchText, dataSource])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value)
  }

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return props.onHandleActionSpecialty({
          ...row,
          action: 'delete'
        })
      }
    })
  }

  const onPressEdit = (row: any) => {
    setEditingItem('edit')
    form.setFieldsValue({
      ...row
    })
    setIsModalVisible(true)
  }

  const handleVisibilityChange = (checked: boolean, record: any) => {
    const newData = [...dataSource]
    const index = newData.findIndex((item) => item.order === record.order)
    if (index > -1) {
      newData[index].status = checked
      setDataSource(newData)
    }
  }

  const handleCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  const handleOk = (values: any) => {
    if (editingItem === 'edit') {
      props.onHandleActionSpecialty({
        ...values,
        status: values?.status,
        action: 'edit'
      })
    } else {
      props.onHandleActionSpecialty({
        ...values,
        status: values?.status || false,
        action: 'create'
      })
    }
    setIsModalVisible(false)
    form.resetFields()
  }

  const handleCreate = () => {
    form.resetFields()
    setEditingItem('')
    setIsModalVisible(true)
  }

  const handleChangeTabText = (e: any) => {
    setDataTabText(e.target.value)
    debouncedHandleChangeTabText(e.target.value)
  }
  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }
  const columns: ColumnsType<any> = normalizeTable(
    [
      {
        title: 'Icon',
        dataIndex: 'icon',
        width: 200,
        align: 'left',
        render: (icon) => (
          <AntdImage
            src={icon}
            width={50}
            alt='icon'
            height={50}
            style={{ objectFit: 'cover' }}
          />
        )
      },
      {
        title: 'Tên chuyên khoa',
        dataIndex: 'name',
        width: 300,
        align: 'left'
      },

      {
        title: 'Hiển thị',
        dataIndex: 'status',
        width: 100,
        align: 'left',
        render: (status, record) => (
          <Switch
            disabled
            checked={status !== false}
            onChange={(checked) => handleVisibilityChange(checked, record)}
            checkedChildren={<CheckOutlined />}
            unCheckedChildren={<CloseOutlined />}
          />
        )
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  return (
    <div className={styles['priceListCard']}>
      <div ref={headerRef} style={{ marginBottom: 16 }}>
        <div className={styles['inputTabWrapper']}>
          <div className={styles['label']}>Tiêu đề</div>
          <Input
            placeholder='Vui lòng nhập tiêu đề'
            value={dataTabText}
            onChange={handleChangeTabText}
            className={styles['input']}
          />
        </div>
        <div className={styles['titleWrapper']}>
          <div className={styles['label']}>Nội dung</div>
        </div>
        <div className={styles['searchContainer']}>
          <Input
            placeholder='Tìm kiếm theo tên chuyên khoa'
            value={searchText}
            onChange={handleSearch}
            prefix={<SearchOutlined />}
            style={{ width: 300 }}
          />
          <MPButton
            typeCustom={'primary'}
            icon={<MdAdd />}
            onClick={handleCreate}
          >
            <span>Thêm</span>
          </MPButton>
        </div>
      </div>
      <MPTable
        columns={columns}
        dataSource={filteredData}
        dynHeightContent={dynHeightContent}
        heightTbBody={dynHeightContent - 55}
        loading={props.loading}
        pagination={false}
        // onChangePageEvent={onChangePageEvent}
        // onChangeSizeEvent={onChangeSizeEvent}
      />
      {/* Modal chỉnh sửa gói bảng giá */}
      {isModalVisible && (
        <Modal
          title={
            editingItem === 'edit'
              ? 'Chỉnh sửa chuyên khoa'
              : 'Thêm mới chuyên khoa'
          }
          open={isModalVisible}
          onCancel={handleCancel}
          className={styles['modalFormWrapper']}
          footer={null}
          width={600}
          centered={true}
        >
          <div>
            <Form
              onFinish={handleOk}
              layout='vertical'
              form={form}
              className={cx(styles['formGroupWrapper'], styles['two'])}
            >
              {handleDetails(props?.uploadProps, form, onChangeImage)
                .reduce((rows: any, item: any, index: number, array: any) => {
                  if (!item.group) {
                    rows.push([item])
                  } else if (index % 2 === 0) {
                    rows.push(array.slice(index, index + 2))
                  }
                  return rows
                }, [])
                .map((row: any, rowIndex: number) => (
                  <div
                    key={rowIndex}
                    className={cx(
                      styles['inputRow'],
                      row.every((item: any) => item.hidden)
                        ? styles['hidden']
                        : null
                    )}
                  >
                    {row.map((item: any, itemIndex: number) => (
                      <div key={itemIndex} className={styles['inputItem']}>
                        {typeof item?.enter === 'function'
                          ? item.enter(item)
                          : null}
                      </div>
                    ))}
                  </div>
                ))}
              <div className={styles['groupAction']}>
                <Form.Item>
                  <MPButton onClick={handleCancel} typeCustom={'cancel'}>
                    Đóng
                  </MPButton>
                </Form.Item>
                <Form.Item>
                  <MPButton
                    htmlType='submit'
                    typeCustom={'approval'}
                    loading={props.loading}
                  >
                    Lưu
                  </MPButton>
                </Form.Item>
              </div>
            </Form>
          </div>
        </Modal>
      )}
    </div>
  )
}

export default Specialty

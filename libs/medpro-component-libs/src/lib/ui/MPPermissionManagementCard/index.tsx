import React, { useMemo, useRef, useState } from 'react'
import {
  Modal,
  type TableColumnsType,
  TablePaginationConfig,
  Tooltip,
  Select
} from 'antd'
import cx from 'classnames'
import { AiOutlinePlusCircle, AiOutlineRedo } from 'react-icons/ai'
import dayjs from 'dayjs'
import { size } from 'lodash'
import {
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import MPTable from '../../atoms/MPTable'
import PermissionManagementDetail from './PermissionManagementDetail'
import PermissionManagementNew from './PermissionManagementNew'
import styles from './styles.module.less'

const contentModal = {
  edit: {
    title: 'Sửa thông tin permission',
    centered: true,
    footer: false,
    className: styles['modalEditWrapper']
  },
  create: {
    title: 'Thêm mới permission',
    centered: true,
    footer: false,
    className: styles['modalNewWrapper']
  }
}

export interface Props {
  heightContent: number
  permissionList: any
  permissionListFilter: any
  loading?: boolean
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

const MPPermissionManagementCard = ({
  heightContent,
  permissionList,
  permissionListFilter,
  loading,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent
    })
  const { Option } = Select
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [permissionDetail, setPermissionDetail] = useState(undefined)

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onSubmitDelete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    setIsOpenEdit(!isOpenEdit)
    setPermissionDetail(row)
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const dataSource = useMemo(() => {
    if (permissionList?.length) {
      return permissionList.map((item: any, index: number) => {
        return {
          ...item,
          key: item.id || index,
          createdAt: dayjs(item.createdAt).format('DD-MM-YYYY, HH:mm'),
          updatedAt: dayjs(item.updatedAt).format('DD-MM-YYYY, HH:mm')
        }
      })
    }
    return []
  }, [permissionList])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'Tên quyền',
        width: 250,
        dataIndex: 'name',
        key: 'name',
        fixed: 'left'
      },
      {
        title: 'Mô tả',
        dataIndex: 'description',
        key: 'description',
        width: 200
      },
      {
        title: 'Ngày tạo',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 180
      },
      {
        title: 'Ngày cập nhật',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        width: 180
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  return (
    <>
      <div className={styles['pageWrapper']}>
        <div ref={headerRef} className={styles['header']}>
          <div className={styles['title']}>
            <h2>Quản lý permission</h2>
          </div>
        </div>
        <div className={styles['body']}>
          <div ref={filterRef} className={styles['filterWrapper']}>
            <div className={styles['left']}>
              {size(permissionListFilter) > 0 && (
                <Select
                  className={styles['select']}
                  allowClear
                  showSearch
                  placeholder='Lọc theo module'
                  style={{ width: 200 }}
                  onChange={(e: any) => onSubmitSearch(e)}
                >
                  {permissionListFilter?.map((item: any) => (
                    <Option key={item} value={item} children={item} />
                  ))}
                </Select>
              )}
            </div>
            <div className={styles['right']}>
              <div className={styles['enhance']}>
                <Tooltip title='Thêm mới' placement='bottom' color={'#44474e'}>
                  <div
                    onClick={onPressNew}
                    className={cx(styles['btn'], styles['btnAdd'])}
                  >
                    <AiOutlinePlusCircle />
                  </div>
                </Tooltip>
                <Tooltip title='Làm mới' placement='bottom' color={'#44474e'}>
                  <div
                    onClick={() => onRefresh()}
                    className={cx(styles['btn'], styles['btnRefresh'])}
                  >
                    <AiOutlineRedo />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
          <MPTable
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        </div>
      </div>
      {isOpenNew && (
        <PermissionManagementNew
          loading={loading}
          contentModal={contentModal.create}
          isOpenNew={isOpenNew}
          setIsOpenNew={setIsOpenNew}
          onSubmitCreate={onSubmitCreate}
        />
      )}
      {isOpenEdit && (
        <PermissionManagementDetail
          loading={loading}
          contentModal={contentModal.edit}
          isOpenEdit={isOpenEdit}
          setIsOpenEdit={setIsOpenEdit}
          data={permissionDetail}
          onSubmitUpdate={onSubmitUpdate}
        />
      )}
    </>
  )
}

export default MPPermissionManagementCard

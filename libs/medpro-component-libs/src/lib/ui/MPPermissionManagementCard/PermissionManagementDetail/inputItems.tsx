import { Form, Input } from 'antd'
import React from 'react'
import { Valid } from '../../../helpers/valid'

const valid = new Valid()
export const inputItems = (form: any) => {
  return [
    {
      id: 'name',
      type: 'text',
      label: 'Tên permission',
      placeholder: 'Vui lòng nhập tên permission',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'description',
      type: 'text',
      label: '<PERSON>ô tả',
      placeholder: 'Vui lòng nhập mô tả',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

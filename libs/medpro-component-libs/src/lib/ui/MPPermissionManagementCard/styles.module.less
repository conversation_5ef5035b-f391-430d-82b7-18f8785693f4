.pageWrapper {
  .header {
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    background: white;
    z-index: 999;
    padding: 10px 0;

    .title {
      display: flex;
      align-items: center;

      :global {
        .ant-ribbon {
          margin-top: -30px;
        }
      }

      h2 {
        font-size: 20px;
      }
    }
  }

  .body {
    margin-top: 12px;

    .on {
      background: green;
      color: #ffffff;
      text-transform: capitalize;
    }

    .off {
      background: red;
      color: #ffffff;
      text-transform: capitalize;
    }

    .filterWrapper {
      -webkit-box-align: center;
      align-items: center;
      border-color: #e7e7e7;
      background-color: #e7e7e7;
      display: flex;
      flex-wrap: wrap;
      border-radius: 10px 10px 0 0;
      padding: 10px 10px;

      .left {
        flex: 35%;
        display: flex;

        :global {
          .ant-input-search {
            max-width: 300px;

            .ant-input-outlined {
              border-color: #e7e7e7;
            }
          }
        }
      }

      .right {
        flex: 10%;
        display: flex;
        justify-content: flex-end;

        .enhance {
          display: flex;
          gap: 5px;

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            padding: 4px;
            border: 1px solid #fff;
            background: #fff;

            &:hover, &:active, &:focus {
              background: #ced5e6;
            }
          }

          .btnFilter {
            svg {
              font-size: 22px;
              color: #44474e;
            }
          }

          .btnRefresh {
            svg {
              font-size: 22px;
              color: #44474e;
            }
          }

          .btnAdd {
            svg {
              font-size: 22px;
              color: #44474e;
            }
          }
        }
      }
    }
  }
}

.modalEditWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

.modalNewWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

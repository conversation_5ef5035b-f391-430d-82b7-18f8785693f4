import React, { useMemo, useRef } from 'react'
import { Select, TablePaginationConfig, Tabs } from 'antd'
import { getReplaceUTF8, MPButton } from '@medpro-libs/medpro-component-libs'
import { size } from 'lodash'
import DoctorAppointment from './DoctorAppointment'
import ClinicBooking from './ClinicBooking'
import DoctorTeleconsultation from './DoctorTeleconsultation'
import LabTestBooking from './LabTestBooking'
import HealthCheckupPackage from './HealthCheckupPackage'
import VaccinationBooking from './VaccinationBooking'
import ExtendedHoursBooking from './ExtendedHoursBooking'
import styles from './styles.module.less'

export interface Props {
  heightContent: number
  hospitalList: any
  catListingList: any
  listingTabInfo: any
  selectedPartner: any
  setSelectedPartner: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  formSelectPartner: (value: any) => void
  onHandleChangeTab: (key: string, level: any) => void
  onDragSortTable?: (data: any) => void
  setTabActiveKey?: any
  tabActiveKey?: any
}

const MPScreenPositionCard = ({
  heightContent,
  hospitalList,
  catListingList,
  listingTabInfo,
  selectedPartner,
  setSelectedPartner,
  loading,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onHandleChangeTab,
  setTabActiveKey,
  tabActiveKey,
  formSelectPartner,
  onDragSortTable
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const { Option } = Select

  const items = () => {
    const sharedProps = {
      headerRef,
      heightContent,
      catListingList,
      loading,
      onPressViewDetail,
      onSubmitCreate,
      onSubmitUpdate,
      onSubmitDelete,
      onRefresh,
      pagination,
      onChangePageEvent,
      onChangeSizeEvent,
      onHandleChangeTab,
      formSelectPartner,
      hospitalList,
      onDragSortTable
    }

    const tabComponents: Record<string, React.ComponentType<any>> = {
      'booking.date': ClinicBooking,
      // 'booking.doctor': DoctorAppointment,
      // 'booking.telemed': DoctorTeleconsultation,
      // 'booking.covid': LabTestBooking,
      // 'booking.package': HealthCheckupPackage,
      // 'booking.vaccine': VaccinationBooking,
      // 'booking.ngoaigio': ExtendedHoursBooking,
      'search.specialities': ClinicBooking
    }

    return listingTabInfo.length
      ? listingTabInfo.map((tab: any) => {
          const Component = tabComponents[tab?.type]
          return {
            key: tab.type,
            label: tab.name,
            forceRender: true,
            children: Component ? (
              <Component {...sharedProps} tabInfo={tab} />
            ) : null,
            closable: false
          }
        })
      : []
  }

  return (
    <div className={styles['pageWrapper']}>
      <div ref={headerRef} className={styles['header']}>
        <div className={styles['title']}>
          <h2>Vị trí hiển thị</h2>
        </div>
        <div className={styles['actionWrapper']}>
          <MPButton typeCustom={'cancel'}>
            <span>Reset cache</span>
          </MPButton>
          <Select value={selectedPartner} onChange={setSelectedPartner}>
            <Option value={'medpro'}>Medpro</Option>
          </Select>
        </div>
      </div>
      <div className={styles['body']}>
        <div className={styles['bigTabWrapper']}>
          <Tabs
            activeKey={tabActiveKey}
            onChange={(key) => {
              setTabActiveKey(key)
              onHandleChangeTab(key, 'generalTab')
            }}
            tabPosition={'top'}
            items={items()}
          />
        </div>
      </div>
    </div>
  )
}

export default MPScreenPositionCard

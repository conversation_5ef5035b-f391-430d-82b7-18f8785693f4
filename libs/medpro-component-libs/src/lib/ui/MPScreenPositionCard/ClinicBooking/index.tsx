import React, { useMemo, useRef, useState } from 'react'
import {
  Modal,
  Switch,
  type TableColumnsType,
  TablePaginationConfig,
  Tabs,
  Image as ImageAntd
} from 'antd'
import { FiPlus } from 'react-icons/fi'
import dayjs from 'dayjs'
import {
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import ClinicBookingNew from './ClinicBookingNew'
import ClinicBookingDetail from './ClinicBookingDetail'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'

const contentModal = {
  edit: {
    title: 'S<PERSON>a cơ sở y tế',
    centered: true,
    footer: false,
    className: styles['modalEditWrapper'],
    width: 800
  },
  create: {
    title: 'Thêm cơ sở y tế',
    centered: true,
    footer: false,
    className: styles['modalNewWrapper'],
    width: 800
  },
  viewCTA: {
    title: 'Chi tiết nội dung',
    centered: true,
    footer: false
  }
}

export interface Props {
  headerRef: any
  heightContent: number
  hospitalList: any
  catListingList: any
  tabInfo?: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  formSelectPartner: (value: any) => void
  onHandleChangeTab: (key: string, level: any) => void
  onDragSortTable?: (data: any) => void
}

const ClinicBooking = ({
  headerRef,
  heightContent,
  hospitalList,
  catListingList,
  tabInfo,
  loading,
  onPressViewDetail,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  formSelectPartner,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onHandleChangeTab,
  onDragSortTable
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 75
    })
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [tabActiveKey, setTabActiveKey] = useState('ALL')
  const [dataRow, setDataRow] = useState(undefined)

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onSubmitDelete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    setIsOpenEdit(!isOpenEdit)
    formSelectPartner({
      partnerId: row?.partnerId
    })
    setDataRow(row)
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const dataSource = useMemo(() => {
    if (catListingList && catListingList.length) {
      return catListingList?.map((item: any, index: number) => {
        return {
          ...item,
          key: item.id || index,
          index,
          position: Number(index + 1),
          sysCreatedAt: item.createdAt,
          sysUpdatedAt: item.updatedAt,
          fmtFromTime: dayjs(item.fromDate).format('HH:mm'),
          fmtFromDate: dayjs(item.fromDate).format('DD-MM-YYYY'),
          fmtToTime: dayjs(item.toDate).format('HH:mm'),
          fmtToDate: dayjs(item.toDate).format('DD-MM-YYYY')
        }
      })
    }
    return []
  }, [catListingList])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'STT',
        width: 90,
        key: 'position',
        align: 'center',
        fixed: 'left',
        render: (row: any) => (
          <div className={styles['groupPosition']}>{row.position}</div>
        )
      },
      {
        title: 'Thông tin cơ sở y tế',
        width: 350,
        key: 'name',
        fixed: 'left',
        render: (row: any) => (
          <div className={styles['groupName']}>
            {tabActiveKey === 'ALL' ? <DragHandle /> : null}
            <div className={styles['contentName']}>
              <div className={styles['image']}>
                <ImageAntd
                  src={row.image}
                  alt={row.name}
                  width={50}
                  height={50}
                />
              </div>
              <div className={styles['text']}>{row.name}</div>
            </div>
          </div>
        )
      },
      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 200,
        render: (row: any) => (
          <>
            {row?.display ? (
              <div className={styles['groupPeriod']}>
                <div
                  className={styles['tabStatus']}
                  style={{ ...row?.tabStatus?.style }}
                >
                  {row?.tabStatus?.text}
                </div>
              </div>
            ) : (
              <div className={styles['groupPeriod']}>
                <div className={styles['period']}>
                  <div className={styles['start']}>
                    {row?.fmtFromTime} <br /> {row?.fmtFromDate}
                  </div>
                  <div className={styles['line']}>-</div>
                  <div className={styles['end']}>
                    {row?.fmtToTime} <br /> {row?.fmtToDate}
                  </div>
                </div>
                <div
                  className={styles['tabStatus']}
                  style={{ ...row?.tabStatus?.style }}
                >
                  {row?.tabStatus?.text}
                </div>
              </div>
            )}
          </>
        )
      },
      {
        title: 'Trạng thái',
        key: 'status',
        align: 'center',
        width: 120,
        render: (row) => <Switch checked={row.status} />
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items = [
    {
      key: 'ALL',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: 'INPROGRESS',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'PENDING',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'EXPIRED',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case 'ALL':
        return (
          <MPTable
            type={'dragTable'}
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={dynHeightContent - 55}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onDragSortTable={onDragSortTable}
          />
        )
      case 'INPROGRESS':
      case 'PENDING':
      case 'EXPIRED':
        return (
          <MPTable
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={dynHeightContent - 55}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          activeKey={tabActiveKey}
          onChange={(key) => {
            setTabActiveKey(key)
            onHandleChangeTab(key, 'filterTab')
          }}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <div ref={filterRef} className={styles['filterWrapper']}>
        <div className={styles['left']}>
          <div className={styles['countRecord']}>
            {catListingList?.length ?? 0} Cơ sở y tế
          </div>
        </div>
        <div className={styles['right']}>
          <div className={styles['enhance']}>
            <MPButton onClick={onPressNew} typeCustom={'primary'}>
              <FiPlus />
              <span>Chọn cơ sở y tế</span>
            </MPButton>
          </div>
        </div>
      </div>
      {renderTabPane()}
      {isOpenEdit && (
        <ClinicBookingDetail
          loading={loading}
          contentModal={contentModal.edit}
          isOpenEdit={isOpenEdit}
          setIsOpenEdit={setIsOpenEdit}
          dataRow={dataRow}
          formSelectPartner={formSelectPartner}
          onSubmitUpdate={onSubmitUpdate}
          hospitalList={hospitalList}
          tabInfo={tabInfo}
        />
      )}
      {isOpenNew && (
        <ClinicBookingNew
          loading={loading}
          contentModal={contentModal.create}
          isOpenNew={isOpenNew}
          setIsOpenNew={setIsOpenNew}
          formSelectPartner={formSelectPartner}
          onSubmitCreate={onSubmitCreate}
          hospitalList={hospitalList}
          tabInfo={tabInfo}
        />
      )}
    </>
  )
}

export default ClinicBooking

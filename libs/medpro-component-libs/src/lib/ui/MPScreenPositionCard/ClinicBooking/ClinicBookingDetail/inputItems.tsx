import { useEffect, useState } from 'react'
import { Form, Radio, DatePicker, Select, Switch } from 'antd'
import { size } from 'lodash'
import { getReplaceUTF8 } from '@medpro-libs/medpro-component-libs'

const { RangePicker } = DatePicker
const { Option } = Select

export const inputItems = ({
  form,
  hospitalList,
  formSelectPartner,
  tabInfo
}: any) => {
  const [timeType, setTimeType] = useState(true)

  useEffect(() => {
    if (form) {
      setTimeType(form.getFieldValue('display'))
    }
  }, [form])

  const onChangeTimeType = (e: any) => {
    setTimeType(e.target.value)
  }

  const onSelectPartner = (value: any) => {
    formSelectPartner({ partnerId: value })
  }

  return [
    {
      id: 'partnerId',
      type: 'text',
      label: 'Cơ sở y tế',
      placeholder: '<PERSON><PERSON>n cơ sở y tế',
      require: true,
      disabled: true,
      enter: ({ id, label, placeholder, require, disabled }: any) => (
        <Form.Item
          label={label}
          name={id}
          rules={[
            {
              required: require,
              message: '<PERSON><PERSON> lòng chọn cơ sở y tế!'
            }
          ]}
        >
          <Select
            disabled={disabled}
            onChange={(value) => {
              onSelectPartner(value)
            }}
            placeholder={placeholder}
            showSearch
            allowClear
            filterOption={(input, option: any) =>
              getReplaceUTF8(
                (option?.children as unknown as string).toLowerCase()
              ).includes(getReplaceUTF8(input.toLowerCase()))
            }
          >
            {hospitalList && size(hospitalList) > 0
              ? hospitalList?.map((item: any, index: number) => (
                  <Option key={index} value={item.partnerId}>
                    {item.name}
                  </Option>
                ))
              : undefined}
          </Select>
        </Form.Item>
      ),
      hidden: false,
      group: false
    },
    {
      id: 'display',
      type: 'text',
      label: 'Thời gian hiển thị',
      placeholder: 'Chọn thời gian hiển thị',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng chọn thời gian hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={onChangeTimeType}
              options={[
                {
                  value: true,
                  label: 'Luôn'
                },
                {
                  value: false,
                  label: 'Theo giờ'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'timePicker',
      type: 'text',
      label: '',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={
              !timeType
                ? [
                    {
                      required: require,
                      message: 'Vui lòng chọn thời gian hiển thị!'
                    }
                  ]
                : []
            }
          >
            <RangePicker
              showTime
              format='DD/MM/YYYY HH:mm'
              placeholder={['Thời gian bắt đầu', 'Thời gian kết thúc']}
            />
          </Form.Item>
        )
      },
      hidden: timeType,
      group: false
    },
    {
      id: 'status',
      type: 'text',
      label: 'Trạng thái',
      placeholder: 'Chọn trạng thái',
      require: false,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Switch />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

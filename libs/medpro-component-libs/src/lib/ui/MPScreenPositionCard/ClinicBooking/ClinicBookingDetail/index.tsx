import React, { useEffect } from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import dayjs from 'dayjs'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputItems'
import styles from '../../styles.module.less'

export interface Props {
  loading?: boolean
  contentModal?: any
  isOpenEdit: boolean
  setIsOpenEdit: any
  hospitalList: any
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  formSelectPartner: (value: any) => void
  tabInfo?: any
  dataRow: any
}

const ClinicBookingDetail = ({
  loading,
  contentModal,
  isOpenEdit,
  setIsOpenEdit,
  onSubmitUpdate,
  hospitalList,
  formSelectPartner,
  tabInfo,
  dataRow
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    form.setFieldsValue({
      ...dataRow,
      timePicker: [
        dataRow?.fromDate ? dayjs(dataRow?.fromDate) : null,
        dataRow?.toDate ? dayjs(dataRow?.toDate) : null
      ]
    })
  }, [dataRow])

  const onCancel = () => {
    setIsOpenEdit(!isOpenEdit)
  }

  const onFinish = (values: any) => {
    return onSubmitUpdate(
      { ...values, category: tabInfo.category, type: tabInfo.type, id: dataRow?._id },
      onCancel
    )
  }

  return (
    <Modal {...contentModal} open={isOpenEdit} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems({
            form,
            hospitalList,
            formSelectPartner,
            tabInfo
          })
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div
                key={rowIndex}
                className={cx(
                  styles['inputRow'],
                  row.every((item: any) => item.hidden)
                    ? styles['hidden']
                    : null
                )}
              >
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export default ClinicBookingDetail

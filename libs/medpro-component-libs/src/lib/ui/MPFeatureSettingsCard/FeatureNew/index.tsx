import React from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import { inputItems } from './inputItems'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import styles from '../styles.module.less'

export interface Props {
  isOpenNew: boolean
  setIsOpenNew: any
}

const FeatureNew = ({ isOpenNew, setIsOpenNew }: Props) => {
  const [form] = Form.useForm()

  const onFinish = (values: any) => {
    console.log(111111111, values)
  }

  const onCancel = () => {
    setIsOpenNew(!isOpenNew)
  }

  const contentModal = {
    title: 'Thêm mới tính năng',
    centered: true,
    footer: false,
    className: styles['modalNewWrapper']
  }

  return (
    <Modal {...contentModal} open={isOpenNew} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems(form)
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div key={rowIndex} className={styles['inputRow']}>
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton htmlType='submit' typeCustom={'approval'}>
              Thêm
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}
export default FeatureNew

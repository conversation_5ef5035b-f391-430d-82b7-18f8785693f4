import { Form, Select } from 'antd'
import React from 'react'
import { Valid } from '../../../helpers/valid'
import { getReplaceUTF8 } from '../../../helpers/func'

const valid = new Valid()
const { Option } = Select

const features = [
  {
    title: 'Đặt khám theo bác sĩ',
    value: 1
  },
  {
    title: '<PERSON>hai báo y tế',
    value: 2
  },
  {
    title: 'Đặt lịch khám VIP',
    value: 3
  },
  {
    title: 'Tư vấn trực tuyến',
    value: 4
  },
  {
    title: 'Đặt khám',
    value: 5
  }
]

export const inputItems = (form: any) => {
  return [
    {
      id: 'feature',
      type: 'text',
      label: 'Chọn tính năng',
      placeholder: 'Chọn tính năng',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.required, required: require }]}
          >
            <Select
              placeholder={placeholder}
              showSearch
              allowClear
              filterOption={(input, option: any) =>
                getReplaceUTF8(
                  (option?.children as unknown as string).toLowerCase()
                ).includes(getReplaceUTF8(input.toLowerCase()))
              }
            >
              {features?.map((item: any, index: number) => (
                <Option key={index} value={item.id}>
                  {`${item.title}`}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

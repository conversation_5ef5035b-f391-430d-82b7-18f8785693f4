import React, { useState } from 'react'
import {
  Col,
  Drawer,
  DrawerProps,
  Form,
  FormInstance,
  Modal,
  Row,
  Table
} from 'antd'
import cx from 'classnames'
import _ from 'lodash'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputItems'
import styles from './styles.module.less'
import { ColumnsType } from 'rc-table'
import { handleDetails } from './common/handleDetails'
import ActionItem from './common/ActionItem'
import { handleCreateTransalte } from './common/handleCreateTransalte'
import { handleCreateTransalteChild } from './common/handleCreateTransalteChild'

export interface Props {
  contentModal?: DrawerProps
  uploadProps: any
  isOpenEdit: boolean
  setIsOpenEdit: any
  isFeature?: boolean
  data?: any
  partnerId?: string
  activeFeature?: string
  title?: string
  handleFinish: (values: any) => void
}

interface DataTranslate {
  locale: string
  name: string
  message: string
  index: number
  screenOptions: any
}

const FeatureDetail = ({
  contentModal,
  uploadProps,
  isOpenEdit,
  setIsOpenEdit,
  data,
  partnerId,
  activeFeature,
  handleFinish
}: Props) => {
  const [fromTranSlate]: [FormInstance<any>] = Form.useForm()
  const [formTranSlateChild]: [FormInstance<any>] = Form.useForm()
  const [disable, setDisable] = useState(data ? true : false)
  const [warn, setWarn] = useState(data?.disabled ? true : false)
  const [isModalOpen, setIsModalOpen] = useState({
    translate: false,
    translateChild: false
  })
  const [translateData, setTranslateData] = useState({
    title: 'create',
    label: 'Tạo mới translate',
    disable: false,
    index: -1
  })
  const [translateDataChild, setTranslateDataChild] = useState({
    title: 'create',
    label: 'Tạo mới translate child',
    disable: false,
    index: -1,
    childId: ''
  })
  const [translate, setTranslate] = useState(
    data ? data.translate : []
  ) as any[]

  const [translateChildren, setTranslateChildren] = useState(
    data ? data?.children : []
  ) as any[]
  const [form] = Form.useForm()

  const toggleModal = (title: string) => {
    switch (title) {
      case 'translate':
        setIsModalOpen({ ...isModalOpen, translate: !isModalOpen.translate })
        break
      case 'translateChild':
        setIsModalOpen({
          ...isModalOpen,
          translateChild: !isModalOpen.translateChild
        })
        break
      default:
        break
    }
  }
  const columnTranslate: ColumnsType<DataTranslate> = [
    {
      title: 'Locale',
      dataIndex: 'locale',
      key: 'locale'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message'
    },
    {
      title: 'Header title',
      dataIndex: 'screenOptions',
      key: 'screenOptions',
      render: (_, record) => record?.screenOptions?.headerTitle
    },
    {
      title: 'Button Text',
      dataIndex: 'bookingButtonText',
      key: 'bookingButtonText',
      render: (_, record) => record?.screenOptions?.bookingButtonText
    },
    {
      title: 'Thao tác',
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (_, record, i) => (
        <ActionItem
          disable={disable}
          record={{ ...record, index: i }}
          handleClick={handleActionTranslate}
        />
      )
    }
  ]

  const columnTranslateChildren: ColumnsType<DataTranslate> = [
    {
      title: 'Locale',
      dataIndex: 'locale',
      key: 'locale'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Subtitle',
      dataIndex: 'subtitle',
      key: 'subtitle'
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Thao tác',
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (_, record, i) => (
        <ActionItem
          disable={disable}
          record={{ ...record, index: i }}
          handleClick={handleActionTranslateChild}
        />
      )
    }
  ]

  const handleOpenTransalte = () => {
    fromTranSlate.resetFields()
    fromTranSlate.setFieldsValue({
      locale: 'km'
    })
    setTranslateData({
      ...translateData,
      label: 'Tạo mới translate',
      title: 'create',
      disable: false
    })
    toggleModal('translate')
  }

  const handleOpenTransalteChild = (_id: string) => {
    formTranSlateChild.resetFields()
    setTranslateDataChild({
      ...translateDataChild,
      title: 'create',
      label: 'Tạo mới translate Child',
      disable: false,
      childId: _id
    })
    toggleModal('translateChild')
  }

  const handleActionTranslate = (data: any) => {
    if (data.item.title === 'delete') {
      const arr = translate.filter((item: any, index: number) => {
        return `${index}` !== `${data.item.info.index}`
      })

      setTranslateData({
        ...translateData,
        index: -1
      })
      setTranslate(arr)
    } else if (data.item.title === 'edit') {
      fromTranSlate.setFieldsValue({
        message: data.item.info.message,
        locale: data.item.info.locale,
        name: data.item.info.name,
        headerTitle: data.item.info.screenOptions?.headerTitle,
        bookingButtonText: data.item.info.screenOptions?.bookingButtonText
      })
      setTranslateData({
        ...translateData,
        title: 'edit',
        label: 'Sửa thông tin translate',
        disable: true,
        index: data.item.info.index
      })
      toggleModal('translate')
    }
  }

  const handleActionTranslateChild = (data: any) => {
    if (data.item.title === 'delete') {
      const deleteTranslateChild = translateChildren.map((item: any) => {
        if (item._id === data.item.info.childId) {
          const newTranslate = item?.translate?.filter(
            (trans: any, i: number) => {
              return `${i}` !== `${data.item.info.index}`
            }
          )
          return { ...item, translate: newTranslate }
        }
        return { ...item }
      })

      setTranslateDataChild({
        ...translateDataChild,
        index: -1,
        childId: data.item.info.childId
      })
      setTranslateChildren(deleteTranslateChild)
    } else if (data.item.title === 'edit') {
      formTranSlateChild.setFieldsValue({ ...data.item.info })
      setTranslateDataChild({
        ...translateDataChild,
        title: 'edit',
        label: 'Sửa thông tin translateChild',
        disable: true,
        index: data.item.info.index,
        childId: data.item.info.childId
      })
      toggleModal('translateChild')
    }
  }

  const handleSubmitTranslate = (values: any) => {
    const arr: any[] = translate
    const newValues = {
      message: values.message,
      locale: values.locale,
      name: values.name,
      screenOptions: {
        headerTitle: values.headerTitle,
        bookingButtonText: values.bookingButtonText
      }
    }
    let arrSort = arr.map((item: any, index: number) => {
      return (item = { ...item, index: index })
    })
    switch (translateData.title) {
      case 'create':
        setTranslate([...translate, newValues])
        toggleModal('translate')
        break
      case 'edit':
        arrSort = [
          { ...newValues, index: translateData.index },
          ...arrSort.filter(
            (item: any, index: number) => index !== translateData.index
          )
        ]
        setTranslate(_.sortBy(arrSort, ['index']))
        setTranslateData({
          ...translateData,
          index: -1
        })
        toggleModal('translate')
        break
      default:
        break
    }
  }

  const handleSubmitTranslateChild = (values: any) => {
    switch (translateDataChild.title) {
      case 'create':
        const newTranslateChild = translateChildren.map((item: any) => {
          if (item._id === translateDataChild.childId) {
            const newTranslate = [...item.translate, values]
            return { ...item, translate: newTranslate }
          }
          return { ...item }
        })
        setTranslateChildren(newTranslateChild)
        toggleModal('translateChild')
        break
      case 'edit':
        const editTranslateChild = translateChildren.map((item: any) => {
          if (item._id === translateDataChild.childId) {
            const newTranslate = item?.translate?.map(
              (trans: any, i: number) => {
                if (i === translateDataChild.index) {
                  return { ...trans, ...values }
                }
              }
            )
            return { ...item, translate: newTranslate }
          }
          return { ...item }
        })
        setTranslateChildren(editTranslateChild)
        setTranslateDataChild({
          ...translateDataChild,
          index: -1
        })
        toggleModal('translateChild')
        break
      default:
        break
    }
  }

  const onFinish = (values: any) => {
    const translateChildrenUpdate = translateChildren?.map((item: any) => {
      return {
        _id: item._id,
        translate: item.translate,
        subtitle: values.subtitle,
        description: values.description
      }
    })
    if (data) {
      const formData = {
        ...values,
        id: data?.id || data?._id,
        featureId: data?.id || data?._id,
        partnerId: partnerId,
        translate: translate,
        disabled: !values?.disabled,
        screenOptions: {
          ...data?.screenOptions,
          bookingButtonText: values.bookingButtonText,
          headerTitle: values.headerTitle
        },
        children: translateChildrenUpdate
      }
      handleFinish(formData)
      setDisable(!disable)
    } else {
      const formData = {
        ...values,
        translate: translate,
        status: true,
        mobileStatus: false,
        screenOptions: {
          bookingButtonText: values.bookingButtonText,
          headerTitle: values.headerTitle
        },
        children: translateChildrenUpdate
      }
      handleFinish(formData)
      form.resetFields()
      setTranslate([])
    }
  }
  const onDataFields = (values: any) => {
    const vara = {
      ...values,
      disabled: !values?.disabled,
      headerTitle: values?.screenOptions?.headerTitle || '',
      bookingButtonText: values?.screenOptions?.bookingButtonText || ''
    }

    return vara
  }

  const onClose = () => {
    setIsOpenEdit(!isOpenEdit)
  }
  console.log('translateChildren :>> ', translateChildren)

  return (
    <Drawer {...contentModal} open={isOpenEdit} onClose={onClose}>
      <>
        <div className={styles['formContact']}>
          <div className={styles['title']}>{data.name}</div>
          <Form
            form={form}
            layout='vertical'
            initialValues={onDataFields(data) || { position: 'IN' }}
            onFinish={onFinish}
            className={styles['listContact']}
          >
            <Row gutter={[50, 25]}>
              {handleDetails(
                disable,
                activeFeature,
                uploadProps,
                form,
                warn,
                setWarn
              ).map((item, index) => {
                if (item.hidden) {
                  return null
                }
                return (
                  <Col key={index} span={24} lg={item.width || 12}>
                    <div className={styles['inputItem']}>
                      {item?.enter && item?.enter(item)}
                    </div>
                  </Col>
                )
              })}
              <Col span={24} lg={24}>
                <div className={styles['titleTranslate']}>Translate:</div>
                <Table
                  pagination={false}
                  className={styles['tableTranslate']}
                  dataSource={translate}
                  columns={[...columnTranslate]}
                />
                <div className={styles['footerTranslate']}>
                  <MPButton
                    disabled={disable}
                    onClick={handleOpenTransalte}
                    type='primary'
                  >
                    Add
                  </MPButton>
                </div>
              </Col>
              {/* Translate children */}
              {/* {!!translateChildren.length && (
                <Col span={24} lg={24}>
                  <div
                    className={cx(
                      styles['titleTranslate'],
                      styles['titleTranslateChild']
                    )}
                  >
                    Translate Introduction:
                  </div>
                  {translateChildren?.map((item: any, index: number) => {
                    const translateChild = item?.translate?.map((el: any) => {
                      return {
                        ...el,
                        childId: item._id
                      }
                    })
                    return (
                      <Col span={24} lg={24}>
                        <div className={styles['titleTranslate']}>
                          {index + 1}. {item?.title}
                        </div>
                        <Table
                          pagination={false}
                          className={styles['tableTranslate']}
                          dataSource={translateChild}
                          columns={[...columnTranslateChildren]}
                        />
                        <div className={styles['footerTranslate']}>
                          <MPButton
                            disabled={disable}
                            onClick={() => handleOpenTransalteChild(item._id)}
                            type='primary'
                          >
                            Add
                          </MPButton>
                        </div>
                      </Col>
                    )
                  })}
                </Col>
              )} */}
            </Row>

            <div className={styles['footerButton']}>
              {data ? (
                disable ? (
                  <MPButton
                    className={cx(styles['btn'], styles['btnEdit'])}
                    onClick={() => setDisable(!disable)}
                  >
                    Chỉnh sửa
                  </MPButton>
                ) : (
                  <>
                    <MPButton
                      className={cx(styles['btn'], styles['btnFooter'])}
                      type='default'
                      onClick={() => setDisable(!disable)}
                      typeCustom='cancel'
                    >
                      Đóng
                    </MPButton>
                    <MPButton
                      className={cx(styles['btn'], styles['btnFooter'])}
                      type='primary'
                      htmlType='submit'
                    >
                      Lưu
                    </MPButton>
                  </>
                )
              ) : (
                <MPButton
                  className={styles['btnFooter']}
                  type='primary'
                  htmlType='submit'
                >
                  Thêm
                </MPButton>
              )}
            </div>
          </Form>
        </div>
        <Drawer
          title={translateData.label}
          open={isModalOpen.translate}
          onClose={() => toggleModal('translate')}
          className={styles['modalForm']}
          width={600}
          footer={null}
        >
          <div>
            <Form
              layout='vertical'
              onFinish={handleSubmitTranslate}
              className={styles['listContact']}
              form={fromTranSlate}
            >
              <Row gutter={50}>
                {handleCreateTransalte(translateData.disable, locale).map(
                  (item, index) => {
                    return (
                      <Col
                        key={index}
                        span={24}
                        lg={item.width === 'fuild' ? 24 : 12}
                      >
                        <div className={styles['inputItem']}>
                          {item?.enter && item?.enter(item)}
                        </div>
                      </Col>
                    )
                  }
                )}
              </Row>
              <div className={styles['footerButton']}>
                <MPButton
                  className={styles['btnFooter']}
                  type='default'
                  onClick={() => toggleModal('translate')}
                >
                  Đóng
                </MPButton>
                <MPButton
                  className={styles['btnFooter']}
                  type='primary'
                  htmlType='submit'
                >
                  Lưu
                </MPButton>
              </div>
            </Form>
          </div>
        </Drawer>
        {/* Modal translate children */}
        <Drawer
          title={translateDataChild.label}
          open={isModalOpen.translateChild}
          onClose={() => toggleModal('translateChild')}
          className={styles['modalForm']}
          width={600}
          footer={null}
        >
          <div>
            <Form
              layout='vertical'
              onFinish={handleSubmitTranslateChild}
              className={styles['listContact']}
              form={formTranSlateChild}
            >
              <Row gutter={50}>
                {handleCreateTransalteChild(
                  translateDataChild.disable,
                  locale
                ).map((item, index) => {
                  return (
                    <Col
                      key={index}
                      span={24}
                      lg={item.width === 'fuild' ? 24 : 12}
                    >
                      <div className={styles['inputItem']}>
                        {item?.enter && item?.enter(item)}
                      </div>
                    </Col>
                  )
                })}
              </Row>
              <div className={styles['footerButton']}>
                <MPButton
                  className={styles['btnFooter']}
                  type='default'
                  onClick={() => toggleModal('translateChild')}
                >
                  Đóng
                </MPButton>
                <MPButton
                  className={styles['btnFooter']}
                  type='primary'
                  htmlType='submit'
                >
                  Lưu
                </MPButton>
              </div>
            </Form>
          </div>
        </Drawer>
      </>
    </Drawer>
  )
}
export default FeatureDetail

const locale = [
  {
    title: 'Việt Nam',
    value: 'vi'
  },
  {
    title: 'Campuchia',
    value: 'km'
  },
  {
    title: 'English',
    value: 'en'
  }
]

import { Form, Input, InputNumber, Tooltip, Switch } from 'antd'
import React from 'react'
import { AiOutlineQuestionCircle } from 'react-icons/ai'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import MPImageFormItemSingle from '../../../atoms/MPImageFormItemSingle'
import { Valid } from '../../../helpers/valid'
import styles from '../styles.module.less'

const valid = new Valid()
const { TextArea } = Input

export const inputItems = (form: any, uploadProps: any) => {
  return [
    {
      id: 'name',
      type: 'text',
      label: 'Tên feature',
      placeholder: 'Vui lòng nhập tên feature (có dấu) ...',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'priority',
      type: 'number',
      label: 'Thứ tự ưu tiên',
      placeholder: '<PERSON>ui lòng nhập thứ tự ưu tiên',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.numberRequired, required: require }]}
          >
            <InputNumber type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'bookingLimit',
      type: 'number',
      label: 'Booking limit',
      placeholder: 'Vui lòng nhập booking limit',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.numberRequired, required: require }]}
          >
            <InputNumber type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'message',
      type: 'text',
      label: (
        <div className={styles['tooltipInputItem']}>
          Thông báo{' '}
          <Tooltip
            title='Nội dung thông báo dùng để hiển thị khi tính năng bị disabled'
            placement='top'
          >
            <span>
              <AiOutlineQuestionCircle />
            </span>
          </Tooltip>
        </div>
      ),
      placeholder: 'Nhập thông báo',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <TextArea rows={2} placeholder={placeholder} autoSize={false} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'image',
      type: 'upload',
      label: 'Hình ảnh web',
      placeholder: '',
      require: true,
      enter: ({ require, id, label }: any) => {
        return (
          <Form.Item
            name={id}
            label={label}
            valuePropName={'url'}
            extra=''
            rules={[{ validator: valid.required }]}
          >
            <MPImageFormItemSingle
              {...uploadProps}
              multiple={false}
              onRemove={() => form.setFieldsValue({ [id]: null })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'mobileIcon',
      type: 'upload',
      label: 'Hình ảnh mobile',
      placeholder: '',
      require: true,
      enter: ({ require, id, label }: any) => {
        return (
          <Form.Item
            name={id}
            label={label}
            valuePropName={'url'}
            extra=''
            rules={[{ validator: valid.required }]}
          >
            <MPImageFormItemSingle
              {...uploadProps}
              multiple={false}
              onRemove={() => form.setFieldsValue({ [id]: null })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'displayIcon',
      type: 'upload',
      label: 'Display icon',
      placeholder: '',
      require: false,
      enter: ({ require, id, label }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemSingle
              {...uploadProps}
              multiple={false}
              onRemove={() => form.setFieldsValue({ [id]: null })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      group: true
    },
    {
      id: 'disabled',
      label: (
        <div className={styles['tooltipInputItem']}>
          Trạng thái{' '}
          <Tooltip title='Tắt/Mở tính năng' placement='top'>
            <span>
              <AiOutlineQuestionCircle />
            </span>
          </Tooltip>
        </div>
      ),
      require: false,
      enter: ({ id, require, label }: any) => {
        return (
          <Form.Item label={label} name={id} valuePropName='checked'>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
              // onChange={(checked) => setWarn(!checked)}
            />
          </Form.Item>
        )
      },
      group: true
    },
    {
      id: 'status',
      label: (
        <div className={styles['tooltipInputItem']}>
          Trạng thái Website{' '}
          <Tooltip title='Tắt/Mở tính năng ở Website' placement='top'>
            <span>
              <AiOutlineQuestionCircle />
            </span>
          </Tooltip>
        </div>
      ),
      require: false,
      enter: ({ id, require, label }: any) => {
        return (
          <Form.Item label={label} name={id} valuePropName='checked'>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
            />
          </Form.Item>
        )
      },
      group: true
    },
    {
      id: 'mobileStatus',
      label: (
        <div className={styles['tooltipInputItem']}>
          Trạng thái App{' '}
          <Tooltip title='Tắt/Mở tính năng ở App' placement='top'>
            <span>
              <AiOutlineQuestionCircle />
            </span>
          </Tooltip>
        </div>
      ),
      require: false,
      enter: ({ id, require, label }: any) => {
        return (
          <Form.Item label={label} name={id} valuePropName='checked'>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
            />
          </Form.Item>
        )
      },
      group: true
    }
  ]
}

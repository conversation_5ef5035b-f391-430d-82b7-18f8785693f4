// @import './../common/global.module.less';
.footerButton {
  .footerButton {
    width: 100%;
    border-top: 1px solid #b2b2b2;
    padding-top: 15px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
  }
}

.enterCustom {
  padding: 0 10px;
  border-radius: 5px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 0.875rem;
  line-height: 1.5715;
  background-color: #fff;
  background-image: none;
  border: 1px solid #000;
  transition: all 0.3s;
  position: relative;
  display: inline-block;
  width: 100%;
}

.formContact {
  // padding: 20px;
  :global {
    .ant-form-vertical .ant-form-item-row {
      flex-direction: unset;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .ant-form-vertical .ant-form-item-label {
      width: 100%;
    }
    .ant-form-vertical .ant-form-item .ant-form-item-control {
      width: 100%;
    }
  }

  .title {
    text-align: center;
    font-weight: 600;
    font-size: 20px;
    padding-bottom: 15px;
  }

  .listContact {
    width: 100%;
    height: 100%;
    max-width: 800px;
    list-style-type: none;
    padding: 30px 15px 15px 15px;
    border-radius: 5px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    // border: 1px solid #b2b2b2;
    li {
      width: 50%;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      &:last-child {
        width: 100%;
      }

      .enter {
        width: 100%;
        label {
          font-weight: 500;
          sup {
            font-size: 1rem;
            color: red;
          }
        }

        input,
        select {
          height: 45px;
          .enterCustom();
        }
        // input:disabled {
        //   background-color: transparent;
        //   cursor: text;
        //   color: #000;
        // }
        textarea {
          padding: 10px !important;
          .enterCustom();
        }

        input,
        select,
        textarea {
          &:hover,
          &:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
            outline: 0;
          }
        }
      }
    }
  }
}

.tableTranslate {
  border: 1px solid #b2b2b2;
  .btnEdit {
    &:hover,
    &:focus,
    &:active {
      border-color: #f1c40f;
      svg {
        fill: #f1c40f;
      }
    }
  }
  .btnDelete {
    &:hover,
    &:focus,
    &:active {
      border-color: #fd766a;
      svg {
        fill: #fd766a;
      }
    }
  }
  .btnEdit,
  .btnDelete {
    border-radius: 50%;
    padding: 5px;
    svg {
      fill: grey;
      width: 17px;
      height: 17px;
    }
  }
  .imageItem {
    width: 45px;
    max-height: 45px;
    object-fit: contain;
    padding: 5px;
    border-radius: 5px;
    border: 1px solid #b2b2b2;
  }
}
.titleTranslate {
  font-weight: 600;
}

.titleTranslateChild {
  margin-bottom: 0.5em;
}

.footerTranslate {
  margin-top: 15px;
  margin-bottom: 15px;
}

.footerButton {
  width: 100%;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 20px;
  .btn {
    padding: 7px 30px;

    height: fit-content;
    font-size: 16px;
    border: none;
    color: #ffffff;
    cursor: pointer;
  }
  .btnEdit {
    background: #40a9ff;
    border: 1px solid transparent;
    &:hover {
      border-color: #40a9ff;
    }
  }
  .btnFooter {
    padding-left: 30px;
    padding-right: 30px;
  }
  .backBtn {
    padding: 7px 30px;
    background: #f0a055;
    color: #ffffff;
    cursor: pointer;
    border: 1px solid #f0a055;
    &:hover {
      border-color: #f0a055;
    }
  }
}

.formInputItem {
  .inputItem {
    label {
      font-weight: 600;
    }
    .requireInput {
      font-size: 100%;
      top: -0.2em;
      left: 3px;
      color: red;
    }
    :global {
      .ant-input-number-disabled,
      .ant-select-disabled.ant-select:not(.ant-select-customize-input)
        .ant-select-selector,
      .ant-input[disabled] {
        background: #ffffff;
        color: #000000;
      }
      .ant-input-number {
        width: 100%;
      }
      .ant-form-item-explain-error {
        font-size: 0.7rem;
      }
      .ql-editor {
        background: #ffffff;
        &::-webkit-scrollbar {
          display: unset;
          width: 3px;
          background: none;
        }
        &::-webkit-scrollbar-thumb {
          background: #192a56;
          background: linear-gradient(
            70deg,
            #006184,
            #facc00,
            #7303c2,
            #01852b
          );
          border: 1px solid #b5b5b5;
          border-radius: 3px;
        }
      }
    }
    .quill {
      height: 170px;
      background: #ffffff;
    }
  }
}
.tooltip_support {
  margin-left: 3px;
}

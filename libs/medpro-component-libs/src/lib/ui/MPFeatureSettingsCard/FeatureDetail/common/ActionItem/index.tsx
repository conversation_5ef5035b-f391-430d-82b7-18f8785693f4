import {} from '@ant-design/icons'
import { Popconfirm, Tooltip, Button } from 'antd'
import { Space } from 'antd'
import React from 'react'
import { AiFillDelete, AiFillEdit } from 'react-icons/ai'
import styles from './../../styles.module.less'
interface DataType {
  type: string
  name: string
  localeName: string
  webRoute: string
  mobileRoute: string
  disabled: boolean
  image: string
  mobileIcon: string
  displayIcon: string
  priority: number | string
  bookingLimit: number | string
  status: boolean
  mobileStatus: boolean
  message: string
  customUrl: string
  position: string
}
interface DataTranslate {
  locale: string
  name: string
  message: string
  index: number
}
interface Props {
  disable: boolean
  record: DataType | DataTranslate
  handleClick: (item: any) => void
}

const ActionItem = ({ disable, record, handleClick }: Props) => {
  const confirm = () => {
    handleClick({ item: { title: 'delete', info: record } })
  }

  return (
    <Space size='middle'>
      <Tooltip title='Chỉnh sửa' placement='bottom' color='#f1c40f'>
        <Button
          disabled={disable}
          onClick={() => handleClick({ item: { title: 'edit', info: record } })}
          className={styles['btnEdit']}
          type='default'
        >
          <AiFillEdit />
        </Button>
      </Tooltip>
      <Tooltip title='Xóa' placement='bottom' color='#fd766a'>
        <Popconfirm
          placement='leftBottom'
          title='Bạn có chắc muốn xóa?'
          onConfirm={confirm}
          okText='Yes'
          cancelText='No'
          disabled={disable}
        >
          <Button
            disabled={disable}
            className={styles['btnDelete']}
            type='default'
          >
            <AiFillDelete />
          </Button>
        </Popconfirm>
      </Tooltip>
    </Space>
  )
}

export default ActionItem

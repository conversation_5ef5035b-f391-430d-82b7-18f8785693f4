import {
  Form,
  FormInstance,
  Input,
  InputNumber,
  Select,
  Switch,
  Tooltip
} from 'antd'
import TextArea from 'antd/lib/input/TextArea'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import { AiOutlineQuestionCircle } from 'react-icons/ai'
import { MPImageFormItemMultiple } from '@medpro-libs/medpro-component-libs'
import { Valid } from '../../../../helpers/valid'
import styles from './../styles.module.less'

const valid = new Valid()
const { Option } = Select
export const handleDetails = (
  disable: boolean,
  activeFeature: any,
  uploadProps: any,
  form: FormInstance<any>,
  warn: any,
  setWarn: any
) => {
  return [
    {
      id: 'image',
      type: 'upload',
      label: 'Hình ảnh web',
      placeholder: '',
      require: true,
      enter: ({ require, id, label }: any) => {
        return (
          <Form.Item
            name={id}
            label={handleRequireInput(label, require)}
            valuePropName={'url'}
            extra=''
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <MPImageFormItemMultiple
              {...uploadProps}
              disable={disable}
              listType='picture-card'
              multiple={false}
              maxCount={1}
              onRemove={() => form.setFieldsValue({ image: '' })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 8
    },
    {
      id: 'mobileIcon',
      type: 'upload',
      label: 'Hình ảnh mobile',
      placeholder: '',
      require: true,
      enter: ({ require, id, label }: any) => {
        return (
          <Form.Item
            name={id}
            label={handleRequireInput(label, require)}
            valuePropName={'url'}
            extra=''
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <MPImageFormItemMultiple
              {...uploadProps}
              disable={disable}
              listType={'picture-card'}
              multiple={false}
              maxCount={1}
              onRemove={() => form.setFieldsValue({ mobileIcon: '' })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 8
    },
    {
      id: 'displayIcon',
      type: 'upload',
      label: 'Display icon',
      placeholder: '',
      require: true,
      enter: ({ require, id, label }: any) => {
        return (
          <Form.Item
            name={id}
            label={handleRequireInput(label, require)}
            valuePropName={'url'}
            extra=''
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <MPImageFormItemMultiple
              disable={disable}
              {...uploadProps}
              multiple={false}
              maxCount={1}
              onRemove={() => form.setFieldsValue({ displayIcon: '' })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 8
    },
    {
      id: 'name',
      type: 'text',
      label: 'Tên tính năng',
      placeholder: 'Nhập tên tính năng',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'localeName',
      type: 'text',
      label: 'Tên feature EN',
      placeholder: 'Nhập tên feature EN',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'type',
      type: 'text',
      label: 'Type',
      placeholder: 'Nhập type',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              placeholder={placeholder}
              disabled={activeFeature === 'add' ? false : true}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'slug',
      type: 'text',
      label: 'Feature slug',
      placeholder: 'Nhập feature slug',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'priority',
      type: 'text',
      label: 'Thứ tự ưu tiên',
      placeholder: 'Nhập thứ tự ưu tiên',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <InputNumber
              type={type}
              placeholder={placeholder}
              disabled={disable}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'bookingLimit',
      type: 'text',
      label: 'Booking limit',
      placeholder: 'Nhập booking limit',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <InputNumber
              type={type}
              placeholder={placeholder}
              disabled={disable}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'webRoute',
      type: 'text',
      label: 'Web Route',
      placeholder: 'Nhập Web Route',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: true,
      width: 12
    },
    {
      id: 'mobileRoute',
      type: 'text',
      label: 'Mobile Route',
      placeholder: 'Nhập Mobile Route',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: true,
      width: 12
    },
    {
      id: 'customUrl',
      type: 'text',
      label: 'Custom Url',
      placeholder: 'Nhập custom Url',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'position',
      type: 'text',
      label: 'Position',
      placeholder: 'Nhập position',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <Select placeholder={placeholder} disabled={disable}>
              {position?.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'message',
      type: 'text',
      label: 'Message',
      placeholder: 'Nhập message',
      support: 'message dùng để show ra khi tính năng bị disabled',
      require: warn ? true : false,
      enter: ({ id, require, placeholder, label, support }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require, support)}
            rules={[warn ? { validator: valid.required } : {}]}
            name={id}
            className={styles['formInputItem']}
          >
            <TextArea
              placeholder={placeholder}
              className={styles['textArea']}
              size='middle'
              disabled={disable}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'warningMessage',
      type: 'text',
      label: 'Warning message',
      placeholder: 'Nhập warning message',
      support: 'warningMessage là show ra trước khi next step',
      require: false,
      enter: ({ id, require, placeholder, label, support }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require, support)}
            name={id}
            className={styles['formInputItem']}
          >
            <TextArea
              placeholder={placeholder}
              className={styles['textArea']}
              size='middle'
              disabled={disable}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'headerTitle',
      type: 'text',
      label: 'Header Title',
      placeholder: 'Nhập header title',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'bookingButtonText',
      type: 'text',
      label: 'Button Text',
      placeholder: 'Nhập button Text',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'subtitle',
      type: 'text',
      label: 'Subtitle',
      placeholder: 'Nhập subtitle',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} disabled={disable} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'description',
      type: 'text',
      label: 'Description',
      placeholder: 'Nhập description',
      require: false,
      enter: ({ id, require, placeholder, label, support }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require, support)}
            name={id}
            className={styles['formInputItem']}
          >
            <TextArea
              placeholder={placeholder}
              className={styles['textArea']}
              size='middle'
              disabled={disable}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 12
    },
    {
      id: 'disabled',
      label: 'Status',
      support: 'ON/OFF tính năng',
      require: false,
      enter: ({ id, require, label, support }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require, support)}
            name={id}
            className={styles['formInputItem']}
            valuePropName='checked'
          >
            <Switch
              disabled={disable}
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
              onChange={(checked) => setWarn(!checked)}
            />
          </Form.Item>
        )
      },
      width: 8
    },
    {
      id: 'status',
      label: 'Status Web',
      support: 'Show/Hide tính năng cho website',
      require: false,
      enter: ({ id, require, label, support }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require, support)}
            name={id}
            className={styles['formInputItem']}
            valuePropName='checked'
          >
            <Switch
              disabled={disable}
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
            />
          </Form.Item>
        )
      },
      width: 8
    },
    {
      id: 'mobileStatus',
      label: 'Status App',
      support: 'Show/Hide tính năng cho mobile',
      require: false,
      enter: ({ id, require, label, support }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require, support)}
            name={id}
            className={styles['formInputItem']}
            valuePropName='checked'
          >
            <Switch
              disabled={disable}
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
            />
          </Form.Item>
        )
      },

      width: 8
    }
  ]
}

export const handleRequireInput = (
  label: string,
  require: boolean,
  support?: string
) => {
  return (
    <div className={styles['inputItem']}>
      <div>
        {label}
        {support && (
          <Tooltip title={support} className={styles['tooltip_support']}>
            <AiOutlineQuestionCircle color='#3498db' />
          </Tooltip>
        )}
        {require ? (
          <>
            <sup className={styles['requireInput']}>* </sup> :
          </>
        ) : (
          ' :'
        )}
      </div>
    </div>
  )
}

export const position = [
  {
    title: 'IN',
    value: 'IN'
  },
  {
    title: 'OUT',
    value: 'OUT'
  }
]

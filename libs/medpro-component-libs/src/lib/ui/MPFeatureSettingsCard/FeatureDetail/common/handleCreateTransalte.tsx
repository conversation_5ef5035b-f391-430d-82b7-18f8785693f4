import { Form, Input, Select, Tooltip } from 'antd'
import { Valid } from '../../../../helpers/valid'

import styles from './../styles.module.less'
import { AiOutlineQuestionCircle } from 'react-icons/ai'
// export interface ListFormIF {}

const valid = new Valid()
const { Option } = Select
export const handleCreateTransalte = (disable: boolean, locale: any[]) => {
  const list = [
    {
      id: 'locale',
      type: 'text',
      label: 'Locale',
      placeholder: 'Nhập locale',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.required }]}
            className={styles['formInputItem']}
          >
            <Select placeholder={placeholder} disabled={disable}>
              {locale?.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'name',
      type: 'text',
      label: 'Name',
      placeholder: 'Nhập Name',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'message',
      type: 'text',
      label: 'Message',
      placeholder: 'Nhập message',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'bookingButtonText',
      type: 'text',
      label: 'Button Text',
      placeholder: 'Nhập button Text',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'headerTitle',
      type: 'text',
      label: 'Header Title',
      placeholder: 'Nhập header title',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    }
  ]
  return list
}

export const handleRequireInput = (
  label: string,
  require: boolean,
  support?: string
) => {
  return (
    <div className={styles['inputItem']}>
      <div>
        {label}
        {support && (
          <Tooltip title={support} className={styles['tooltip_support']}>
            <AiOutlineQuestionCircle color='#3498db' />
          </Tooltip>
        )}
        {require ? (
          <>
            <sup className={styles['requireInput']}>* </sup> :
          </>
        ) : (
          ' :'
        )}
      </div>
    </div>
  )
}

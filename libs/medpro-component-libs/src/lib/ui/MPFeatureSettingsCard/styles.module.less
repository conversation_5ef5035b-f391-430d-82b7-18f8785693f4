.pageWrapper {
  .header {
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    background: white;
    z-index: 999;
    padding: 10px 0;

    .title {
      display: flex;
      align-items: center;

      :global {
        .ant-ribbon {
          margin-top: -30px;
        }
      }

      h2 {
        font-size: 20px;
      }
    }
  }

  .body {
    margin-top: 12px;

    .filterWrapper {
      -webkit-box-align: center;
      align-items: center;
      border-color: #e7e7e7;
      background-color: #e7e7e7;
      display: flex;
      flex-wrap: wrap;
      border-radius: 10px 10px 0 0;
      padding: 10px 10px;

      .left {
        flex: 35%;
        display: flex;

        :global {
          .ant-input-search {
            max-width: 300px;

            .ant-input-outlined {
              border-color: #e7e7e7;
            }
          }
        }
      }

      .right {
        flex: 10%;
        display: flex;
        justify-content: flex-end;

        .enhance {
          display: flex;
          gap: 5px;

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            padding: 4px;
            border: 1px solid #fff;
            background: #fff;

            &:hover,
            &:active,
            &:focus {
              background: #ced5e6;
            }
          }

          .btnFilter {
            svg {
              font-size: 22px;
              color: #44474e;
            }
          }

          .btnRefresh {
            svg {
              font-size: 22px;
              color: #44474e;
            }
          }

          .btnAdd {
            svg {
              font-size: 22px;
              color: #44474e;
            }
          }
        }
      }
    }
  }
}

.SpaceWrapper {
  margin: 10px 0;
  flex-wrap: wrap;
}

.cardFeatureSettings {
  border: 1px solid #dfdfdf !important;
  width: 180px !important;
  height: 180px !important;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  transition: background 0.3s ease-in-out;

  :global {
    .ant-card-body {
      padding: 0 12px !important;
    }
  }
}

.cardFeatureSettings:hover {
  background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter: blur(10px);
}

.cardFeatureSettings:hover .action-icon {
  opacity: 1;
  animation: fadeIn 0.3s ease-in-out;
}

.action-icon {
  position: absolute;
  width: 180px;
  height: 180px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter: blur(10px);
}

.cardImageWrapper {
  width: 100%;
  height: 100px;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.featureTitle {
  font-size: 16px;
  font-weight: 500;
  color: #44474e;
  text-align: center;
}

.DrawerWrapper {
  :global {
    .ant-modal-content {
      height: 100vh;
      display: flex;
      flex-direction: column;

      .ant-modal-body {
        height: 100%;
        overflow: auto;
      }
    }
  }

  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;
      position: sticky;
      bottom: 0;
      background: #ffffff;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

.modalNewWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

@keyframes fadeInOut {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

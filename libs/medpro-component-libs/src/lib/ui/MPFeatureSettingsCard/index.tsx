import React, { useState } from 'react'
import { Tooltip, Input, Card, Space } from 'antd'
import type { DrawerProps } from 'antd'
import {
  AiFillEdit,
  AiFillEye,
  AiOutlinePlusCircle,
  AiOutlineRedo
} from 'react-icons/ai'
import cx from 'classnames'
import FeatureNew from './FeatureNew'
import FeatureDetail from './FeatureDetail'
import styles from './styles.module.less'
import Image from 'next/image'
import MPLoading from '../../atoms/MPLoading'
import { handleName } from '../../helpers/func'

export interface FeatureSettingsProps {
  listFeatureByPartner?: Item_Feature[]
  // listAllGlobalFeature?: Item_Feature[]
  dataResult?: Item_Feature[]
  uploadProps: any
  loading: {
    key: string
    status: boolean
    description?: string
  }
  onEdit: (values: any) => void
  handleChangeSearch?: any
  fetchListFeature: () => void
}

const contentModal: DrawerProps = {
  title: 'C<PERSON>u hình tính năng',
  width: '55%',
  footer: false,
  className: styles['DrawerWrapper'],
  closable: { 'aria-label': 'Close Button' },
  placement: 'right'
}

const MPFeatureSettingsCard = ({
  dataResult,
  uploadProps,
  loading,
  onEdit,
  fetchListFeature,
  handleChangeSearch
}: FeatureSettingsProps) => {
  const { Search } = Input
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [dataEdit, setDataEdit] = useState(undefined)
  const [selectFeature, setSelectFeature] = useState<Item_Feature | any>({})

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const handleActionDetail = (feature: Item_Feature) => {
    setIsOpenEdit((preState) => !preState)
    setSelectFeature(feature)
  }

  return (
    <>
      <div className={cx(styles['pageWrapper'], 'fade-in')}>
        <div className={styles['header']}>
          <div className={styles['title']}>
            <h2>Danh sách tính năng</h2>
          </div>
        </div>
        <div className={cx(styles['body'])}>
          <div className={styles['filterWrapper']}>
            <div className={styles['left']}>
              <Search
                size='large'
                placeholder='Tìm kiếm tên tính năng ...'
                onSearch={handleChangeSearch}
                onKeyPress={handleName}
                onChange={(
                  e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
                ) => handleChangeSearch(e.target.value)}
                style={{ width: '100%' }}
                allowClear
                enterButton
              />
            </div>
            <div className={styles['right']}>
              <div className={styles['enhance']}>
                {/* <Tooltip title='Thêm mới' placement='bottom' color={'#44474e'}>
                  <div
                    onClick={onPressNew}
                    className={cx(styles['btn'], styles['btnAdd'])}
                  >
                    <AiOutlinePlusCircle />
                  </div>
                </Tooltip> */}
                <Tooltip title='Làm mới' placement='bottom' color={'#44474e'}>
                  <div
                    className={cx(styles['btn'], styles['btnRefresh'])}
                    onClick={fetchListFeature}
                  >
                    <AiOutlineRedo />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
          {loading.status ? (
            <MPLoading
              status={loading.status}
              description={loading.description}
            />
          ) : (
            <Space
              direction='horizontal'
              className={styles['SpaceWrapper']}
              size={16}
            >
              {dataResult?.map((item: any, index: number) => {
                return (
                  <Card
                    key={index}
                    hoverable
                    style={{ width: 240 }}
                    className={styles['cardFeatureSettings']}
                    cover={
                      <div className={styles['cardImageWrapper']}>
                        <Image
                          src={item?.displayIcon || ''}
                          alt={item.name}
                          width={50}
                          height={50}
                          layout='fixed'
                        />
                      </div>
                    }
                  >
                    <p className={styles['featureTitle']}>{item.name}</p>
                    {/* <div className={styles['action-icon']}>
                      <Space direction='horizontal' size={'large'}>
                        <AiFillEye
                          size={28}
                          color='#24313d8d'
                          onClick={() => handleActionDetail(item)}
                        />
                      </Space>
                    </div> */}
                  </Card>
                )
              })}
            </Space>
          )}
        </div>
      </div>
      {isOpenEdit && (
        <FeatureDetail
          contentModal={contentModal}
          uploadProps={uploadProps}
          isOpenEdit={isOpenEdit}
          setIsOpenEdit={setIsOpenEdit}
          data={selectFeature}
          partnerId={'nhidong1'}
          handleFinish={onEdit}
          isFeature={true}
          activeFeature={selectFeature?.id}
          title='Chi tiết tính năng'
        />
      )}
      {isOpenNew && (
        <FeatureNew isOpenNew={isOpenNew} setIsOpenNew={setIsOpenNew} />
      )}
    </>
  )
}

export default MPFeatureSettingsCard

interface Item_Feature {
  message?: string
  disabled: boolean
  children: any[]
  bookingLimit: number
  position: string
  translate: TranslateItem[]
  type: string
  name: string
  image: string
  priority: number
  status: boolean
  mobileStatus: boolean
  createdAt: string
  updatedAt: string
  mobileIcon: string
  mobileRoute: string
  webRoute: string
  slug: string
  displayIcon?: string
  id: string
}

interface TranslateItem {
  _id?: string
  locale?: string
  name?: string
  index?: number
}

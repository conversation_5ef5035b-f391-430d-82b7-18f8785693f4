import React from 'react'
import { Select } from 'antd'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import styles from './styles.module.less'

interface ActionResetCacheProps {
  selectedPartner: string
  setSelectedPartner: (value: string) => void
  onResetCache?: () => void
  children?: React.ReactNode
}

const ActionResetCache = ({
  selectedPartner,
  setSelectedPartner,
  onResetCache,
  children
}: ActionResetCacheProps) => {
  const { Option } = Select

  return (
    <div className={styles['actionWrapper']}>
      {onResetCache && (
        <MPButton typeCustom={'cancel'} onClick={onResetCache}>
          <span>Reset cache</span>
        </MPButton>
      )}
      <Select value={selectedPartner} onChange={setSelectedPartner}>
        <Option value={'medpro'}>Medpro</Option>
      </Select>
      {children}
    </div>
  )
}

export default ActionResetCache

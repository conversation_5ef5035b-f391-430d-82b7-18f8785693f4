import { Tooltip } from 'antd'
import { AiOutlineQuestionCircle } from 'react-icons/ai'
import styles from './global.module.less'

export const handleRequireInput = (
  label: string,
  require: boolean,
  support?: string
) => {
  return (
    <div className={styles['inputItem']}>
      <div>
        {label}
        {support && (
          <Tooltip title={support} className={styles['tooltip_support']}>
            <AiOutlineQuestionCircle color='#3498db' />
          </Tooltip>
        )}
        {require ? (
          <>
            <sup className={styles['requireInput']}>* </sup> :
          </>
        ) : (
          ' :'
        )}
      </div>
    </div>
  )
}

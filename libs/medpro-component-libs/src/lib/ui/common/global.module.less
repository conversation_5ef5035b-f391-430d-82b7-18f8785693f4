.footerButton {
  border-top: 1px solid #b2b2b2;
  padding-top: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}
.inputItem {
  label {
    font-weight: 600;
  }
  .requireInput {
    font-size: 100%;
    top: -0.2em;
    left: 3px;
    color: red;
  }
  :global {
    .ant-input-number-disabled,
    .ant-select-disabled.ant-select:not(.ant-select-customize-input)
      .ant-select-selector,
    .ant-input[disabled] {
      background: #ffffff;
      color: #000000;
    }
    .ant-input-number {
      width: 100%;
    }
    .ant-form-item-explain-error {
      font-size: 0.7rem;
    }
    .ql-editor {
      background: #ffffff;
      &::-webkit-scrollbar {
        display: unset;
        width: 3px;
        background: none;
      }
      &::-webkit-scrollbar-thumb {
        background: #192a56;
        background: linear-gradient(70deg, #006184, #facc00, #7303c2, #01852b);
        border: 1px solid #b5b5b5;
        border-radius: 3px;
      }
    }
  }
  .quill {
    height: 170px;
    background: #ffffff;
  }
}
.tooltip_support {
  margin-left: 3px;
}

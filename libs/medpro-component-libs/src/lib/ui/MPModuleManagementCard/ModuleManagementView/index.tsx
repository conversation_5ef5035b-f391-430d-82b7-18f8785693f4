import React, { useEffect, useMemo, useState } from 'react'
import { Form, Modal, Transfer } from 'antd'
import type { TransferProps } from 'antd'
import dayjs from 'dayjs'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import styles from '../styles.module.less'

export interface Props {
  data: any
  isOpenView: boolean
  setIsOpenView: any
  contentModal?: any
  permissionList: any
  loading?: boolean
  onSubmitTransferModule: (values: any, cancelModal: any) => Promise<any>
}

const ModuleManagementView = ({
  data,
  setIsOpenView,
  isOpenView,
  contentModal,
  permissionList,
  loading,
  onSubmitTransferModule
}: Props) => {
  const [targetKeys, setTargetKeys] = useState<React.Key[]>([])
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([])

  const oriTargetKeys = useMemo<React.Key[]>(() => {
    return data?.permissions
      ? data.permissions.map((item: any) => item._id)
      : []
  }, [data?.permissions])

  useEffect(() => {
    setTargetKeys(oriTargetKeys)
  }, [oriTargetKeys])

  const handleChange: TransferProps['onChange'] = (newTargetKeys) => {
    setTargetKeys(newTargetKeys)
  }

  const handleSelectChange: TransferProps['onSelectChange'] = (
    sourceSelectedKeys,
    targetSelectedKeys
  ) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys])
  }

  const onCancel = () => {
    setIsOpenView(!isOpenView)
  }

  const onFinish = () => {
    return onSubmitTransferModule(
      { moduleId: data._id, permissionIds: targetKeys },
      onCancel
    )
  }

  const mockData = useMemo(() => {
    return permissionList
      .map((permission: any, i: any) => {
        if (permission.name.includes(`${data.name}_`)) {
          return {
            ...permission,
            key: permission._id
          }
        } else {
          return null
        }
      })
      .filter((item: any) => item !== null)
  }, [permissionList])

  return (
    <Modal {...contentModal} open={isOpenView} onCancel={onCancel}>
      <div className={styles['contentWrapper']}>
        <div className={styles['item']}>
          <div className={styles['label']}>Tên module:</div>
          <div className={styles['value']}>{data?.name}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Mô tả:</div>
          <div className={styles['value']}>{data?.description}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Text:</div>
          <div className={styles['value']}>{data?.text}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Ngày tạo:</div>
          <div className={styles['value']}>
            {dayjs(data?.sysCreatedAt).format('DD-MM-YYYY, HH:mm')}
          </div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Ngày cập nhật:</div>
          <div className={styles['value']}>
            {dayjs(data?.sysUpdatedAt).format('DD-MM-YYYY, HH:mm')}
          </div>
        </div>
      </div>
      <div className={styles['transferWrapper']}>
        <Transfer
          dataSource={mockData}
          titles={['Danh sách module', 'Danh sách module được chọn']}
          targetKeys={targetKeys}
          selectedKeys={selectedKeys}
          onChange={handleChange}
          onSelectChange={handleSelectChange}
          render={(item: any) => item.name}
          locale={{
            itemUnit: 'Mục',
            itemsUnit: 'Mục',
            notFoundContent: 'Không có dữ liệu',
            removeAll: 'Xóa toàn bộ',
            selectAll: 'Chọn tất cả',
            selectInvert: 'Đảo ngược mục chọn'
          }}
        />
      </div>
      <div className={styles['groupAction']}>
        <Form.Item>
          <MPButton onClick={onCancel} typeCustom={'cancel'}>
            Đóng
          </MPButton>
        </Form.Item>
        <Form.Item>
          <MPButton
            typeCustom={'approval'}
            loading={loading}
            onClick={onFinish}
          >
            Lưu
          </MPButton>
        </Form.Item>
      </div>
    </Modal>
  )
}
export default ModuleManagementView

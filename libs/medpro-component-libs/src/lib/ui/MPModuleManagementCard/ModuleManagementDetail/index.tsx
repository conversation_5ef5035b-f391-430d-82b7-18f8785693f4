import React, { useEffect } from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputItems'
import styles from '../styles.module.less'

export interface Props {
  loading?: boolean
  isOpenEdit: boolean
  setIsOpenEdit: any
  data: any
  contentModal?: any
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
}

const ModuleManagementDetail = ({
  loading,
  contentModal,
  isOpenEdit,
  setIsOpenEdit,
  onSubmitUpdate,
  data
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data
      })
    }
  }, [data])

  const onCancel = () => {
    setIsOpenEdit(!isOpenEdit)
  }

  const onFinish = (values: any) => {
    return onSubmitUpdate({ ...values, id: data._id }, onCancel)
  }

  return (
    <Modal {...contentModal} open={isOpenEdit} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems(form)
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div key={rowIndex} className={styles['inputRow']}>
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Hủy
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}
export default ModuleManagementDetail

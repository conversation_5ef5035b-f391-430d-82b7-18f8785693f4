import React, { useMemo, useRef, useState } from 'react'
import {
  Input,
  Modal,
  type TableColumnsType,
  TablePaginationConfig,
  Tooltip
} from 'antd'
import cx from 'classnames'
import { AiOutlinePlusCircle, AiOutlineRedo } from 'react-icons/ai'
import dayjs from 'dayjs'
import {
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import MPTable from '../../atoms/MPTable'
import ModuleManagementNew from './ModuleManagementNew'
import ModuleManagementDetail from './ModuleManagementDetail'
import ModuleManagementView from './ModuleManagementView'
import styles from './styles.module.less'

const contentModal = {
  edit: {
    title: 'Sửa thông tin module',
    centered: true,
    footer: false,
    className: styles['modalEditWrapper']
  },
  create: {
    title: 'Thêm mới module',
    centered: true,
    footer: false,
    className: styles['modalNewWrapper']
  },
  view: {
    title: 'Thông tin module',
    centered: true,
    footer: false,
    className: styles['modalViewWrapper'],
    width: '100%'
  }
}

export interface Props {
  heightContent: number
  moduleList: any
  permissionList: any
  moduleDetail: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitTransferModule: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

const MPModuleManagementCard = ({
  heightContent,
  moduleList,
  permissionList,
  loading,
  moduleDetail,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onSubmitTransferModule,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent
    })
  const { Search } = Input
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [isOpenView, setIsOpenView] = useState(false)

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onSubmitDelete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    setIsOpenEdit(!isOpenEdit)
    return onPressViewDetail(row._id)
  }

  const onPressView = (row: any) => {
    setIsOpenView(!isOpenView)
    return onPressViewDetail(row._id)
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const dataSource = useMemo(() => {
    if (moduleList?.length) {
      return moduleList.map((item: any, index: number) => {
        return {
          ...item,
          key: item.id || index,
          sysCreatedAt: item.createdAt,
          sysUpdatedAt: item.updatedAt,
          createdAt: dayjs(item.createdAt).format('DD-MM-YYYY, HH:mm'),
          updatedAt: dayjs(item.updatedAt).format('DD-MM-YYYY, HH:mm')
        }
      })
    }
    return []
  }, [moduleList])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'Tên module',
        width: 250,
        dataIndex: 'name',
        key: 'name',
        fixed: 'left'
      },
      {
        title: 'Mô tả',
        dataIndex: 'description',
        key: 'description',
        width: 200
      },
      {
        title: 'Text',
        dataIndex: 'text',
        key: 'text',
        width: 250
      },
      {
        title: 'Ngày tạo',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 180
      },
      {
        title: 'Ngày cập nhật',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        width: 180
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete', 'view']
      }
    ],
    onPressEdit,
    onPressDelete,
    onPressView
  )

  return (
    <>
      <div className={styles['pageWrapper']}>
        <div ref={headerRef} className={styles['header']}>
          <div className={styles['title']}>
            <h2>Quản lý module</h2>
          </div>
        </div>
        <div className={styles['body']}>
          <div ref={filterRef} className={styles['filterWrapper']}>
            <div className={styles['left']}>
              <Search
                size={'middle'}
                placeholder='Tìm kiếm module..'
                enterButton
                allowClear
                onSearch={(value: any) => onSubmitSearch(value)}
              />
            </div>
            <div className={styles['right']}>
              <div className={styles['enhance']}>
                <Tooltip title='Thêm mới' placement='bottom' color={'#44474e'}>
                  <div
                    onClick={onPressNew}
                    className={cx(styles['btn'], styles['btnAdd'])}
                  >
                    <AiOutlinePlusCircle />
                  </div>
                </Tooltip>
                <Tooltip title='Làm mới' placement='bottom' color={'#44474e'}>
                  <div
                    onClick={() => onRefresh()}
                    className={cx(styles['btn'], styles['btnRefresh'])}
                  >
                    <AiOutlineRedo />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
          <MPTable
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        </div>
      </div>
      {isOpenEdit && (
        <ModuleManagementDetail
          loading={loading}
          contentModal={contentModal.edit}
          isOpenEdit={isOpenEdit}
          setIsOpenEdit={setIsOpenEdit}
          data={moduleDetail}
          onSubmitUpdate={onSubmitUpdate}
        />
      )}
      {isOpenNew && (
        <ModuleManagementNew
          loading={loading}
          contentModal={contentModal.create}
          isOpenNew={isOpenNew}
          setIsOpenNew={setIsOpenNew}
          onSubmitCreate={onSubmitCreate}
        />
      )}
      {isOpenView && (
        <ModuleManagementView
          loading={loading}
          contentModal={contentModal.view}
          data={moduleDetail}
          permissionList={permissionList}
          isOpenView={isOpenView}
          setIsOpenView={setIsOpenView}
          onSubmitTransferModule={onSubmitTransferModule}
        />
      )}
    </>
  )
}

export default MPModuleManagementCard

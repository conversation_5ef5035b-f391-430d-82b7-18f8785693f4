import React, { useEffect, useState } from 'react'
import { Button, Upload, UploadProps, message } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import { checkImageType } from '../../helpers/func'
export interface MPImageFormItemProps extends UploadProps {
  responseValuePropName?: string
  url?: string
  disable?: boolean
  typeUpload?: string
}

const MPImageFormItem = (props: MPImageFormItemProps) => {
  const [fileList, setFileList] = useState([]) as any[]

  const { url, responseValuePropName = 'url', onChange, ...rest } = props

  useEffect(() => {
    if (url) {
      const files = [{ uid: 'uuid', status: 'done', url: url }]
      setFileList(files)
    }
  }, [url])
  const onValueChange = (event: any) => {
    if (event.file.status === 'uploading') {
      setFileList(event.fileList)
    }
    if (event.file.status === 'done' || event.file.status === 'removed') {
      const { file, fileList = [] } = event

      if (file?.response?.[responseValuePropName]) {
        setFileList(event.fileList)
        const newValue = {
          target: { url: file.response[responseValuePropName] }
        } as any
        onChange?.(newValue)
      }
      setFileList(fileList.slice(0 - length))
    }
  }
  const beforeUpload = (file: any) => {
    const isLt1M5 = file.size / 1024 / 1024 < 1.5
    if (!isLt1M5) {
      message.error('Kích thước hình ảnh vượt quá 1.5MB !')
      return false
    }
    if (!checkImageType(file.type)) {
      message.error(`${file.name} không phải là hình ảnh`)
      return false
    }
    return true
  }

  return (
    <Upload
      fileList={fileList}
      onChange={onValueChange}
      {...rest}
      listType={'picture'}
      disabled={props.disable}
      beforeUpload={beforeUpload}
      accept='image/*'
    >
      <Button icon={<UploadOutlined />} disabled={props.disable}>
        Click to upload
      </Button>
    </Upload>
  )
}

export default MPImageFormItem

/* Styles cho component chính */
.bannerPage {
  .bannerHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: white;
    z-index: 999;
    padding: 10px 0;

    @media only screen and (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
    }

    .bannerTitle {
      display: flex;
      align-items: center;

      :global {
        .ant-ribbon {
          margin-top: -30px;
        }
      }

      h2 {
        font-size: 20px;
      }
    }

    .bannerActions {
      display: flex;
      align-items: center;
      flex-flow: wrap;
      gap: 10px;

      @media only screen and (max-width: 768px) {
        margin-top: 10px;
      }

      :global {
        .ant-select {
          min-width: 120px;
        }
      }
    }
  }

  .bannerBody {
    :global {
      .ant-tabs {
        .ant-tabs-nav {
          margin: 0;
        }
      }
    }

    .tabsWrapper {
      :global {
        .ant-tabs
          .ant-tabs-nav
          .ant-tabs-nav-wrap
          .ant-tabs-nav-list
          .ant-tabs-tab-active {
          font-weight: 400;
        }
      }
    }

    .compTabWrapper {
      :global {
        .ant-tabs
          .ant-tabs-nav
          .ant-tabs-nav-wrap
          .ant-tabs-nav-list
          .ant-tabs-tab-active {
          font-weight: 400;
        }
        .ant-tabs .ant-tabs-tab-btn {
          padding: 0 5px;
        }
      }
    }

    .groupPosition {
      border: 1px dashed #b8b8b8;
      max-width: 50px;
      height: 40px;
      gap: 10px;
      border-radius: 6px;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }

    .groupName {
      display: flex;
      align-items: center;
      gap: 12px;

      .contentName {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .groupPeriod {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      div {
        text-align: justify;

        &.line {
          width: 10px;
          border-bottom: 1px solid;
        }
      }
    }

    .groupStatus {
      max-width: 120px;
      height: 30px;
      gap: 10px;
      padding: 6px 10px;
      border-radius: 16px;
      border-width: 1px;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;

      &.active {
        border: 1px solid #00d22e;
        background: #00d22e29;
        color: #00d22e;
      }

      &.inactive {
        background: #eeeeeeee;
        color: #66666666;
      }

      &.pending {
        border: 1px solid #f1963a;
        background: #f1963a29;
        color: #f1963a;
      }
    }

    .filterWrapper {
      margin-bottom: 16px;

      .filterAction {
        -webkit-box-align: center;
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        padding: 10px 0;

        .left {
          flex: 35%;
          display: flex;

          :global {
            .ant-input-search {
              max-width: 300px;

              .ant-input-outlined {
                border-color: #e7e7e7;
              }
            }
          }

          .countRecord {
            font-weight: 500;
            font-size: 20px;
            line-height: 28px;
            color: #111827;
          }
        }

        .right {
          flex: 10%;
          display: flex;
          justify-content: flex-end;

          .enhance {
            display: flex;
            gap: 5px;
          }
        }
      }

      .filterTab {
        display: flex;
        align-items: center;
        gap: 8px;

        .inActive {
          background: #ffffff !important;
          border: 1px solid #ebebeb;
        }
      }
    }
  }
}

/* Styles cho component con */
.compTabWrapper {
  :global {
    .ant-tabs
      .ant-tabs-nav
      .ant-tabs-nav-wrap
      .ant-tabs-nav-list
      .ant-tabs-tab-active {
      font-weight: 400;
    }
    .ant-tabs .ant-tabs-tab-btn {
      padding: 0 5px;
    }
  }
}

.groupPosition {
  border: 1px dashed #b8b8b8;
  max-width: 50px;
  height: 40px;
  gap: 10px;
  border-radius: 6px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.groupName {
  display: flex;
  align-items: center;
  gap: 12px;

  .contentName {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

.groupPeriod {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;

  div {
    text-align: justify;

    &.line {
      width: 10px;
      border-bottom: 1px solid;
    }
  }
}

.groupStatus {
  max-width: 120px;
  height: 30px;
  gap: 10px;
  padding: 6px 10px;
  border-radius: 16px;
  border-width: 1px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;

  &.active {
    border: 1px solid #00d22e;
    background: #00d22e29;
    color: #00d22e;
  }

  &.inactive {
    background: #eeeeeeee;
    color: #66666666;
  }

  &.pending {
    border: 1px solid #f1963a;
    background: #f1963a29;
    color: #f1963a;
  }
}

.filterWrapper {
  margin-bottom: 16px;

  .filterAction {
    -webkit-box-align: center;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    padding: 10px 0;

    .left {
      flex: 35%;
      display: flex;

      :global {
        .ant-input-search {
          max-width: 300px;

          .ant-input-outlined {
            border-color: #e7e7e7;
          }
        }
      }

      .countRecord {
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        color: #111827;
      }
    }

    .right {
      flex: 10%;
      display: flex;
      justify-content: flex-end;

      .enhance {
        display: flex;
        gap: 5px;
      }
    }
  }

  .filterTab {
    display: flex;
    align-items: center;
    gap: 8px;

    .inActive {
      background: #ffffff !important;
      border: 1px solid #ebebeb;
    }
  }
}

.modalEditWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }
    .uploadWrapper {
      :global {
        .ant-upload-list-picture-card {
          width: 100%;
          flex-direction: column;

          .ant-upload-select,
          .ant-upload-list-item-container {
            width: 100%;
            height: 200px;
            border: 1px dashed #f07c63;
            border-color: #f07c63 !important;
            padding: 20px;
          }

          .ant-upload-list-item-container {
            padding: 0;

            .ant-upload-list-item-done {
              border: none;
            }
          }
        }
      }
      .uploadBox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 16px;

        .text {
          color: #27272a;
          font-weight: 700;
          font-size: 14px;
          line-height: 20px;
          vertical-align: middle;

          .extension {
            color: #808089;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            vertical-align: middle;
          }
        }
      }
    }
    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

.modalNewWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }
    .uploadWrapper {
      :global {
        .ant-upload-list-picture-card {
          width: 100%;
          flex-direction: column;

          .ant-upload-select,
          .ant-upload-list-item-container {
            width: 100%;
            height: 200px;
            border: 1px dashed #f07c63;
            border-color: #f07c63 !important;
            padding: 20px;
          }

          .ant-upload-list-item-container {
            padding: 0;

            .ant-upload-list-item-done {
              border: none;
            }
          }
        }
      }
      .uploadBox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 16px;

        .text {
          color: #27272a;
          font-weight: 700;
          font-size: 14px;
          line-height: 20px;
          vertical-align: middle;

          .extension {
            color: #808089;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            vertical-align: middle;
          }
        }
      }
    }
    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

.modalViewWrapper {
  :global {
    .ant-modal-content {
      height: 100vh;
      display: flex;
      flex-direction: column;

      .ant-modal-body {
        height: 100%;
        overflow: auto;
      }
    }
  }

  .contentWrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .item {
      display: flex;
      align-items: center;

      @media only screen and (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
      }

      .label {
        min-width: 150px;
        font-weight: 500;
      }

      .value {
        width: 100%;

        .on {
          background: green;
          color: #ffffff;
          text-transform: capitalize;
        }

        .off {
          background: red;
          color: #ffffff;
          text-transform: capitalize;
        }
      }
    }
  }

  .transferWrapper {
    margin-top: 20px;
    margin-bottom: 20px;

    :global {
      .ant-transfer-list {
        width: 100%;
        height: 700px;

        .ant-transfer-list-body-customize-wrapper {
          overflow: auto;
          height: 100%;
        }
      }
    }
  }

  .groupAction {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 10px;
    position: sticky;
    bottom: 0;
    background: #ffffff;

    :global {
      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
}

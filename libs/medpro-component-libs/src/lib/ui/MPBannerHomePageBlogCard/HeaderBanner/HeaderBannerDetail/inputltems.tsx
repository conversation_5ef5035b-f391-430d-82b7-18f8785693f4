import { Form, Input, Radio, DatePicker, Select, Switch } from 'antd'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import React, { useState } from 'react'
import Image from 'next/image'
import { size } from 'lodash'
import { getReplaceUTF8, MPButton } from '@medpro-libs/medpro-component-libs'
import IconUploadImg from '../../../../images/iconUploadImg.png'
import MPImageFormItemMultiple from '../../../../atoms/MPImageFormItemMultiple'
import styles from '../../styles.module.less'

const { RangePicker } = DatePicker
const { Option } = Select

export const appOptions = [
  {
    title: 'Tính năng',
    value: 'feature'
  },
  {
    title: 'Đặt lịch',
    value: 'booking'
  },
  {
    title: 'Mở Link',
    value: 'link'
  },
  {
    title: 'Bệnh viện',
    value: 'hospital'
  },
  {
    title: '<PERSON><PERSON><PERSON> kh<PERSON>',
    value: 'package'
  },
  {
    title: '<PERSON><PERSON><PERSON> sĩ',
    value: 'doctor'
  },
  {
    title: '<PERSON><PERSON><PERSON> sĩ Telemed',
    value: 'doctorTelemed'
  }
]

export const inputItems = ({ form, uploadProps, onChangeImage }: any) => {
  return [
    {
      id: 'banner',
      type: 'upload',
      label: 'Tải hình lên',
      placeholder: '',
      require: true,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
              className={styles['uploadWrapper']}
              customBody={
                <div className={styles['uploadBox']}>
                  <Image src={IconUploadImg} alt={''} priority />
                  <div className={styles['text']}>
                    Kéo tập tin vào đây hoặc nhấp vào bên dưới để tải lên
                    <div className={styles['extension']}>
                      .ai, .png, .jpg, .gif, .svg, .pdf, .eps, .jpeg
                      <br />
                      10mb max file size.
                    </div>
                  </div>
                  <div>
                    <MPButton typeCustom={'upload'}>Chọn tập tin</MPButton>
                  </div>
                </div>
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'url',
      type: 'text',
      label: 'URL',
      placeholder: 'Nhập url',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

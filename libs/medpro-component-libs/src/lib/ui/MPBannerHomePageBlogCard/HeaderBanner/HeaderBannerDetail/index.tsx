import React, { useEffect } from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import { get, isArray } from 'lodash'
import dayjs from 'dayjs'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputltems'
import styles from '../../styles.module.less'

export interface Props {
  loading?: boolean
  contentModal?: any
  isOpenEdit: boolean
  setIsOpenEdit: any
  hospitalList: any
  featurePartnerList: any
  featureList: any
  filterTabActive?: any
  uploadProps?: any
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  formSelectPartner: (value: any) => void
  doctorReferenceList: any
  serviceReferenceList: any
  subjectReferenceList: any
  dataRow: any
}

const HeaderBannerDetail = ({
  loading,
  contentModal,
  isOpenEdit,
  setIsOpenEdit,
  onSubmitUpdate,
  hospitalList,
  featurePartnerList,
  featureList,
  filterTabActive,
  uploadProps,
  formSelectPartner,
  doctorReferenceList,
  serviceReferenceList,
  subjectReferenceList,
  dataRow
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (dataRow) {
      // Initialize form with existing data
      const formData = {
        banner: dataRow.imageUrl,
        url: dataRow.url,
        timeType: dataRow.displayPeriod?.startDate ? 'time' : 'always',
        timePicker: dataRow.displayPeriod?.startDate
          ? [
              dayjs(dataRow.displayPeriod.startDate),
              dayjs(dataRow.displayPeriod.endDate)
            ]
          : undefined,
        status: dataRow.status || 'active',
        pin: dataRow.pin !== undefined ? dataRow.pin : true,
        ...dataRow
      }

      form.setFieldsValue(formData)

      // If there's CTA data, set action and related fields
      if (dataRow.cta) {
        formSelectPartner({
          partnerId: dataRow.cta.partnerId,
          action: dataRow.cta.action
        })
      }
    }
  }, [dataRow, form, formSelectPartner])

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onCancel = () => {
    setIsOpenEdit(!isOpenEdit)
  }

  const onFinish = (values: any) => {
    // Process date values if they exist
    let processedValues = { ...values }

    if (values.timePicker && values.timePicker.length === 2) {
      processedValues.displayPeriod = {
        startDate: values.timePicker[0].format('DD/MM/YYYY HH:mm'),
        endDate: values.timePicker[1].format('DD/MM/YYYY HH:mm')
      }
    }

    // Add ID to the submitted data
    const updatedValues = {
      ...processedValues,
      _id: dataRow._id
    }

    return onSubmitUpdate(updatedValues, onCancel)
  }

  return (
    <Modal {...contentModal} open={isOpenEdit} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems({
            form,
            uploadProps,
            onChangeImage
          })
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div
                key={rowIndex}
                className={cx(
                  styles['inputRow'],
                  row.every((item: any) => item.hidden)
                    ? styles['hidden']
                    : null
                )}
              >
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export default HeaderBannerDetail

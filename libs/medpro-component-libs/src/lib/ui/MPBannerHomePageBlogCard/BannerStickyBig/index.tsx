import {
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import {
  Image as ImageAntd,
  Modal,
  Switch,
  type TableColumnsType,
  TablePaginationConfig,
  Tabs
} from 'antd'
import cx from 'classnames'
import dayjs from 'dayjs'
import { useMemo, useRef, useState } from 'react'
import { FaPen } from 'react-icons/fa'
import { FiPlus } from 'react-icons/fi'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'
export interface Props {
  headerRef: any
  heightContent: number
  hospitalList: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

const BannerStickyBig = ({
  headerRef,
  heightContent,
  hospitalList,
  loading,
  onPressViewDetail,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 70
    })
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [tabActiveKey, setTabActiveKey] = useState('1')
  const [filterTabActive, setFilterTabActive] = useState('website')
  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onSubmitDelete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    setIsOpenEdit(!isOpenEdit)
    return onPressViewDetail(row._id)
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const onPressFilterTab = (key: string) => {
    setFilterTabActive(key)
  }

  const dataSource = useMemo(() => {
    if (hospitalList?.length) {
      return hospitalList.map((item: any, index: number) => {
        return {
          ...item,
          key: item.id || index,
          sysCreatedAt: item.createdAt,
          sysUpdatedAt: item.updatedAt,
          createdAt: dayjs(item.createdAt).format('DD-MM-YYYY, HH:mm'),
          updatedAt: dayjs(item.updatedAt).format('DD-MM-YYYY, HH:mm')
        }
      })
    }
    return []
  }, [hospitalList])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'STT',
        width: 90,
        key: 'position',
        align: 'center',
        fixed: 'left',
        render: (row: any) => (
          <div className={styles['groupPosition']}>{row.position}</div>
        )
      },
      {
        title: 'Thông tin bác sĩ',
        width: 350,
        key: 'name',
        fixed: 'left',
        render: (row: any) => (
          <div className={styles['groupName']}>
            <DragHandle />
            <div className={styles['contentName']}>
              <div className={styles['image']}>
                <ImageAntd
                  width={300}
                  height={70}
                  src='https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'
                  alt='logo'
                />
              </div>
            </div>
          </div>
        )
      },
      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 200,
        render: (row: any) => (
          <div className={styles['groupPeriod']}>
            <div className={styles['start']}>
              {row?.displayPeriod?.startDate}
            </div>
            <div className={styles['line']} />
            <div className={styles['end']}>{row?.displayPeriod?.endDate}</div>
          </div>
        )
      },
      {
        title: 'Trạng thái',
        key: 'status',
        width: 150,
        align: 'center',
        render: (row: any) => (
          <div className={cx(styles['groupStatus'], styles[row.status])}>
            {row.status === 'active'
              ? 'Đang hiển thị'
              : row.status === 'inactive'
              ? 'Hết hạn'
              : 'Chờ hiển thị'}
          </div>
        )
      },
      {
        title: 'Bật / Tắt ghim',
        key: 'pin',
        align: 'center',
        width: 120,
        render: (row) => <Switch checked={row.pin} />
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items = [
    {
      key: '1',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: '2',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: '3',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: '4',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case '1':
      case '2':
      case '3':
      case '4':
        return (
          <MPTable
            type={'dragTable'}
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          defaultActiveKey={'1'}
          onChange={setTabActiveKey}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <div ref={filterRef} className={styles['filterWrapper']}>
        <div className={styles['filterAction']}>
          <div className={styles['left']}>
            <div className={styles['countRecord']}>1 Banner</div>
          </div>
          <div className={styles['right']}>
            <div className={styles['enhance']}>
              <MPButton onClick={() => onRefresh()} typeCustom={'cancel'}>
                <span>Cập nhật</span>
              </MPButton>
              <MPButton onClick={onPressNew} typeCustom={'primary'}>
                <FiPlus />
                <span>Thêm banner</span>
              </MPButton>
            </div>
          </div>
        </div>
        <div className={styles['filterTab']}>
          <MPButton
            onClick={() => onPressFilterTab('website')}
            className={cx(
              filterTabActive === 'app' ? styles['inActive'] : null
            )}
            typeCustom={'cancel'}
          >
            <span>Website</span>
          </MPButton>
          <MPButton
            onClick={() => onPressFilterTab('app')}
            className={cx(
              filterTabActive === 'website' ? styles['inActive'] : null
            )}
            typeCustom={'cancel'}
          >
            <span>App</span>
          </MPButton>
        </div>
      </div>
      {renderTabPane()}
    </>
  )
}

export default BannerStickyBig

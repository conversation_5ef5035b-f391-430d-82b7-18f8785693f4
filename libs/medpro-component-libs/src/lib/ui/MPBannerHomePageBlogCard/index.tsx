import { getReplaceUTF8, MPButton } from '@medpro-libs/medpro-component-libs'
import { Select, TablePaginationConfig, Tabs } from 'antd'
import { size } from 'lodash'
import { useRef, useState } from 'react'
import HeaderBanner from './HeaderBanner'
import BannerStickySmall from './BannerStickySmall'
import BannerStickyBig from './BannerStickyBig'
import styles from './styles.module.less'

export interface Props {
  heightContent: number
  hospitalList: any
  loading?: boolean
  homeBannerBlog: any
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

const MPBannerHomePageBlogCard = ({
  heightContent,
  hospitalList,
  homeBannerBlog,
  loading,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const [tabActiveKey, setTabActiveKey] = useState('1')
  const { Option } = Select

  const items = [
    {
      key: '1',
      label: 'Header Banner',
      forceRender: true,
      children: null
    },
    {
      key: '2',
      label: 'Banner Sticky (Nhỏ)',
      forceRender: true,
      children: null
    },
    {
      key: '3',
      label: 'Banner Sticky (Lớn)',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case '1':
        return (
          <HeaderBanner
            headerRef={headerRef}
            heightContent={heightContent}
            hospitalList={hospitalList}
            homeBannerBlog={homeBannerBlog}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      case '2':
        return (
          <BannerStickySmall
            headerRef={headerRef}
            heightContent={heightContent}
            hospitalList={hospitalList}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      case '3':
        return (
          <BannerStickyBig
            headerRef={headerRef}
            heightContent={heightContent}
            hospitalList={hospitalList}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )

      default:
        return null
    }
  }

  return (
    <div className={styles['bannerPage']}>
      <div ref={headerRef} className={styles['bannerHeader']}>
        <div className={styles['bannerTitle']}>
          <h2>Banner Blog</h2>
        </div>
        <div className={styles['bannerActions']}>
          <MPButton typeCustom={'cancel'}>
            <span>Reset cache</span>
          </MPButton>
          <Select
            defaultValue={hospitalList[0]?.partnerId}
            showSearch
            filterOption={(input, option: any) =>
              getReplaceUTF8(
                (option?.children as unknown as string).toLowerCase()
              ).includes(getReplaceUTF8(input.toLowerCase()))
            }
          >
            {hospitalList && size(hospitalList) > 0
              ? hospitalList?.map((item: any, index: number) => (
                  <Option key={index} value={item.partnerId}>
                    {item.name}
                  </Option>
                ))
              : undefined}
          </Select>
          <Select defaultValue={'beta'}>
            <Option value={'beta'}>BETA</Option>
          </Select>
        </div>
      </div>
      <div className={styles['bannerBody']}>
        <div className={styles['tabsWrapper']}>
          <Tabs
            items={items}
            type={'card'}
            defaultActiveKey={'1'}
            tabPosition={'top'}
            onChange={setTabActiveKey}
          />
        </div>
        {renderTabPane()}
      </div>
    </div>
  )
}

export default MPBannerHomePageBlogCard

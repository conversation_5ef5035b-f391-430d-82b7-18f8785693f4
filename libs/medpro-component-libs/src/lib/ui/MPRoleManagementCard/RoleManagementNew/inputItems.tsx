import { Form, Input, Select } from 'antd'
import React from 'react'
import { size } from 'lodash'
import { Valid } from '../../../helpers/valid'
import { getReplaceUTF8 } from '../../../helpers/func'

const valid = new Valid()
const { Option } = Select
export const inputItems = (form: any, hospitalList: any) => {
  return [
    {
      id: 'partnerId',
      type: 'select',
      label: 'Cơ sở y tế',
      placeholder: '<PERSON>hập cơ sở y tế',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              { required: require, message: 'Vui lòng nhập cơ sở y tế!' }
            ]}
          >
            <Select
              placeholder={placeholder}
              showSearch
              allowClear
              filterOption={(input, option: any) =>
                getReplaceUTF8(
                  (option?.children as unknown as string).toLowerCase()
                ).includes(getReplaceUTF8(input.toLowerCase()))
              }
            >
              {hospitalList && size(hospitalList) > 0
                ? hospitalList?.map((item: any, index: number) => (
                  <Option key={index} value={item.partnerId}>
                    {item.name}
                  </Option>
                ))
                : undefined}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'name',
      type: 'text',
      label: 'Tên quyền',
      placeholder: 'Vui lòng nhập tên quyền',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'description',
      type: 'text',
      label: 'Mô tả',
      placeholder: 'Vui lòng nhập mô tả',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

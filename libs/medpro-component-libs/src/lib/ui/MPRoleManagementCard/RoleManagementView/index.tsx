import React, { useEffect, useState } from 'react'
import { Form, Modal, Transfer, Tree } from 'antd'
import { DataNode } from 'antd/es/tree'
import dayjs from 'dayjs'
import { DownOutlined } from '@ant-design/icons'
import {
  filterPermissionLeftData,
  filterPermissionTree,
  getReplaceUTF8,
  mergePermissionCheckedResult,
  MPButton,
  removePermissionCheckedResult
} from '@medpro-libs/medpro-component-libs'
import styles from '../styles.module.less'

export interface Props {
  data: any
  isOpenView: boolean
  setIsOpenView: any
  contentModal?: any
  permissionList: any
  rolePermissionList: any
  loading?: boolean
  onSubmitTransferModule: (values: any, cancelModal: any) => Promise<any>
}

const RoleManagementView = ({
  data,
  setIsOpenView,
  isOpenView,
  contentModal,
  permissionList,
  rolePermissionList,
  loading,
  onSubmitTransferModule
}: Props) => {
  const [originalPermissionList, setOriginalPermissionList] =
    useState<any>(permissionList)
  const [leftCheckedKeys, setLeftCheckedKeys] = useState<any>([])
  const [rightCheckedKeys, setRightCheckedKeys] = useState<any>([])
  const [checkedNodesToRight, setCheckedNodesToRight] = useState<any>([])
  const [checkedNodesToLeft, setCheckedNodesToLeft] = useState<any>([])
  const [targetNodes, setTargetNodes] = useState<any>([])
  const [mockTargetNodes, setMockTargetNodes] = useState<any>(targetNodes)
  const [mockPermissionList, setMockPermissionList] = useState<any>()
  const [searchValueLeft, setSearchValueLeft] = useState<any>('')
  const [searchValueRight, setSearchValueRight] = useState<any>('')

  useEffect(() => {
    setOriginalPermissionList(permissionList)
  }, [permissionList])

  useEffect(() => {
    setTargetNodes(rolePermissionList)
  }, [rolePermissionList])

  useEffect(() => {
    setMockTargetNodes(targetNodes)
    setMockTargetNodes(
      targetNodes.filter((item: any) =>
        getReplaceUTF8(item?.name ?? '')
          .toLowerCase()
          .includes(getReplaceUTF8(searchValueRight.toLowerCase()))
      )
    )
  }, [targetNodes, searchValueRight])

  useEffect(() => {
    if (!searchValueRight) {
      setMockPermissionList(
        filterPermissionLeftData(mockTargetNodes, originalPermissionList)
      )
    } else {
      setMockPermissionList(
        filterPermissionLeftData(targetNodes, originalPermissionList)
      )
    }
  }, [searchValueRight, mockTargetNodes, originalPermissionList, targetNodes])

  const onCancel = () => {
    setIsOpenView(!isOpenView)
  }

  const onFinish = () => {
    // return onSubmitTransferModule(
    //   { moduleId: data._id, permissionIds: targetKeys },
    //   onCancel
    // )
  }

  const onChangeTransfer = (nextTargetKeys: any, direction: string) => {
    setLeftCheckedKeys([])
    if (direction === 'right') {
      setTargetNodes(mergePermissionCheckedResult([...checkedNodesToRight]))
      setMockPermissionList(
        removePermissionCheckedResult([...leftCheckedKeys], mockPermissionList)
      )
    } else {
      setTargetNodes(
        removePermissionCheckedResult([...rightCheckedKeys], targetNodes)
      )
      setMockPermissionList(
        mergePermissionCheckedResult([...checkedNodesToLeft])
      )
    }
  }

  const onSearchTransfer = (direction: string, value: string) => {
    if (direction === 'left') {
      setSearchValueLeft(value)
      setOriginalPermissionList(
        permissionList.filter(
          (item: any) =>
            getReplaceUTF8(item?.description ?? '')
              .toLowerCase()
              .includes(getReplaceUTF8(value.toLowerCase())) ||
            getReplaceUTF8(item?.name ?? '')
              .toLowerCase()
              .includes(getReplaceUTF8(value.toLowerCase()))
        )
      )
    } else {
      setSearchValueRight(value)
      const rightData = [...targetNodes]
      setMockTargetNodes(
        rightData.filter(
          (item: any) =>
            getReplaceUTF8(item?.description ?? '')
              .toLowerCase()
              .includes(getReplaceUTF8(value.toLowerCase())) ||
            getReplaceUTF8(item?.name ?? '')
              .toLowerCase()
              .includes(getReplaceUTF8(value.toLowerCase()))
        )
      )
    }
  }

  const renderTreeNodes = (data?: any[]): DataNode[] | undefined => {
    return data?.map((item: any) => ({
      title: `${item?.name}`,
      key: item?._id,
      children: item?.permission ? renderTreeNodes(item.permission) : []
    }))
  }

  return (
    <Modal {...contentModal} open={isOpenView} onCancel={onCancel}>
      <div className={styles['contentWrapper']}>
        <div className={styles['item']}>
          <div className={styles['label']}>Tên quyền:</div>
          <div className={styles['value']}>{data?.name}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Mô tả:</div>
          <div className={styles['value']}>{data?.description}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>PartnerId:</div>
          <div className={styles['value']}>{data?.partnerId}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Ngày tạo:</div>
          <div className={styles['value']}>
            {dayjs(data?.sysCreatedAt).format('DD-MM-YYYY, HH:mm')}
          </div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Ngày cập nhật:</div>
          <div className={styles['value']}>
            {dayjs(data?.sysUpdatedAt).format('DD-MM-YYYY, HH:mm')}
          </div>
        </div>
      </div>
      <div className={styles['transferWrapper']}>
        <Transfer
          className={styles['transfer']}
          listStyle={{
            width: 400,
            height: 650
          }}
          titles={['List Module Permission', 'Module Permission Selected']}
          operations={['', '']}
          onChange={(nextTargetKeys, direction) =>
            onChangeTransfer(nextTargetKeys, direction)
          }
          showSelectAll={false}
          showSearch
          onSearch={(direction, value) => onSearchTransfer(direction, value)}
        >
          {({ direction, onItemSelect, selectedKeys }) =>
            direction === 'left' ? (
              <Tree
                showLine
                switcherIcon={<DownOutlined />}
                blockNode
                checkable
                checkedKeys={leftCheckedKeys}
                onCheck={(selectedKeys: any, info: any) => {
                  setLeftCheckedKeys(selectedKeys)
                  const filteredTree = filterPermissionTree(
                    selectedKeys,
                    info.halfCheckedKeys,
                    originalPermissionList
                  )
                  setCheckedNodesToRight([...targetNodes, ...filteredTree])
                  const eventKey = info.node.key
                  onItemSelect(eventKey, selectedKeys.includes(eventKey))
                }}
                treeData={
                  mockPermissionList && renderTreeNodes(mockPermissionList)
                }
              />
            ) : (
              <Tree
                showLine
                switcherIcon={<DownOutlined />}
                autoExpandParent
                blockNode
                checkable
                onCheck={(selectedKeys: any, info: any) => {
                  const eventKey = info.node.key
                  onItemSelect(eventKey, selectedKeys.includes(eventKey))
                  setRightCheckedKeys(selectedKeys)
                  const filteredTree = filterPermissionTree(
                    selectedKeys,
                    info.halfCheckedKeys,
                    permissionList
                  )
                  setCheckedNodesToLeft([
                    ...mockPermissionList,
                    ...filteredTree
                  ])
                }}
                treeData={renderTreeNodes(mockTargetNodes)}
              />
            )
          }
        </Transfer>
      </div>
      <div className={styles['groupAction']}>
        <Form.Item>
          <MPButton onClick={onCancel} typeCustom={'cancel'}>
            Đóng
          </MPButton>
        </Form.Item>
        <Form.Item>
          <MPButton
            typeCustom={'approval'}
            loading={loading}
            onClick={onFinish}
          >
            Lưu
          </MPButton>
        </Form.Item>
      </div>
    </Modal>
  )
}
export default RoleManagementView

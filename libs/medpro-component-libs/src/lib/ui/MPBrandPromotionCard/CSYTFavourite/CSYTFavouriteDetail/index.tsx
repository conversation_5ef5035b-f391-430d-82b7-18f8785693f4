import React, { useEffect, useState } from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import { get, isArray } from 'lodash'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputIteams'
import styles from '../../styles.module.less'
import dayjs from 'dayjs'

export interface Props {
  loading?: boolean
  contentModal?: any
  isOpenEdit: boolean
  setIsOpenEdit: any
  hospitalList: any
  actionCSYTFavourite: any
  detailData?: any
}

const CSYTFavouriteDetail = ({
  loading,
  contentModal,
  isOpenEdit,
  setIsOpenEdit,
  hospitalList,
  actionCSYTFavourite,
  detailData
}: Props) => {
  const [form] = Form.useForm()
  const [initialValues, setInitialValues] = useState<any>(null)

  useEffect(() => {
    if (detailData && Object.keys(detailData).length > 0) {
      const formattedData = {
        _id: detailData._id,
        circleLogo: detailData.circleLogo,
        partnerId: detailData.hospital?.partnerId,
        cta: detailData.cta || { title: 'Đặt khám ngay', url: '' },
        displayPeriod: [dayjs(detailData.fromDate), dayjs(detailData.toDate)],
        status: detailData.status,
        targetWeb: detailData.cta?.targetWeb
      }

      setInitialValues(formattedData)
      form.setFieldsValue(formattedData)
    }
  }, [detailData, form])

  const onCancel = () => {
    form.resetFields()
    setIsOpenEdit(!isOpenEdit)
  }

  const onFinish = (values: any) => {
    const cta = {
      url: values.cta?.url || '',
      type: 'SCREEN',
      target: 'HOSPITAL_DETAIL',
      partnerId: values?.partnerId,
      targetWeb: values.targetWeb
    }
    const payload = {
      repo: detailData?.repo,
      _id: detailData?._id,
      id: values?.partnerId,
      fromDate: values.displayPeriod[0]?.toISOString(),
      toDate: values.displayPeriod[1]?.toISOString(),
      status: values.status,
      cta: cta
    }

    return actionCSYTFavourite.update(payload, onCancel)
  }

  return (
    <Modal {...contentModal} open={isOpenEdit} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
        initialValues={initialValues}
      >
        <div className={styles['inputItems']}>
          {inputItems({
            form,
            hospitalList
          })
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div
                key={rowIndex}
                className={cx(
                  styles['inputRow'],
                  row.every((item: any) => item.hidden)
                    ? styles['hidden']
                    : null
                )}
              >
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export default CSYTFavouriteDetail

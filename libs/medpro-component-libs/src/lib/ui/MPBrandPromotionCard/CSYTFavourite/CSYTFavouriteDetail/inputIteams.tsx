import { Form, Input, Radio, DatePicker, Select, Switch } from 'antd'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import React, { useState } from 'react'
import Image from 'next/image'
import { size } from 'lodash'
import { getReplaceUTF8, MPButton } from '@medpro-libs/medpro-component-libs'
import IconUploadImg from '../../../../images/iconUploadImg.png'
import MPImageFormItemMultiple from '../../../../atoms/MPImageFormItemMultiple'
import styles from '../../styles.module.less'

const { RangePicker } = DatePicker
const { Option } = Select

export const inputItems = ({ form, hospitalList = [] }: any) => {
  return [
    {
      id: 'partnerId',
      type: 'select',
      label: 'Chọn cơ sở y tế',
      placeholder: 'Chọn cơ sở y tế',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ required: true, message: '<PERSON><PERSON> lòng chọn cơ sở y tế' }]}
          >
            <Select placeholder={placeholder}>
              {hospitalList.map((hospital: any) => (
                <Option key={hospital.partnerId} value={hospital.partnerId}>
                  {hospital.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'url',
      type: 'text',
      label: 'URL',
      placeholder: 'Nhập URL',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={['cta', 'url']}
            rules={[{ required: true, message: 'Vui lòng nhập URL' }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: true
    },
    {
      id: 'displayPeriod',
      type: 'rangePicker',
      label: 'Thời gian hiển thị',
      placeholder: 'Chọn thời gian',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              { required: true, message: 'Vui lòng chọn thời gian hiển thị' }
            ]}
          >
            <RangePicker
              format='DD/MM/YYYY'
              placeholder={['Từ ngày', 'Đến ngày']}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'targetWeb',
      type: 'select',
      label: 'Target Web',
      placeholder: 'Chọn target web',
      require: false,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <Select placeholder={placeholder} options={targetWebOptions} />
          </Form.Item>
        )
      }
    },
    {
      id: 'status',
      type: 'switch',
      label: 'Hiển thị',
      require: false,
      enter: ({ id, label }: any) => {
        return (
          <Form.Item label={label} name={id} valuePropName='checked'>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
              defaultChecked
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

export const targetWebOptions = [
  {
    value: '_self',
    label: 'One at tab - _self'
  },
  {
    value: '_blank',
    label: 'One new tab - _blank'
  }
]

import React, { useMemo, useRef, useState } from 'react'
import {
  Modal,
  Switch,
  type TableColumnsType,
  TablePaginationConfig,
  Tabs
} from 'antd'
import cx from 'classnames'
import { FiPlus } from 'react-icons/fi'
import dayjs from 'dayjs'
import {
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import { Image as ImageAtd } from 'antd'
import Image from 'next/image'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'
import { FaPen } from 'react-icons/fa'

export interface Props {
  headerRef: any
  heightContent: number
  hospitalList: any
  packageList: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

const HealthCare = ({
  headerRef,
  heightContent,
  hospitalList,
  packageList,
  loading,
  onPressViewDetail,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 70
    })
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [tabActiveKey, setTabActiveKey] = useState('1')

  // Create a map of hospital data using partnerId as key
  const hospitalMap = hospitalList?.length
    ? hospitalList.reduce((acc: any, hospital: any) => {
        if (hospital?.partnerId) {
          acc[hospital.partnerId] = hospital
        }
        return acc
      }, {})
    : {}

  // Map package data with hospital data
  const mappedPackageData = useMemo(() => {
    if (!packageList?.partner?.ctas) return []

    return Object.entries(packageList.partner.ctas).map(
      ([key, value]: [string, any]) => {
        const hospital = hospitalMap[value?.partnerId] || {}
        return {
          ...value,
          key,
          name: hospital.name || '',
          image: hospital.image || '',
          circleLogo: hospital.circleLogo || ''
          // Add other hospital properties as needed
        }
      }
    )
  }, [packageList, hospitalMap])

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onSubmitDelete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    setIsOpenEdit(!isOpenEdit)
    return onPressViewDetail(row._id)
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const dataSource = useMemo(() => {
    // Use mappedPackageData instead of hospitalList
    return mappedPackageData.map((item: any, index: number) => {
      return {
        ...item,
        id: item.key,
        // Add display information for the table
        displayPeriod: {
          startDate: '01/01/2023',
          endDate: '31/12/2023'
        },
        status: 'active',
        pin: true
        // You can add more properties as needed for the table display
      }
    })
  }, [mappedPackageData])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'Thông tin gói khám',
        width: 350,
        key: 'name',
        fixed: 'left',
        render: (row: any) => {
          return (
            <div className={styles['itemInfo']}>
              <DragHandle />
              <div className={styles['itemContent']}>
                <div className={styles['itemImage']}>
                  <ImageAtd
                    src={row.image}
                    alt={row.name}
                    width={50}
                    height={50}
                  />
                </div>
                <div className={styles['itemText']}>{row.name}</div>
              </div>
            </div>
          )
        }
      },
      // CSYT
      {
        title: 'CSYT',
        key: 'hospital',
        width: 200,
        render: (row: any) => (
          <div className={styles['groupHospital']}>{row.name}</div>
        )
      },
      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 200,
        render: (row: any) => (
          <div className={styles['groupPeriod']}>
            <div className={styles['start']}>
              {row?.displayPeriod?.startDate}
            </div>
            <div className={styles['line']} />
            <div className={styles['end']}>{row?.displayPeriod?.endDate}</div>
          </div>
        )
      },
      {
        title: 'Trạng thái',
        key: 'status',
        width: 150,
        align: 'center',
        render: (row: any) => (
          <div className={cx(styles['groupStatus'], styles[row.status])}>
            {row.status === 'active'
              ? 'Đang hiển thị'
              : row.status === 'inactive'
              ? 'Hết hạn'
              : 'Chờ hiển thị'}
          </div>
        )
      },
      {
        title: 'Bật / Tắt ghim',
        key: 'pin',
        align: 'center',
        width: 120,
        render: (row) => <Switch checked={row.pin} />
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items = [
    {
      key: '1',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: '2',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: '3',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: '4',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case '1':
      case '2':
      case '3':
      case '4':
        return (
          <MPTable
            type={'dragTable'}
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={false}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          defaultActiveKey={'1'}
          onChange={setTabActiveKey}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <div ref={filterRef} className={styles['filterWrapper']}>
        <div className={styles['left']}>
          <div className={styles['recordCount']}>
            {dataSource.length} Gói khám
          </div>
        </div>
        <div className={styles['right']}>
          <div className={styles['actionGroup']}>
            <MPButton onClick={() => onRefresh()} typeCustom={'cancel'}>
              <span>Cập nhật</span>
            </MPButton>
            <MPButton onClick={onPressNew} typeCustom={'primary'}>
              <FiPlus />
              <span>Chọn gói khám</span>
            </MPButton>
          </div>
        </div>
      </div>
      {renderTabPane()}
    </>
  )
}

export default HealthCare

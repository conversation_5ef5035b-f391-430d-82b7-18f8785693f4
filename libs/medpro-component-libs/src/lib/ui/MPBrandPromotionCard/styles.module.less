.brandPage {
  .brandHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: white;
    z-index: 999;
    padding: 10px 0;

    @media only screen and (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
    }

    .brandTitle {
      display: flex;
      align-items: center;

      :global {
        .ant-ribbon {
          margin-top: -30px;
        }
      }

      h2 {
        font-size: 20px;
      }
    }

    .brandActions {
      display: flex;
      align-items: center;
      flex-flow: wrap;
      gap: 10px;

      @media only screen and (max-width: 768px) {
        margin-top: 10px;
      }

      :global {
        .ant-select {
          min-width: 120px;
        }
      }
    }
  }

  .brandBody {
    :global {
      .ant-tabs {
        .ant-tabs-nav {
          margin: 0;
        }
      }
    }

    .tabsWrapper {
      :global {
        .ant-tabs
          .ant-tabs-nav
          .ant-tabs-nav-wrap
          .ant-tabs-nav-list
          .ant-tabs-tab-active {
          font-weight: 400;
        }
      }
    }

    .compTabWrapper {
      :global {
        .ant-tabs
          .ant-tabs-nav
          .ant-tabs-nav-wrap
          .ant-tabs-nav-list
          .ant-tabs-tab-active {
          font-weight: 400;
        }
        .ant-tabs .ant-tabs-tab-btn {
          padding: 0 5px;
        }
      }
    }
    .groupPosition {
      border: 1px dashed #b8b8b8;
      max-width: 50px;
      height: 40px;
      gap: 10px;
      border-radius: 6px;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }
    .itemInfo {
      display: flex;
      align-items: center;
      gap: 12px;

      .itemContent {
        display: flex;
        align-items: center;
        gap: 12px;

        .itemText {
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
    .groupStatus {
      max-width: 120px;
      height: 30px;
      gap: 10px;
      padding: 6px 10px;
      border-radius: 16px;
      border-width: 1px;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;

      &.active {
        border: 1px solid #00d22e;
        background: #00d22e29;
        color: #00d22e;
      }

      &.inactive {
        background: #eeeeeeee;
        color: #66666666;
      }

      &.pending {
        border: 1px solid #f1963a;
        background: #f1963a29;
        color: #f1963a;
      }
    }
    .groupPeriod {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 17px;
      .line {
        height: 1px;
        width: 11px;
        background-color: #333333;
      }
      .start,
      .end {
        color: #333333;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        p {
          margin-bottom: 5px;
        }
      }
    }
    .periodInfo {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-self: center;
      .periodInfoDate {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        div {
          text-align: center;

          &.dateLine {
            width: 10px;
            border-bottom: 1px solid;
          }
        }
      }
      .statusBadge {
        max-width: 120px;
        height: 30px;
        gap: 10px;
        padding: 6px 10px;
        border-radius: 16px;
        border-width: 1px;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
      }
    }

    .statusBadge {
      max-width: 120px;
      height: 30px;
      gap: 10px;
      padding: 6px 10px;
      border-radius: 16px;
      border-width: 1px;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;

      &.active {
        border: 1px solid #00d22e;
        background: #00d22e29;
        color: #00d22e;
      }

      &.inactive {
        background: #eeeeeeee;
        color: #66666666;
      }

      &.pending {
        border: 1px solid #f1963a;
        background: #f1963a29;
        color: #f1963a;
      }
    }

    .filterWrapper {
      -webkit-box-align: center;
      align-items: center;
      display: flex;
      flex-wrap: wrap;
      padding: 10px 0;

      .left {
        flex: 35%;
        display: flex;

        :global {
          .ant-input-search {
            max-width: 300px;

            .ant-input-outlined {
              border-color: #e7e7e7;
            }
          }
        }

        .recordCount {
          font-weight: 500;
          font-size: 20px;
          line-height: 28px;
          color: #111827;
        }
      }

      .right {
        .actionGroup {
          display: flex;
          gap: 10px;
          justify-content: flex-end;
        }
      }
    }
  }
}
// Modal New
.modalNewWrapper {
  .formGroupWrapper {
    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }
    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;
      position: sticky;
      bottom: 0;
      background: #ffffff;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}
// Modal Edit
.modalEditWrapper {
  .formGroupWrapper {
    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }
    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;
      position: sticky;
      bottom: 0;
      background: #ffffff;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}
// Delete Confirm
.deleteConfirm {
  .deleteText {
    font-weight: 500;
    font-size: 14px;
  }
  .spanName {
    font-weight: 500;
    font-size: 14px;
    color: #1b5a90;
  }
}

import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  Form,
  Modal,
  Space,
  Switch,
  Table,
  type TableColumnsType,
  TablePaginationConfig,
  Tabs
} from 'antd'
import cx from 'classnames'
import { FiPlus } from 'react-icons/fi'
import dayjs from 'dayjs'
import {
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import { Image } from 'antd'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'
import { FaPen } from 'react-icons/fa'
import { handleBase, handleBaseEdit } from './handleBase'
import { size } from 'lodash'

export interface Props {
  headerRef: any
  heightContent: number
  loading: boolean
  onPressViewDetail: (id: string) => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  baseValueDoctorTelemed: any
  methodsDoctorTelemed: any
}

const DoctorConsultationVideo = ({
  headerRef,
  heightContent,
  loading,
  onPressViewDetail,
  baseValueDoctorTelemed,
  methodsDoctorTelemed,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 70
    })
  const [tabActiveKey, setTabActiveKey] = useState('ALL')

  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const [action, setAction] = useState('')
  const [selectedDoctor, setSelectedDoctor] = useState<any>(null)

  const onPressEdit = (row: any) => {
    setSelectedDoctor(row)
    toggleModal('edit')
  }
  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.role} ${row.title} khỏi danh sách?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        methodsDoctorTelemed.onDelete(row._id)
      }
    })
  }

  const onPressNew = () => {
    toggleModal('create')
    methodsDoctorTelemed.getAllDoctorTelemed()
  }

  const dataSource = useMemo(() => {
    if (size(baseValueDoctorTelemed.doctorTelemedList) > 0) {
      return baseValueDoctorTelemed.doctorTelemedList.map(
        (item: any, index: number) => ({
          ...item,
          key: item.doctorId,
          displayPeriod: {
            fromDate: item.fromDate,
            toDate: item.toDate
          },
          tabStatus: item.tabStatus,
          status: item.status
        })
      )
    }
    return []
  }, [baseValueDoctorTelemed])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'STT',
        width: 70,
        key: 'position',
        align: 'center',
        fixed: 'left',
        onCell: () => ({ style: { padding: '12px 10px' } }),
        render: (text: any, record: any, index: number) => {
          return <div className={styles['groupPosition']}>{index + 1}</div>
        }
      },
      {
        title: 'Thông tin bác sĩ',
        width: 350,
        key: 'name',
        render: (row: any) => {
          return (
            <div className={styles['itemInfo']}>
              <DragHandle />
              <div className={styles['itemContent']}>
                <div className={styles['itemImage']}>
                  <Image
                    src={row.imageUrl}
                    alt={row.title}
                    width={50}
                    height={50}
                  />
                </div>
                <div className={styles['itemText']}>
                  {row.role} {row.title}
                </div>
              </div>
            </div>
          )
        }
      },

      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 200,
        render: (row: any) => (
          <div className={styles['groupPeriod']}>
            {row?.fromDate && (
              <div className={styles['start']}>
                <p>{dayjs(row?.fromDate).format('HH:mm, DD/MM/YYYY')}</p>
              </div>
            )}
            <div className={styles['line']} />
            {row?.toDate && (
              <div className={styles['end']}>
                <p>{dayjs(row?.toDate).format('HH:mm, DD/MM/YYYY')}</p>
              </div>
            )}
          </div>
        )
      },
      {
        title: 'Trạng thái',
        key: 'status',
        width: 150,
        align: 'center',
        render: (row: any) => (
          <div
            className={cx(styles['groupStatus'], styles['status'])}
            style={{
              color: row?.tabStatus?.style?.color,
              backgroundColor: row?.tabStatus?.style?.backgroundColor,
              border: row?.tabStatus?.style?.border
            }}
          >
            {row?.tabStatus?.text}
          </div>
        )
      },
      {
        title: 'Bật / Tắt ghim',
        key: 'status',
        align: 'center',
        width: 120,
        render: (row) => <Switch disabled checked={row.status} />
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items = [
    {
      key: 'ALL',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: 'INPROGRESS',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'PENDING',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'EXPIRED',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case 'ALL':
      case 'INPROGRESS':
      case 'PENDING':
      case 'EXPIRED':
        return (
          <MPTable
            type={'dragTable'}
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={false}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      default:
        return null
    }
  }

  const onChangeTabEvent = (key: string) => {
    setTabActiveKey(key)
    void methodsDoctorTelemed.getListDoctorTelemed({ tab: key })
  }

  const toggleModal = (value: string) => {
    setIsOpenModal(!isOpenModal)
    setAction(value)
  }

  const renderModal = (action: string) => {
    switch (action) {
      case 'create':
        return (
          <ModalAddDoctorTelemed
            isOpenModal={isOpenModal}
            contentModal={contentModal}
            baseValueDoctorTelemed={baseValueDoctorTelemed}
            onCancel={() => {
              setIsOpenModal(false)
              setAction('')
            }}
            onSubmit={(values: any) => {
              methodsDoctorTelemed.onAdd(values)
              toggleModal('create')
            }}
            loading={loading}
          />
        )
      case 'edit':
        return (
          <ModalEditDoctorTelemed
            isOpenModal={isOpenModal}
            contentModal={contentModal}
            baseValueDoctorTelemed={baseValueDoctorTelemed}
            onCancel={() => {
              setIsOpenModal(false)
              setAction('')
            }}
            onSubmit={(values: any) => {
              methodsDoctorTelemed.onUpdate(values)
              toggleModal('edit')
            }}
            loading={loading}
            initialValues={selectedDoctor}
          />
        )
      default:
        return null
    }
  }

  const contentModal = {
    create: {
      title: 'Thêm Bác sĩ Tư Vấn Video',
      centered: true,
      footer: false,
      className: styles['modalNewWrapper'],
      width: 800
    },
    edit: {
      title: 'Chỉnh sửa Thông tin Bác sĩ',
      centered: true,
      footer: false,
      className: styles['modalEditWrapper'],
      width: 800
    }
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          defaultActiveKey={'1'}
          onChange={onChangeTabEvent}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <div ref={filterRef} className={styles['filterWrapper']}>
        <div className={styles['left']}>
          <div className={styles['recordCount']}>
            {dataSource.length} Bác sĩ
          </div>
        </div>
        <div className={styles['right']}>
          <div className={styles['actionGroup']}>
            <MPButton
              onClick={methodsDoctorTelemed.getListDoctorTelemed}
              typeCustom={'repair'}
            >
              <span>Tải lại</span>
            </MPButton>
            <MPButton onClick={onPressNew} typeCustom={'primary'}>
              <FiPlus />
              <span>Chọn bác sĩ</span>
            </MPButton>
          </div>
        </div>
      </div>
      {renderTabPane()}
      {renderModal(action)}
    </>
  )
}

interface ModalAddDoctorTelemedProps {
  isOpenModal: boolean
  contentModal: any
  baseValueDoctorTelemed: any
  onCancel: () => void
  onSubmit: (values: any) => void
  loading: boolean
  initialValues?: any
}

const ModalAddDoctorTelemed = ({
  isOpenModal,
  contentModal,
  baseValueDoctorTelemed,
  onCancel,
  onSubmit,
  loading
}: ModalAddDoctorTelemedProps) => {
  const [form] = Form.useForm()

  const [timeType, setTimeType] = useState(true)

  useEffect(() => {
    if (form.getFieldValue('display')) {
      toggleTimeType(true)
    }
  }, [form])

  const toggleTimeType = (value: boolean) => {
    setTimeType(value)
  }
  const onFinish = (values: any) => {
    onSubmit(values)
  }

  return (
    <Modal {...contentModal.create} open={isOpenModal} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        {handleBase({
          form,
          allDoctorTelemedList: baseValueDoctorTelemed.allDoctorTelemedList,
          timeType,
          toggleTimeType
        })
          .reduce((rows: any, item: any, index: number, array: any) => {
            if (!item.group) {
              rows.push([item])
            } else if (index % 2 === 0) {
              rows.push(array.slice(index, index + 2))
            }
            return rows
          }, [])
          .map((row: any, rowIndex: number) => (
            <div
              key={rowIndex}
              className={cx(
                styles['inputRow'],
                row.every((item: any) => item.hidden) ? styles['hidden'] : null
              )}
            >
              {row.map((item: any, itemIndex: number) => (
                <div key={itemIndex} className={styles['inputItem']}>
                  {typeof item?.enter === 'function' ? item.enter(item) : null}
                </div>
              ))}
            </div>
          ))}
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

const ModalEditDoctorTelemed = ({
  isOpenModal,
  contentModal,
  baseValueDoctorTelemed,
  onCancel,
  onSubmit,
  loading,
  initialValues
}: ModalAddDoctorTelemedProps) => {
  const [form] = Form.useForm()
  const [timeType, setTimeType] = useState(false)

  useEffect(() => {
    if (form.getFieldValue('display')) {
      toggleTimeType(true)
    }
  }, [form])

  const toggleTimeType = (value: boolean) => {
    setTimeType(value)
  }

  const onFinish = (values: any) => {
    const body = { ...values }
    if (body.display) {
      delete body.displayPeriod
    }
    onSubmit(body)
  }

  const transformInitialValues = useMemo(() => {
    return {
      _id: initialValues?._id,
      doctorId: initialValues?.doctorId,
      display: Boolean(initialValues?.display),
      displayPeriod: [
        dayjs(initialValues?.fromDate),
        dayjs(initialValues?.toDate)
      ],
      status: initialValues.status
    }
  }, [initialValues])

  return (
    <Modal {...contentModal.edit} open={isOpenModal} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
        initialValues={transformInitialValues}
      >
        <Space direction='vertical' size={16}>
          {handleBaseEdit({
            form,
            allDoctorTelemedList: baseValueDoctorTelemed.allDoctorTelemedList,
            timeType,
            toggleTimeType
          })
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div
                key={rowIndex}
                className={cx(
                  styles['inputRow'],
                  row.every((item: any) => item.hidden)
                    ? styles['hidden']
                    : null
                )}
              >
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </Space>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export default DoctorConsultationVideo

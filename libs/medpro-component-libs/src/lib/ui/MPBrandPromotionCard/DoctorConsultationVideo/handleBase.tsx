import { DatePicker, Form, Radio, Select, Switch } from 'antd'
import MPImageFormItemMultiple from '../../../atoms/MPImageFormItemMultiple'

import IconUploadImg from '../../../images/iconUploadImg.png'
import styles from '../styles.module.less'
import MPButton from '../../../atoms/MPButton'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import { FormInstance } from 'antd/lib'

const { RangePicker } = DatePicker

const required = (require: boolean, message: any) => {
  return [
    {
      required: require,
      message: `Vui lòng ${message}`
    }
  ]
}

interface BaseProps {
  form: FormInstance<any>
  allDoctorTelemedList: any
  timeType: boolean
  toggleTimeType: (value: boolean) => void
}

export const handleBase = ({
  form,
  allDoctorTelemedList,
  timeType,
  toggleTimeType
}: BaseProps) => {
  return [
    {
      id: 'doctorId',
      type: 'select',
      label: 'Chọ<PERSON> bác sĩ',
      placeholder: 'Chọn bác sĩ',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            name={id}
            label={label}
            valuePropName={'url'}
            extra=''
            rules={required(require, 'Vui lòng chọn bác sĩ!')}
          >
            <Select
              placeholder={placeholder}
              options={[
                ...(allDoctorTelemedList?.map((item: any) => ({
                  label: item.role + ' ' + item.title,
                  value: item.id || item?.doctorDescription?.doctorId
                })) || [])
              ]}
            />
          </Form.Item>
        )
      }
    },
    {
      id: 'display',
      type: 'text',
      label: 'Thời gian hiển thị',
      placeholder: 'Chọn thời gian hiển thị',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng chọn thời gian hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={(e) => toggleTimeType(e.target.value)}
              options={[
                {
                  value: true,
                  label: 'Luôn'
                },
                {
                  value: false,
                  label: 'Theo giờ'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'timePicker',
      type: 'text',
      label: '',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={
              !timeType
                ? [
                    {
                      required: require,
                      message: 'Vui lòng chọn thời gian hiển thị!'
                    }
                  ]
                : []
            }
          >
            <RangePicker
              showTime
              format='DD/MM/YYYY HH:mm'
              placeholder={['Thời gian bắt đầu', 'Thời gian kết thúc']}
            />
          </Form.Item>
        )
      },
      hidden: timeType,
      group: false
    }
  ]
}

export const handleBaseEdit = ({
  form,
  allDoctorTelemedList,
  timeType,
  toggleTimeType
}: {
  form: FormInstance<any>
  allDoctorTelemedList: any
  timeType: boolean
  toggleTimeType: (value: boolean) => void
}) => {
  return [
    {
      id: '_id',
      type: 'text',
      label: '',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return <Form.Item hidden label={label} name={id} />
      },
      hidden: true
    },
    {
      id: 'doctorId',
      type: 'text',
      label: '',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return <Form.Item hidden label={label} name={id} />
      },
      hidden: true
    },
    {
      id: 'status',
      type: 'text',
      label: 'Trạng thái',
      placeholder: 'Chọn trạng thái',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={required(require, 'Vui lòng chọn trạng thái!')}
          >
            <Switch />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'display',
      type: 'text',
      label: 'Thời gian hiển thị',
      placeholder: 'Chọn thời gian hiển thị',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng chọn thời gian hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={(e) => toggleTimeType(e.target.value)}
              options={[
                {
                  value: true,
                  label: 'Luôn'
                },
                {
                  value: false,
                  label: 'Theo giờ'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'displayPeriod',
      type: 'text',
      label: '',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={
              !timeType
                ? [
                    {
                      required: require,
                      message: 'Vui lòng chọn thời gian hiển thị!'
                    }
                  ]
                : []
            }
          >
            <RangePicker
              showTime
              format='DD/MM/YYYY HH:mm'
              placeholder={['Thời gian bắt đầu', 'Thời gian kết thúc']}
            />
          </Form.Item>
        )
      },
      hidden: timeType,
      group: false
    }
  ]
}

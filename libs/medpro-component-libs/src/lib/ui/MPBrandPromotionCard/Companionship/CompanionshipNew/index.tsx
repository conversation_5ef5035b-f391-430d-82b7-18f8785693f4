import React, { useEffect } from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import { get, isArray } from 'lodash'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputIteams'
import styles from '../../styles.module.less'

export interface Props {
  loading?: boolean
  contentModal?: any
  isOpenNew: boolean
  setIsOpenNew: any
  hospitalList: any
  actionCompanionship: any
}

const CompanionshipNew = ({
  loading,
  contentModal,
  isOpenNew,
  setIsOpenNew,
  actionCompanionship,
  hospitalList
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    form.setFieldsValue({
      status: true
    })
  }, [])

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onCancel = () => {
    setIsOpenNew(!isOpenNew)
    form.resetFields()
  }

  const onFinish = (values: any) => {
    console.log('Form submitted with values:', values)
    const payload = {
      id: values.partnerId,
      fromDate: values.displayPeriod[0]?.toISOString(),
      toDate: values.displayPeriod[1]?.toISOString(),
      status: values.status,
      cta: {
        type: 'SCREEN',
        target: 'HOSPITAL_DETAIL',
        partnerId: values.partnerId,
        url: values.url,
        targetWeb: values.targetWeb
      }
    }

    console.log('Sending payload:', payload)
    return actionCompanionship.create(payload, onCancel)
  }

  return (
    <Modal {...contentModal} open={isOpenNew} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems({
            form,
            hospitalList,
            onChangeImage
          })
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div
                key={rowIndex}
                className={cx(
                  styles['inputRow'],
                  row.every((item: any) => item.hidden)
                    ? styles['hidden']
                    : null
                )}
              >
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export default CompanionshipNew

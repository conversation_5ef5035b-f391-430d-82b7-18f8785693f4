import React, { useMemo, useRef, useState } from 'react'
import {
  Input,
  Modal,
  type TableColumnsType,
  TablePaginationConfig,
  Tooltip
} from 'antd'
import cx from 'classnames'
import { AiOutlinePlusCircle, AiOutlineRedo } from 'react-icons/ai'
import dayjs from 'dayjs'
import {
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import MPTable from '../../atoms/MPTable'
import UserManagementNew from './UserManagementNew'
import UserManagementDetail from './UserManagementDetail'
import UserManagementView from './UserManagementView'
import styles from './styles.module.less'

const contentModal = {
  edit: {
    title: 'Sửa thông tin người dùng',
    centered: true,
    footer: false,
    className: styles['modalEditWrapper']
  },
  create: {
    title: 'Thêm mới người dùng',
    centered: true,
    footer: false,
    className: styles['modalNewWrapper']
  },
  view: {
    title: 'Thông tin người dùng',
    centered: true,
    footer: false,
    className: styles['modalViewWrapper']
  }
}

export interface Props {
  heightContent: number
  userList: any
  userDetail: any
  hospitalList?: any
  roleList?: any
  loading?: boolean
  onPressDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
}

const MPUserManagementCard = ({
  heightContent,
  userList,
  userDetail,
  hospitalList,
  roleList,
  loading,
  onPressDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent
    })
  const { Search } = Input
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [isOpenView, setIsOpenView] = useState(false)

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onSubmitDelete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    setIsOpenEdit(!isOpenEdit)
    return onPressDetail(row._id)
  }

  const onPressView = (row: any) => {
    setIsOpenView(!isOpenView)
    return onPressDetail(row._id)
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const dataSource = useMemo(() => {
    if (userList?.length) {
      return userList.map((item: any, index: number) => ({
        ...item,
        key: item.id || index,
        active: item.active ? 'on' : 'off',
        createdAt: dayjs(item.createdAt).format('DD-MM-YYYY, HH:mm'),
        updatedAt: dayjs(item.updatedAt).format('DD-MM-YYYY, HH:mm')
      }))
    }
    return []
  }, [userList])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'Tên người dùng',
        width: 250,
        dataIndex: 'name',
        key: 'name',
        fixed: 'left'
      },
      {
        title: 'Trạng thái',
        dataIndex: 'active',
        key: 'active',
        align: 'center',
        width: 150,
        render: (row) => (
          <div className={cx(styles['groupActive'], styles[row])}>
            {row === 'on' ? 'Đang hiển thị' : 'Ngừng hiển thị'}
          </div>
        )
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
        width: 200
      },
      {
        title: 'Ngày tạo',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 180
      },
      {
        title: 'Ngày cập nhật',
        dataIndex: 'updatedAt',
        key: 'updatedAt',
        width: 180
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete', 'view']
      }
    ],
    onPressEdit,
    onPressDelete,
    onPressView
  )

  return (
    <>
      <div className={styles['pageWrapper']}>
        <div ref={headerRef} className={styles['header']}>
          <div className={styles['title']}>
            <h2>Quản lý người dùng</h2>
          </div>
        </div>
        <div className={styles['body']}>
          <div ref={filterRef} className={styles['filterWrapper']}>
            <div className={styles['left']}>
              <Search
                size={'middle'}
                placeholder='Tìm kiếm người dùng..'
                enterButton
                allowClear
                onSearch={(value: any) => onSubmitSearch(value)}
              />
            </div>
            <div className={styles['right']}>
              <div className={styles['enhance']}>
                <Tooltip title='Thêm mới' placement='bottom' color={'#44474e'}>
                  <div
                    onClick={onPressNew}
                    className={cx(styles['btn'], styles['btnAdd'])}
                  >
                    <AiOutlinePlusCircle />
                  </div>
                </Tooltip>
                <Tooltip title='Làm mới' placement='bottom' color={'#44474e'}>
                  <div
                    onClick={() => onRefresh()}
                    className={cx(styles['btn'], styles['btnRefresh'])}
                  >
                    <AiOutlineRedo />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>
          <MPTable
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={heightTbBody}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        </div>
      </div>
      {isOpenEdit && (
        <UserManagementDetail
          loading={loading}
          contentModal={contentModal.edit}
          isOpenEdit={isOpenEdit}
          setIsOpenEdit={setIsOpenEdit}
          data={userDetail}
          roleList={roleList}
          onSubmitUpdate={onSubmitUpdate}
        />
      )}
      {isOpenNew && (
        <UserManagementNew
          loading={loading}
          contentModal={contentModal.create}
          hospitalList={hospitalList}
          isOpenNew={isOpenNew}
          setIsOpenNew={setIsOpenNew}
          onSubmitCreate={onSubmitCreate}
        />
      )}
      {isOpenView && (
        <UserManagementView
          contentModal={contentModal.view}
          data={userDetail}
          isOpenView={isOpenView}
          setIsOpenView={setIsOpenView}
        />
      )}
    </>
  )
}

export default MPUserManagementCard

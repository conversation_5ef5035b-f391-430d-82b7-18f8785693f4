import React from 'react'
import { Modal, Tag } from 'antd'
import dayjs from 'dayjs'
import styles from '../styles.module.less'

export interface Props {
  data: any
  isOpenView: boolean
  setIsOpenView: any
  contentModal?: any
}

const UserManagementView = ({
  data,
  setIsOpenView,
  isOpenView,
  contentModal
}: Props) => {
  const onCancel = () => {
    setIsOpenView(!isOpenView)
  }
  return (
    <Modal {...contentModal} open={isOpenView} onCancel={onCancel}>
      <div className={styles['contentWrapper']}>
        <div className={styles['item']}>
          <div className={styles['label']}>Tên người dùng:</div>
          <div className={styles['value']}>{data?.name}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Email:</div>
          <div className={styles['value']}>{data?.email}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Partner:</div>
          <div className={styles['value']}>{data?.partnerId}</div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Trạng thái:</div>
          <div className={styles['value']}>
            <Tag className={data?.active ? styles['on'] : styles['off']}>
              {data?.active ? 'Bật' : 'Tắt'}
            </Tag>
          </div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Ngày tạo:</div>
          <div className={styles['value']}>
            {dayjs(data?.createdAt).format('DD-MM-YYYY, HH:mm')}
          </div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Ngày cập nhật:</div>
          <div className={styles['value']}>
            {dayjs(data?.updatedAt).format('DD-MM-YYYY, HH:mm')}
          </div>
        </div>
        <div className={styles['item']}>
          <div className={styles['label']}>Vai trò:</div>
          <div className={styles['value']}>
            {data?.roles.map((role: any) => (
              <Tag color={'#108ee9'} key={role._id}>
                {role.name}
              </Tag>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  )
}
export default UserManagementView

import { Form, Input, Switch, Select } from 'antd'
import React from 'react'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import { Valid } from '../../../helpers/valid'

const valid = new Valid()

export const inputItems = (form: any, roleList: any) => {
  return [
    {
      id: 'name',
      type: 'text',
      label: 'Tên người dùng',
      placeholder: 'Vui lòng nhập tên người dùng (có dấu) ...',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'email',
      type: 'text',
      label: 'Email',
      placeholder: 'Vui lòng nhập địa chỉ email',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.email, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'roles',
      type: 'text',
      label: 'Danh sách phân quyền',
      placeholder: 'Chọn quyền',
      require: false,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id} rules={[{ required: require }]}>
            <Select
              mode={'tags'}
              placeholder={placeholder}
              options={roleList}
              optionFilterProp='children'
              filterOption={(input: string, option: any) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'active',
      label: 'Trạng thái',
      require: false,
      enter: ({ id, require, label }: any) => {
        return (
          <Form.Item label={label} name={id} valuePropName='checked'>
            <Switch
              checkedChildren={<CheckOutlined />}
              unCheckedChildren={<CloseOutlined />}
            />
          </Form.Item>
        )
      },
      group: false
    }
  ]
}

.pageWrapper {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: white;
    z-index: 999;
    padding: 10px 0;

    @media only screen and (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
    }

    .title {
      display: flex;
      align-items: center;

      :global {
        .ant-ribbon {
          margin-top: -30px;
        }
      }

      h2 {
        font-size: 20px;
      }
    }

    .actionWrapper {
      display: flex;
      align-items: center;
      flex-flow: wrap;
      gap: 10px;

      @media only screen and (max-width: 768px) {
        margin-top: 10px;
      }

      :global {
        .ant-select {
          min-width: 120px;
        }
      }
    }
  }

  .body {
    :global {
      .ant-tabs {
        .ant-tabs-nav {
          margin: 0;
        }
      }
    }

    .bigTabWrapper {
      :global {
        .ant-tabs .ant-tabs-nav .ant-tabs-nav-wrap .ant-tabs-nav-list .ant-tabs-tab-active {
          font-weight: 400;
        }
      }
    }

    .compTabWrapper {
      :global {
        .ant-tabs .ant-tabs-nav .ant-tabs-nav-wrap .ant-tabs-nav-list .ant-tabs-tab-active {
          font-weight: 400;
        }

        .ant-tabs .ant-tabs-tab-btn {
          padding: 0 5px;
        }
      }
    }

    .groupPosition {
      border: 1px dashed #B8B8B8;
      max-width: 50px;
      height: 40px;
      gap: 10px;
      border-radius: 6px;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }

    .groupName {
      display: flex;
      align-items: center;
      gap: 12px;
      max-height: 70px;
      overflow: hidden;

      .contentName {
        display: flex;
        align-items: center;
        gap: 12px;

        .text {
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
        }
      }
    }

    .groupPeriod {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      gap: 12px;

      .period {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;

        .start, .end {
          width: 85px;
        }

        .line {
          width: 10px;
          display: flex;
          justify-content: center;
        }
      }

      .tabStatus {
        width: 120px;
        max-width: 120px;
        height: 30px;
        gap: 10px;
        padding: 6px 10px;
        border-radius: 16px;
        border-width: 1px;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
      }
    }

    .groupLinkTarget {
      position: relative;
      border: 1px solid #E6E6E6;
      padding: 8px 25px 8px 8px;
      max-width: 300px;
      display: flex;
      gap: 5px;
      align-content: center;
      cursor: pointer;

      .link {
        color: #1677ff;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        text-decoration: underline;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }

      .editIcon {
        position: absolute;
        right: 5px;
        margin-left: 8px;
        margin-top: 3px;
        cursor: pointer;
        color: #111827;

        &:hover {
          color: #1d70b8;
        }

        svg {
          display: flex;
        }
      }
    }

    .filterWrapper {
      margin-bottom: 16px;

      .filterAction {
        -webkit-box-align: center;
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        padding: 10px 0;

        .left {
          flex: 35%;
          display: flex;

          :global {
            .ant-input-search {
              max-width: 300px;

              .ant-input-outlined {
                border-color: #e7e7e7;
              }
            }
          }

          .countRecord {
            font-weight: 500;
            font-size: 20px;
            line-height: 28px;
            color: #111827;
          }
        }

        .right {
          flex: 10%;
          display: flex;
          justify-content: flex-end;

          .enhance {
            display: flex;
            gap: 5px;
          }
        }
      }

      .filterTab {
        display: flex;
        align-items: center;
        gap: 8px;

        .inActive {
          background: #FFFFFF !important;
          border: 1px solid #EBEBEB;
        }
      }
    }
  }
}

.modalEditWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        &.hidden {
          display: none;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }

    .uploadWrapper {
      :global {
        .ant-upload-list-picture-card {
          width: 100%;
          flex-direction: column;

          .ant-upload-select, .ant-upload-list-item-container {
            width: 100%;
            height: 200px;
            border: 1px dashed #F07C63;
            border-color: #F07C63 !important;
            padding: 20px;
          }

          .ant-upload-list-item-container {
            padding: 0;

            .ant-upload-list-item-done {
              border: none;
            }
          }
        }
      }

      .uploadBox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 16px;

        .text {
          color: #27272A;
          font-weight: 700;
          font-size: 14px;
          line-height: 20px;
          vertical-align: middle;

          .extension {
            color: #808089;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            vertical-align: middle;
          }
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;
      position: sticky;
      bottom: 0;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

.modalNewWrapper {
  .formGroupWrapper {
    height: 100%;

    &.two {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .inputRow {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        @media only screen and (max-width: 768px) {
          flex-direction: column;
        }

        &:last-child {
          margin-bottom: 0;
        }

        &.hidden {
          display: none;
        }

        .inputItem {
          width: 100%;

          :global {
            .ant-form-item {
              margin-bottom: 10px;
            }
          }

          .moreVideo {
            position: relative;

            .btnRemoveInput {
              position: absolute;
              right: 0;
              height: 100%;
            }
          }
        }
      }
    }

    .tooltipInputItem {
      display: flex;
      gap: 5px;
      align-items: center;

      svg {
        cursor: pointer;

        path {
          fill: #3498db;
        }
      }
    }

    .uploadWrapper {
      :global {
        .ant-upload-list-picture-card {
          width: 100%;
          flex-direction: column;

          .ant-upload-select, .ant-upload-list-item-container {
            width: 100%;
            height: 200px;
            border: 1px dashed #F07C63;
            border-color: #F07C63 !important;
            padding: 20px;
          }

          .ant-upload-list-item-container {
            padding: 0;

            .ant-upload-list-item-done {
              border: none;
            }
          }
        }
      }

      .uploadBox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 16px;

        .text {
          color: #27272A;
          font-weight: 700;
          font-size: 14px;
          line-height: 20px;
          vertical-align: middle;

          .extension {
            color: #808089;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            vertical-align: middle;
          }
        }
      }
    }

    .groupAction {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 10px;
      position: sticky;
      bottom: 0;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
}

.modalViewWrapper {
  :global {
    .ant-modal-content {
      height: 100vh;
      display: flex;
      flex-direction: column;

      .ant-modal-body {
        height: 100%;
        overflow: auto;
      }
    }
  }

  .contentWrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .item {
      display: flex;
      align-items: center;

      @media only screen and (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
      }

      .label {
        min-width: 150px;
        font-weight: 500;
      }

      .value {
        width: 100%;

        .on {
          background: green;
          color: #ffffff;
          text-transform: capitalize;
        }

        .off {
          background: red;
          color: #ffffff;
          text-transform: capitalize;
        }
      }
    }
  }

  .transferWrapper {
    margin-top: 20px;
    margin-bottom: 20px;

    :global {
      .ant-transfer-list {
        width: 100%;
        height: 700px;

        .ant-transfer-list-body-customize-wrapper {
          overflow: auto;
          height: 100%;
        }
      }
    }
  }

  .groupAction {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 10px;
    position: sticky;
    bottom: 0;
    background: #ffffff;

    :global {
      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
}

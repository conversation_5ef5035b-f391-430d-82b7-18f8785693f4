import React, { useEffect } from 'react'
import { Form, Modal } from 'antd'
import cx from 'classnames'
import { get, isArray } from 'lodash'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import { inputItems } from './inputItems'
import styles from '../../styles.module.less'

export interface Props {
  loading?: boolean
  contentModal?: any
  isOpenNew: boolean
  setIsOpenNew: any
  hospitalList: any
  selectedPartner: any
  featurePartnerList: any
  featureList: any
  filterTabActive?: any
  uploadProps?: any
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  formSelectPartner: (value: any) => void
  doctorReferenceList: any
  serviceReferenceList: any
  subjectReferenceList: any
  servicePackageList: any
}

const HomeBannerServiceNew = ({
  loading,
  contentModal,
  isOpenNew,
  setIsOpenNew,
  onSubmitCreate,
  hospitalList,
  selectedPartner,
  featurePartnerList,
  featureList,
  filterTabActive,
  uploadProps,
  formSelectPartner,
  doctorReferenceList,
  serviceReferenceList,
  subjectReferenceList,
  servicePackageList
}: Props) => {
  const [form] = Form.useForm()

  useEffect(() => {
    form.setFieldsValue({ display: true, status: true })
  }, [])

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onCancel = () => {
    setIsOpenNew(!isOpenNew)
  }

  const onFinish = (values: any) => {
    return onSubmitCreate({ ...values }, onCancel)
  }

  return (
    <Modal {...contentModal} open={isOpenNew} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={styles['inputItems']}>
          {inputItems({
            form,
            uploadProps,
            onChangeImage,
            filterTabActive,
            hospitalList,
            selectedPartner,
            featurePartnerList: featurePartnerList.filter(
              (item: any) => item.disabled
            ),
            featureList,
            doctorReferenceList,
            serviceReferenceList,
            subjectReferenceList,
            servicePackageList,
            formSelectPartner
          })
            .reduce((rows: any, item: any, index: number, array: any) => {
              if (!item.group) {
                rows.push([item])
              } else if (index % 2 === 0) {
                rows.push(array.slice(index, index + 2))
              }
              return rows
            }, [])
            .map((row: any, rowIndex: number) => (
              <div
                key={rowIndex}
                className={cx(
                  styles['inputRow'],
                  row.every((item: any) => item.hidden)
                    ? styles['hidden']
                    : null
                )}
              >
                {row.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className={styles['inputItem']}>
                    {typeof item?.enter === 'function'
                      ? item.enter(item)
                      : null}
                  </div>
                ))}
              </div>
            ))}
        </div>
        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export default HomeBannerServiceNew

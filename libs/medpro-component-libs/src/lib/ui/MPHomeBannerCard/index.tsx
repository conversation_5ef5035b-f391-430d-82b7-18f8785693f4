import React, { useRef } from 'react'
import { TablePaginationConfig, Tabs } from 'antd'
import ActionResetCache from '../common/ActionResetCache'
import HomeBannerService from './HomeBannerService'
import HomeBannerSingle from './HomeBannerSingle'
import HomeBannerMultiple from './HomeBannerMultiple'
import styles from './styles.module.less'

export interface Props {
  heightContent: number
  homeBannerConfig: any
  uploadProps: any
  hospitalList: any
  selectedPartner: any
  setSelectedPartner: any
  featurePartnerList: any
  featureList: any
  doctorReferenceList: any
  serviceReferenceList: any
  subjectReferenceList: any
  servicePackageList: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onHandleChangeTab: (key: string, level: any) => void
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  formSelectPartner: (value: any) => void
  setTabActiveKey?: any
  tabActiveKey?: any
  setSelectedFilterTab?: any
  selectedFilterTab?: any
  onDragSortTable?: (data: any) => void
  onResetCache?: () => void
}

const MPHomeBannerCard = ({
  heightContent,
  homeBannerConfig,
  uploadProps,
  hospitalList,
  selectedPartner,
  setSelectedPartner,
  featurePartnerList,
  featureList,
  doctorReferenceList,
  serviceReferenceList,
  subjectReferenceList,
  servicePackageList,
  loading,
  onPressViewDetail,
  onHandleChangeTab,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  formSelectPartner,
  setTabActiveKey,
  tabActiveKey,
  setSelectedFilterTab,
  selectedFilterTab,
  onDragSortTable,
  onResetCache
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)

  const items = [
    {
      key: 'banners_home',
      label: 'Banner trang chủ đơn',
      forceRender: true,
      children: null
    },
    {
      key: 'banners_multi',
      label: 'Banner trang chủ nhóm',
      forceRender: true,
      children: null
    },
    {
      key: 'banner_services',
      label: 'Banner trang dịch vụ',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    const sharedProps = {
      headerRef,
      heightContent,
      homeBannerConfig,
      uploadProps,
      selectedPartner,
      hospitalList,
      featurePartnerList,
      featureList,
      loading,
      onPressViewDetail,
      onSubmitCreate,
      onSubmitUpdate,
      onSubmitDelete,
      onRefresh,
      pagination,
      onChangePageEvent,
      onChangeSizeEvent,
      formSelectPartner,
      doctorReferenceList,
      serviceReferenceList,
      subjectReferenceList,
      servicePackageList,
      onHandleChangeTab,
      onDragSortTable,
      setSelectedFilterTab,
      selectedFilterTab
    }

    const tabComponents: Record<string, React.ComponentType<any>> = {
      banners_home: HomeBannerSingle,
      banners_multi: HomeBannerMultiple,
      banner_services: HomeBannerService
    }

    const Component = tabComponents[tabActiveKey]
    return Component ? <Component {...sharedProps} /> : null
  }

  return (
    <div className={styles['pageWrapper']}>
      <div ref={headerRef} className={styles['header']}>
        <div className={styles['title']}>
          <h2>Banner trang chủ</h2>
        </div>
        <ActionResetCache
          selectedPartner={selectedPartner}
          setSelectedPartner={setSelectedPartner}
          onResetCache={() => onResetCache}
        />
      </div>
      <div className={styles['body']}>
        <div className={styles['bigTabWrapper']}>
          <Tabs
            activeKey={tabActiveKey}
            onChange={(key) => {
              setTabActiveKey(key)
              onHandleChangeTab(key, 'generalTab')
            }}
            tabPosition={'top'}
            items={items}
            type={'card'}
          />
        </div>
        {renderTabPane()}
      </div>
    </div>
  )
}

export default MPHomeBannerCard

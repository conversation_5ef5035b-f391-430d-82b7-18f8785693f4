import React, { useMemo, useRef, useState } from 'react'
import {
  Modal,
  Switch,
  type TableColumnsType,
  TablePaginationConfig,
  Tabs,
  Image as ImageAntd
} from 'antd'
import cx from 'classnames'
import { FiPlus } from 'react-icons/fi'
import dayjs from 'dayjs'
import {
  checkJson,
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import HomeBannerSingleNew from './HomeBannerSingleNew'
import HomeBannerSingleDetail from './HomeBannerSingleDetail'
import MPJSONEditor from '../../../atoms/MPJsonEditor'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'

const contentModal = {
  edit: {
    title: 'Sửa banner',
    centered: true,
    footer: false,
    className: styles['modalEditWrapper'],
    width: 800
  },
  create: {
    title: 'Thêm banner',
    centered: true,
    footer: false,
    className: styles['modalNewWrapper'],
    width: 800
  },
  viewCTA: {
    title: 'Chi tiết nội dung',
    centered: true,
    footer: false
  }
}

export interface Props {
  headerRef: any
  heightContent: number
  homeBannerConfig: any
  uploadProps: any
  hospitalList: any
  selectedPartner: any
  featurePartnerList: any
  featureList: any
  doctorReferenceList: any
  serviceReferenceList: any
  subjectReferenceList: any
  servicePackageList: any
  loading?: boolean
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  formSelectPartner: (value: any) => void
  onHandleChangeTab: (key: string, level: any) => void
  onDragSortTable?: (data: any) => void
  setSelectedFilterTab?: any
  selectedFilterTab?: any
}

const HomeBannerSingle = ({
  headerRef,
  heightContent,
  homeBannerConfig,
  uploadProps,
  hospitalList,
  selectedPartner,
  featurePartnerList,
  featureList,
  doctorReferenceList,
  serviceReferenceList,
  subjectReferenceList,
  servicePackageList,
  loading,
  onPressViewDetail,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  formSelectPartner,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  onHandleChangeTab,
  onDragSortTable,
  setSelectedFilterTab,
  selectedFilterTab
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 75
    })
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [isOpenViewCTA, setIsOpenViewCTA] = useState(false)
  const [dataCTA, setDataCTA] = useState(null)
  const [filterTabActive, setFilterTabActive] = useState('website')
  const [dataRow, setDataRow] = useState(undefined)

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ${row.name}?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return onSubmitDelete(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    setIsOpenEdit(!isOpenEdit)
    formSelectPartner({
      partnerId: row?.cta?.partnerId,
      action: row?.cta?.action
    })
    setDataRow(row)
  }

  const onPressNew = () => {
    setIsOpenNew(!isOpenNew)
  }

  const onPressViewCTA = (row: any) => {
    setIsOpenViewCTA(!isOpenViewCTA)
    setDataCTA(row?.cta)
  }

  const onPressFilterTab = (key: string) => {
    setFilterTabActive(key)
  }

  const dataSource = useMemo(() => {
    if (homeBannerConfig && homeBannerConfig.length) {
      return homeBannerConfig?.map((item: any, index: number) => {
        return {
          ...item,
          key: item.id || index,
          index,
          position: Number(index + 1),
          sysCreatedAt: item.createdAt,
          sysUpdatedAt: item.updatedAt,
          fmtFromTime: dayjs(item.fromDate).format('HH:mm'),
          fmtFromDate: dayjs(item.fromDate).format('DD-MM-YYYY'),
          fmtToTime: dayjs(item.toDate).format('HH:mm'),
          fmtToDate: dayjs(item.toDate).format('DD-MM-YYYY')
        }
      })
    }
    return []
  }, [homeBannerConfig])

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'STT',
        width: 90,
        key: 'position',
        align: 'center',
        fixed: 'left',
        render: (row: any) => (
          <div className={styles['groupPosition']}>{row.position}</div>
        )
      },
      {
        title: 'Hình banner',
        width: 450,
        key: 'name',
        fixed: 'left',
        render: (row: any) => (
          <div className={styles['groupName']}>
            {selectedFilterTab === 'ALL' ? <DragHandle /> : null}
            <ImageAntd width={400} src={row?.imageUrl} alt='logo' />
          </div>
        )
      },
      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 200,
        render: (row: any) => (
          <>
            {row?.display ? (
              <div className={styles['groupPeriod']}>
                <div
                  className={styles['tabStatus']}
                  style={{ ...row?.tabStatus?.style }}
                >
                  {row?.tabStatus?.text}
                </div>
              </div>
            ) : (
              <div className={styles['groupPeriod']}>
                <div className={styles['period']}>
                  <div className={styles['start']}>
                    {row?.fmtFromTime} <br /> {row?.fmtFromDate}
                  </div>
                  <div className={styles['line']}>-</div>
                  <div className={styles['end']}>
                    {row?.fmtToTime} <br /> {row?.fmtToDate}
                  </div>
                </div>
                <div
                  className={styles['tabStatus']}
                  style={{ ...row?.tabStatus?.style }}
                >
                  {row?.tabStatus?.text}
                </div>
              </div>
            )}
          </>
        )
      },
      {
        title: 'Trạng thái',
        key: 'status',
        align: 'center',
        width: 120,
        render: (row) => <Switch checked={row.status} />
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 120,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items = [
    {
      key: 'ALL',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: 'INPROGRESS',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'PENDING',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'EXPIRED',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (selectedFilterTab) {
      case 'ALL':
        return (
          <MPTable
            type={'dragTable'}
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={dynHeightContent - 55}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            onDragSortTable={onDragSortTable}
          />
        )
      case 'INPROGRESS':
      case 'PENDING':
      case 'EXPIRED':
        return (
          <MPTable
            columns={columns}
            dataSource={dataSource}
            dynHeightContent={dynHeightContent}
            heightTbBody={dynHeightContent - 55}
            loading={loading}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )
      default:
        return null
    }
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          activeKey={selectedFilterTab}
          onChange={(key) => {
            setSelectedFilterTab(key)
            onHandleChangeTab(key, 'filterTab')
          }}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <div ref={filterRef} className={styles['filterWrapper']}>
        <div className={styles['filterAction']}>
          <div className={styles['left']}>
            <div className={styles['countRecord']}>
              {homeBannerConfig?.length ?? 0} Banner
            </div>
          </div>
          <div className={styles['right']}>
            <div className={styles['enhance']}>
              <MPButton onClick={onPressNew} typeCustom={'primary'}>
                <FiPlus />
                <span>Thêm banner</span>
              </MPButton>
            </div>
          </div>
        </div>
        <div className={styles['filterTab']}>
          <MPButton
            onClick={() => {
              onPressFilterTab('website')
              onHandleChangeTab('desktop', 'platformTab')
            }}
            className={cx(
              filterTabActive === 'app' ? styles['inActive'] : null
            )}
            typeCustom={'cancel'}
          >
            <span>Website</span>
          </MPButton>
          <MPButton
            onClick={() => {
              onPressFilterTab('app')
              onHandleChangeTab('mobile', 'platformTab')
            }}
            className={cx(
              filterTabActive === 'website' ? styles['inActive'] : null
            )}
            typeCustom={'cancel'}
          >
            <span>App</span>
          </MPButton>
        </div>
      </div>
      {renderTabPane()}
      {isOpenEdit && (
        <HomeBannerSingleDetail
          loading={loading}
          contentModal={contentModal.edit}
          isOpenEdit={isOpenEdit}
          setIsOpenEdit={setIsOpenEdit}
          uploadProps={uploadProps}
          selectedPartner={selectedPartner}
          formSelectPartner={formSelectPartner}
          onSubmitUpdate={onSubmitUpdate}
          dataRow={dataRow}
          hospitalList={hospitalList}
          featurePartnerList={featurePartnerList}
          featureList={featureList}
          filterTabActive={filterTabActive}
          doctorReferenceList={doctorReferenceList}
          serviceReferenceList={serviceReferenceList}
          subjectReferenceList={subjectReferenceList}
          servicePackageList={servicePackageList}
        />
      )}
      {isOpenNew && (
        <HomeBannerSingleNew
          loading={loading}
          contentModal={contentModal.create}
          isOpenNew={isOpenNew}
          setIsOpenNew={setIsOpenNew}
          uploadProps={uploadProps}
          selectedPartner={selectedPartner}
          formSelectPartner={formSelectPartner}
          onSubmitCreate={onSubmitCreate}
          hospitalList={hospitalList}
          featurePartnerList={featurePartnerList}
          featureList={featureList}
          filterTabActive={filterTabActive}
          doctorReferenceList={doctorReferenceList}
          serviceReferenceList={serviceReferenceList}
          subjectReferenceList={subjectReferenceList}
          servicePackageList={servicePackageList}
        />
      )}
      {isOpenViewCTA && (
        <Modal
          {...contentModal.viewCTA}
          open={isOpenViewCTA}
          onCancel={() => {
            setIsOpenViewCTA(!isOpenViewCTA)
            setDataCTA(null)
          }}
        >
          <MPJSONEditor
            readOnly={true}
            content={{
              json:
                dataCTA && checkJson(dataCTA)
                  ? JSON.parse(`${dataCTA}`)
                  : dataCTA
                  ? dataCTA
                  : {},
              text: undefined
            }}
          />
        </Modal>
      )}
    </>
  )
}

export default HomeBannerSingle

import { Form, Input, Radio, DatePicker, Select, Switch } from 'antd'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import { size } from 'lodash'
import { getReplaceUTF8, MPButton } from '@medpro-libs/medpro-component-libs'
import IconUploadImg from '../../../../images/iconUploadImg.png'
import MPImageFormItemMultiple from '../../../../atoms/MPImageFormItemMultiple'
import styles from '../../styles.module.less'

const { RangePicker } = DatePicker
const { Option } = Select

export const appOptions = [
  {
    title: 'Tính năng',
    value: 'feature'
  },
  {
    title: 'Đặt lịch',
    value: 'booking'
  },
  {
    title: 'Mở Link',
    value: 'link'
  },
  {
    title: 'Bệnh viện',
    value: 'hospital'
  },
  {
    title: '<PERSON><PERSON><PERSON> kh<PERSON>',
    value: 'package'
  },
  {
    title: '<PERSON><PERSON><PERSON> sĩ',
    value: 'doctor'
  },
  {
    title: '<PERSON><PERSON><PERSON> sĩ Telemed',
    value: 'doctorTelemed'
  }
]

export const targetLink = [
  {
    title: '_blank',
    value: '_blank'
  },
  {
    title: '_self',
    value: '_self'
  }
]

export const inputItems = ({
  form,
  uploadProps,
  onChangeImage,
  filterTabActive,
  hospitalList,
  selectedPartner,
  featurePartnerList,
  featureList,
  doctorReferenceList,
  serviceReferenceList,
  subjectReferenceList,
  servicePackageList,
  formSelectPartner
}: any) => {
  const [timeType, setTimeType] = useState(true)
  const [appOption, setAppOption] = useState<any>(null)

  useEffect(() => {
    if (appOption) {
      let target
      switch (appOption) {
        case 'hospital':
          target = 'HOSPITAL_DETAIL'
          break
        case 'package':
          target = 'PACKAGE_DETAIL'
          break
        case 'doctor':
          target = 'DOCTOR_DETAIL'
          break
        case 'doctorTelemed':
          target = 'TELEMED'
          break
        default:
          break
      }
      form.setFieldsValue({
        type: 'SCREEN',
        target: target
      })
    }
  }, [form, appOption])

  const onChangeTimeType = (e: any) => {
    setTimeType(e.target.value)
  }

  const onSelectPartner = (value: any) => {
    formSelectPartner({ partnerId: value, action: appOption })
    form.setFieldsValue({
      featureType: null,
      subjectId: null,
      doctorId: null,
      serviceId: null,
      treeId: null,
      link: null,
      browser: false
    })
  }

  const onSelectAppOption = (value: any) => {
    setAppOption(value)
    if (value === 'doctorTelemed') {
      formSelectPartner({ partnerId: selectedPartner })
    } else {
      formSelectPartner(null)
    }
    form.setFieldsValue({
      featureType: null,
      partnerId: null,
      subjectId: null,
      doctorId: null,
      serviceId: null,
      treeId: null,
      link: null,
      browser: false
    })
  }

  const getAppOption = () => {
    switch (appOption) {
      case 'feature':
        return [
          {
            id: 'featureType',
            type: 'text',
            label: 'Kiểu tính năng',
            placeholder: 'Chọn kiểu tính năng',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn kiểu tính năng!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {featureList && size(featureList) > 0
                    ? featureList?.map((item: any, index: number) => (
                        <Option key={index} value={item.type}>
                          {`${item.name} - ${item.type}`}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          }
        ]
      case 'booking':
        return [
          {
            id: 'partnerId',
            type: 'text',
            label: 'Bệnh viện',
            placeholder: 'Chọn bệnh viện',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn bệnh viện!'
                  }
                ]}
              >
                <Select
                  onChange={(value) => {
                    onSelectPartner(value)
                  }}
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {hospitalList && size(hospitalList) > 0
                    ? hospitalList?.map((item: any, index: number) => (
                        <Option key={index} value={item.partnerId}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          { group: true },
          {
            id: 'treeId',
            type: 'text',
            label: 'Luồng khám',
            placeholder: 'Chọn luồng khám',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn luồng khám!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {featurePartnerList && size(featurePartnerList) > 0
                    ? featurePartnerList?.map((item: any, index: number) => (
                        <Option key={index} value={item.type}>
                          {`${item.name} - ${item.type}`}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: true
          },
          {
            id: 'subjectId',
            type: 'text',
            label: 'Chuyên khoa',
            placeholder: 'Chọn chuyên khoa',
            require: false,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn chuyên khoa!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {subjectReferenceList && size(subjectReferenceList) > 0
                    ? subjectReferenceList?.map((item: any, index: number) => (
                        <Option key={index} value={item.id}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: true
          },
          {
            id: 'doctorId',
            type: 'text',
            label: 'Bác sĩ',
            placeholder: 'Chọn bác sĩ',
            require: false,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn bác sĩ!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {doctorReferenceList && size(doctorReferenceList) > 0
                    ? doctorReferenceList?.map((item: any, index: number) => (
                        <Option key={index} value={item.id}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: true
          },
          {
            id: 'serviceId',
            type: 'text',
            label: 'Dịch vụ',
            placeholder: 'Chọn dịch vụ',
            require: false,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn dịch vụ!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {serviceReferenceList && size(serviceReferenceList) > 0
                    ? serviceReferenceList?.map((item: any, index: number) => (
                        <Option key={index} value={item.id}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: true
          }
        ]
      case 'link':
        return [
          {
            id: 'link',
            type: 'text',
            label: 'URL trên di động',
            placeholder: 'Ex: https://medpro.vn/dich-vu-y-te...',
            require: false,
            enter: ({ id, label, placeholder, hidden, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng nhập URL trên di động!'
                  }
                ]}
              >
                <Input placeholder={placeholder} />
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          {
            id: 'browser',
            type: 'text',
            label: 'Trình duyệt',
            placeholder: '',
            require: false,
            enter: ({ id, require, label, hidden }: any) => {
              return (
                <Form.Item
                  label={label}
                  name={id}
                  className={styles['formInputItem']}
                  valuePropName='checked'
                >
                  <Switch
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                  />
                </Form.Item>
              )
            },
            hidden: false,
            group: false
          }
        ]
      case 'hospital':
        return [
          {
            id: 'partnerId',
            type: 'text',
            label: 'Bệnh viện',
            placeholder: 'Chọn bệnh viện',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn bệnh viện!'
                  }
                ]}
              >
                <Select
                  onChange={(value) => {
                    onSelectPartner(value)
                  }}
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {hospitalList && size(hospitalList) > 0
                    ? hospitalList?.map((item: any, index: number) => (
                        <Option key={index} value={item.partnerId}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          {
            id: 'target',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          },
          {
            id: 'type',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          }
        ]
      case 'package':
        return [
          {
            id: 'partnerId',
            type: 'text',
            label: 'Bệnh viện',
            placeholder: 'Chọn bệnh viện',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn bệnh viện!'
                  }
                ]}
              >
                <Select
                  onChange={(value) => {
                    onSelectPartner(value)
                  }}
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {hospitalList && size(hospitalList) > 0
                    ? hospitalList?.map((item: any, index: number) => (
                        <Option key={index} value={item.partnerId}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          {
            id: 'serviceId',
            type: 'text',
            label: 'Gói khám',
            placeholder: 'Chọn gói khám',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn gói khám!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {servicePackageList && size(servicePackageList) > 0
                    ? servicePackageList?.map((item: any, index: number) => (
                        <Option key={index} value={item.id}>
                          {item.title}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          {
            id: 'target',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          },
          {
            id: 'type',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          }
        ]
      case 'doctor':
        return [
          {
            id: 'partnerId',
            type: 'text',
            label: 'Bệnh viện',
            placeholder: 'Chọn bệnh viện',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn bệnh viện!'
                  }
                ]}
              >
                <Select
                  onChange={(value) => {
                    onSelectPartner(value)
                  }}
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {hospitalList && size(hospitalList) > 0
                    ? hospitalList?.map((item: any, index: number) => (
                        <Option key={index} value={item.partnerId}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          {
            id: 'doctorId',
            type: 'text',
            label: 'Bác sĩ',
            placeholder: 'Chọn bác sĩ',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn bác sĩ!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {doctorReferenceList && size(doctorReferenceList) > 0
                    ? doctorReferenceList?.map((item: any, index: number) => (
                        <Option key={index} value={item.id}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          {
            id: 'target',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          },
          {
            id: 'type',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          }
        ]
      case 'doctorTelemed':
        return [
          {
            id: 'subjectId',
            type: 'text',
            label: 'Chuyên khoa',
            placeholder: 'Chọn chuyên khoa',
            require: true,
            enter: ({ id, label, placeholder, require }: any) => (
              <Form.Item
                label={label}
                name={id}
                rules={[
                  {
                    required: require,
                    message: 'Vui lòng chọn chuyên khoa!'
                  }
                ]}
              >
                <Select
                  placeholder={placeholder}
                  showSearch
                  allowClear
                  filterOption={(input, option: any) =>
                    getReplaceUTF8(
                      (option?.children as unknown as string).toLowerCase()
                    ).includes(getReplaceUTF8(input.toLowerCase()))
                  }
                >
                  {subjectReferenceList && size(subjectReferenceList) > 0
                    ? subjectReferenceList?.map((item: any, index: number) => (
                        <Option key={index} value={item.id}>
                          {item.name}
                        </Option>
                      ))
                    : undefined}
                </Select>
              </Form.Item>
            ),
            hidden: false,
            group: false
          },
          {
            id: 'target',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          },
          {
            id: 'type',
            type: 'text',
            label: '',
            placeholder: '',
            require: false,
            enter: ({ id, require, type, placeholder, label, hidden }: any) => {
              return (
                <Form.Item label={label} name={id} hidden={hidden}>
                  <Input type={type} placeholder={placeholder} />
                </Form.Item>
              )
            },
            hidden: true,
            group: false
          }
        ]
      default:
        return []
    }
  }

  return [
    {
      id: 'banner',
      type: 'upload',
      label: 'Tải hình lên',
      placeholder: '',
      require: true,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item
            name={id}
            label={label}
            valuePropName={'url'}
            extra=''
            rules={[
              {
                required: require,
                message: `Vui lòng tải hình lên!`
              }
            ]}
          >
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
              className={styles['uploadWrapper']}
              customBody={
                <div className={styles['uploadBox']}>
                  <Image src={IconUploadImg} alt={''} priority />
                  <div className={styles['text']}>
                    Kéo tập tin vào đây hoặc nhấp vào bên dưới để tải lên
                    <div className={styles['extension']}>
                      .ai, .png, .jpg, .gif, .svg, .pdf, .eps, .jpeg
                      <br />
                      10mb max file size.
                    </div>
                  </div>
                  <div>
                    <MPButton typeCustom={'upload'}>Chọn tập tin</MPButton>
                  </div>
                </div>
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'alt',
      type: 'text',
      label: 'Alt hình ảnh',
      placeholder: 'Nhập alt hình ảnh',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: `Vui lòng nhập alt hình ảnh!`
              }
            ]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'url',
      type: 'text',
      label: 'URL',
      placeholder: 'Nhập url',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    filterTabActive !== 'app'
      ? {
          id: 'target',
          type: 'text',
          label: 'Kiểu điều hướng cho URL',
          placeholder: 'Chọn kiểu điều hướng cho URL',
          require: false,
          enter: ({ id, require, type, placeholder, label, hidden }: any) => {
            return (
              <Form.Item label={label} name={id} hidden={hidden}>
                <Select placeholder={placeholder}>
                  {targetLink.map((item: any, index: number) => (
                    <Option key={index} value={item.value}>
                      {item.title}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )
          },
          hidden: filterTabActive === 'app',
          group: false
        }
      : { group: false },
    {
      id: 'display',
      type: 'text',
      label: 'Thời gian hiển thị',
      placeholder: 'Chọn thời gian hiển thị',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng chọn thời gian hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={onChangeTimeType}
              options={[
                {
                  value: true,
                  label: 'Luôn'
                },
                {
                  value: false,
                  label: 'Theo giờ'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'timePicker',
      type: 'text',
      label: '',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={
              !timeType
                ? [
                    {
                      required: require,
                      message: 'Vui lòng chọn thời gian hiển thị!'
                    }
                  ]
                : []
            }
          >
            <RangePicker
              showTime
              format='DD/MM/YYYY HH:mm'
              placeholder={['Thời gian bắt đầu', 'Thời gian kết thúc']}
            />
          </Form.Item>
        )
      },
      hidden: timeType,
      group: false
    },
    {
      id: 'status',
      type: 'text',
      label: 'Trạng thái',
      placeholder: 'Chọn trạng thái',
      require: false,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Switch />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    filterTabActive === 'app' && {
      id: 'action',
      type: 'text',
      label: 'Cài đặt hiển thị trên di động',
      placeholder: 'Chọn cài đặt hiển thị trên di động',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng chọn cài đặt hiển thị trên di động!'
              }
            ]}
          >
            <Select
              placeholder={placeholder}
              onChange={(value) => {
                onSelectAppOption(value)
              }}
            >
              {appOptions.map((item: any, index: number) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    ...(getAppOption() ?? [])
  ]
}

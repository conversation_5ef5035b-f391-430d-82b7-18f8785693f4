import {
  DatePicker,
  Form,
  FormInstance,
  Input,
  InputNumber,
  Radio,
  Select,
  Switch
} from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { Valid } from '../../../helpers/valid'
import MPImageFormItemMultiple from '../../../atoms/MPImageFormItemMultiple'
import Image from 'next/image'
import MPButton from '../../../atoms/MPButton'
import IconUploadImg from '../../../images/iconUploadImg.png'
import styles from '../styles.module.less'
import { getReplaceUTF8 } from '../../../helpers/func'

const { RangePicker } = DatePicker
const valid = new Valid()
interface ItemsBaseFormProps {
  form: FormInstance<any>
  uploadProps: any
  onChangeImage: (key: any, value: any) => void
  baseValuePopupConfig?: any
  onChangeConfig?: (value: any, params?: any) => void
  methodsBase?: any
  typeBooking: string
  typeDetail: string
  onChangeTypeBooking: (value: any) => void
  onChangeTypeDetail: (value: any) => void
  toggleModal: (value: any) => void
}

const required = (require: boolean, message: any) => {
  return [
    {
      required: require,
      message: `Vui lòng ${message}`
    }
  ]
}

export const ItemsBaseForm = ({
  form,
  uploadProps,
  onChangeImage,
  methodsBase,
  baseValuePopupConfig,
  typeBooking,
  typeDetail,
  onChangeTypeBooking,
  onChangeTypeDetail,
  toggleModal
}: ItemsBaseFormProps) => {
  return [
    {
      id: 'status',
      type: 'select',
      label: 'Trạng thái',
      placeholder: '',
      require: false,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item label={label} name={id}>
            <Switch checkedChildren='ON' unCheckedChildren='OFF' />
          </Form.Item>
        )
      }
    },
    {
      id: 'datePeriod',
      type: 'text',
      label: 'Thời gian hiển thị',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng chọn thời gian hiển thị!'
              }
            ]}
          >
            <RangePicker
              showTime
              format='DD/MM/YYYY HH:mm'
              placeholder={['Thời gian bắt đầu', 'Thời gian kết thúc']}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    }
  ]
}

export const ItemsCreateForm = ({
  form,
  uploadProps,
  onChangeImage,
  baseValuePopupConfig,
  onChangeConfig,
  typeBooking,
  typeDetail,
  onChangeTypeBooking,
  onChangeTypeDetail,
  toggleModal
}: ItemsBaseFormProps) => {
  return [
    {
      id: 'imagePcUrl',
      type: 'upload',
      label: 'Hình ảnh Website',
      placeholder: '',
      require: true,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
              className={styles['uploadWrapper']}
              customBody={
                <div className={styles['uploadBox']}>
                  <Image src={IconUploadImg} alt={''} priority />
                  <div className={styles['text']}>
                    Kéo tập tin vào đây hoặc nhấp vào bên dưới để tải lên
                    <div className={styles['extension']}>
                      .ai, .png, .jpg, .gif, .svg, .pdf, .eps, .jpeg
                      <br />
                      10mb max file size.
                    </div>
                  </div>
                  <div>
                    <MPButton typeCustom={'upload'}>Chọn tập tin</MPButton>
                  </div>
                </div>
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'ctaPC_label_booking',
      type: 'text',
      label: 'Tiêu đề Đặt Khám',
      placeholder: 'Ex: Đặt Khám Ngay, Mua Ngay',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input
              defaultValue={'Đặt khám ngay'}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'ctaPC_link_booking',
      type: 'text',
      label: 'Link Đặt Khám',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'ctaPC_target_booking',
      type: 'text',
      label: 'Target Đặt Khám',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Switch checkedChildren='New Tab' unCheckedChildren='Current Tab' />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'ctaPC_label_detail',
      type: 'text',
      label: 'Tiêu đề Chi Tiết',
      placeholder: 'Ex: Xem Chi Tiết',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input
              defaultValue={'Xem chi tiết'}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'ctaPC_link_detail',
      type: 'text',
      label: 'Link Chi Tiết',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'ctaPC_target_detail',
      type: 'text',
      label: 'Target Chi Tiết',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Switch checkedChildren='New Tab' unCheckedChildren='Current Tab' />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'imageUrl',
      type: 'upload',
      label: 'Hình ảnh Mobile',
      placeholder: '',
      require: true,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
              className={styles['uploadWrapper']}
              customBody={
                <div className={styles['uploadBox']}>
                  <Image src={IconUploadImg} alt={''} priority />
                  <div className={styles['text']}>
                    Kéo tập tin vào đây hoặc nhấp vào bên dưới để tải lên
                    <div className={styles['extension']}>
                      .ai, .png, .jpg, .gif, .svg, .pdf, .eps, .jpeg
                      <br />
                      10mb max file size.
                    </div>
                  </div>
                  <div>
                    <MPButton typeCustom={'upload'}>Chọn tập tin</MPButton>
                  </div>
                </div>
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_label_booking',
      type: 'text',
      label: 'Tiêu đề Đặt Khám',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input
              defaultValue={'Đặt khám ngay'}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'cta_action_booking',
      type: 'text',
      label: 'Cấu hình Đặt Khám',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng loại hình hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={(e) => {
                onChangeTypeBooking(e.target.value)
              }}
              options={[
                {
                  value: 'hospital',
                  label: 'Cơ sở y tế'
                },
                {
                  value: 'package',
                  label: 'Gói khám'
                },
                {
                  value: 'doctor',
                  label: 'Bác sĩ'
                },
                {
                  value: 'feature',
                  label: 'Tính năng'
                },
                {
                  value: 'booking',
                  label: 'Đặt lịch'
                },
                {
                  value: 'link',
                  label: 'Mở Link'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_partnerId_booking',
      type: 'text',
      label: 'Chọn Cơ Sở Y Tế',
      placeholder: 'Chọn Cơ Sở Y Tế',
      require: typeDetail !== 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Select
              placeholder={placeholder}
              showSearch
              filterOption={(input, option) =>
                getReplaceUTF8(
                  (option?.children as unknown as string).toLowerCase()
                ).includes(getReplaceUTF8(input?.toLowerCase()))
              }
              onChange={(value: any) => {
                onChangeConfig?.(typeBooking, { partnerId: value })
              }}
              options={[
                ...(baseValuePopupConfig?.hospitalList?.map(
                  (hospital: any) => ({
                    label: hospital.name,
                    value: hospital.partnerId
                  })
                ) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: ![
        'hospital',
        'feature',
        'package',
        'doctor',
        'feature',
        'booking'
      ].includes(typeBooking)
    },
    {
      id: 'cta_doctorId_booking',
      type: 'text',
      label: 'Chọn Bác Sĩ',
      placeholder: 'Chọn bác sĩ',
      require: typeBooking === 'doctor',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              defaultValue={''}
              showSearch
              options={[
                ...(baseValuePopupConfig?.doctorList?.map((item: any) => ({
                  label: item.name,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'doctor'
    },
    {
      id: 'cta_packageId_booking',
      type: 'text',
      label: 'Chọn Gói Khám',
      placeholder: 'Chọn gói khám',
      require: typeBooking === 'package',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.packageList?.map((item: any) => ({
                  label: item.title,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'package'
    },
    {
      id: 'cta_featureType_booking',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: 'Chọn tính năng',
      require: typeBooking === 'feature',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.name,
                  value: item.slug
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'feature'
    },
    {
      id: 'cta_treeId_booking',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: 'Chọn tính năng',
      require: typeBooking === 'booking',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.name,
                  value: item.slug
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'booking'
    },
    {
      id: 'cta_browser_booking',
      type: 'text',
      label: 'Điều hướng URL',
      placeholder: '',
      require: typeBooking === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Radio.Group
              options={[
                {
                  value: true,
                  label: 'Mở trình duyệt'
                },
                {
                  value: false,
                  label: 'Mở trong ứng dụng'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'link'
    },
    {
      id: 'cta_link_booking',
      type: 'text',
      label: 'URL',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: typeBooking === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'link'
    },
    {
      id: 'cta_textInApp_booking',
      type: 'text',
      label: 'Label trong ứng dụng',
      placeholder: 'Nhập tiêu đề trong ứng dụng',
      require: typeBooking === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'link'
    },
    {
      id: 'cta_label_detail',
      type: 'text',
      label: 'Tiêu đề Chi Tiết',
      placeholder: 'Ex: Xem Chi tiết',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input
              defaultValue={'Xem chi tiết'}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_action_detail',
      type: 'text',
      label: 'Cấu hình Chi tiết',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng loại hình hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={(e) => onChangeTypeDetail(e.target.value)}
              options={[
                {
                  value: 'hospital',
                  label: 'Cơ sở y tế'
                },
                {
                  value: 'package',
                  label: 'Gói khám'
                },
                {
                  value: 'doctor',
                  label: 'Bác sĩ'
                },
                {
                  value: 'feature',
                  label: 'Tính năng'
                },
                {
                  value: 'booking',
                  label: 'Đặt lịch'
                },
                {
                  value: 'link',
                  label: 'Mở Link'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_partnerId_detail',
      type: 'text',
      label: 'Chọn Cơ Sở Y Tế',
      placeholder: 'Chọn cơ sở y tế',
      require: typeDetail !== 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              allowClear
              filterOption={(input, option) =>
                getReplaceUTF8(
                  (option?.children as unknown as string).toLowerCase()
                ).includes(getReplaceUTF8(input?.toLowerCase()))
              }
              onChange={(value: any) => {
                onChangeConfig?.(typeDetail, { partnerId: value })
              }}
              options={[
                ...(baseValuePopupConfig?.hospitalList?.map(
                  (hospital: any) => ({
                    label: hospital.name,
                    value: hospital.partnerId
                  })
                ) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: ![
        'hospital',
        'feature',
        'package',
        'doctor',
        'feature',
        'booking'
      ].includes(typeDetail)
    },
    {
      id: 'cta_doctorId_detail',
      type: 'text',
      label: 'Chọn Bác Sĩ',
      placeholder: 'Chọn bác sĩ',
      require: typeDetail === 'doctor',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.doctorList?.map((item: any) => ({
                  label: item.name,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'doctor'
    },
    {
      id: 'cta_packageId_detail',
      type: 'text',
      label: 'Chọn Gói Khám',
      placeholder: 'Chọn gói khám',
      require: typeDetail === 'package',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              options={[
                ...(baseValuePopupConfig?.packageList?.map((item: any) => ({
                  label: item.name,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'package'
    },
    {
      id: 'cta_featureType_detail',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: '',
      require: typeDetail === 'feature',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.title,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'feature'
    },
    {
      id: 'cta_treeId_detail',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: 'Chọn tính năng',
      require: typeDetail === 'booking',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.title,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'booking'
    },
    {
      id: 'cta_browser_detail',
      type: 'text',
      label: 'Điều hướng URL',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Radio.Group
              options={[
                {
                  value: true,
                  label: 'Mở trình duyệt'
                },
                {
                  value: false,
                  label: 'Mở trong ứng dụng'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'link'
    },
    {
      id: 'cta_link_detail',
      type: 'text',
      label: 'URL',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: typeDetail === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'link'
    },
    {
      id: 'cta_textInApp_detail',
      type: 'text',
      label: 'Label trong ứng dụng',
      placeholder: 'Nhập tiêu đề trong ứng dụng',
      require: typeDetail === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'link'
    },
    {
      title: 'Thời gian hiển thị',
      key: 'displayPeriod',
      width: 200,
      render: (row: any) => (
        <div className={styles['periodInfo']}>
          <div className={styles['startDate']}>
            {row?.displayPeriod?.startDate}
          </div>
          <div className={styles['dateLine']} />
          <div className={styles['endDate']}>{row?.displayPeriod?.endDate}</div>
        </div>
      )
    }
  ]
}

export const ItemsEditDesktopForm = ({
  form,
  uploadProps,
  onChangeImage
}: ItemsBaseFormProps) => {
  return [
    {
      id: 'imagePcUrl',
      type: 'upload',
      label: 'Hình ảnh Website',
      placeholder: '',
      require: true,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
              className={styles['uploadWrapper']}
              customBody={
                <div className={styles['uploadBox']}>
                  <Image src={IconUploadImg} alt={''} priority />
                  <div className={styles['text']}>
                    Kéo tập tin vào đây hoặc nhấp vào bên dưới để tải lên
                    <div className={styles['extension']}>
                      .ai, .png, .jpg, .gif, .svg, .pdf, .eps, .jpeg
                      <br />
                      10mb max file size.
                    </div>
                  </div>
                  <div>
                    <MPButton typeCustom={'upload'}>Chọn tập tin</MPButton>
                  </div>
                </div>
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'ctaPC_label_booking',
      type: 'text',
      label: 'Tiêu đề Đặt Khám',
      placeholder: 'Ex: Đặt Khám Ngay, Mua Ngay',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'ctaPC_link_booking',
      type: 'text',
      label: 'Link Đặt Khám',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'ctaPC_target_booking',
      type: 'text',
      label: 'Target Đặt Khám',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Switch checkedChildren='New Tab' unCheckedChildren='Current Tab' />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'ctaPC_label_detail',
      type: 'text',
      label: 'Tiêu đề Chi Tiết',
      placeholder: 'Ex: Xem Chi Tiết',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'ctaPC_link_detail',
      type: 'text',
      label: 'Link Chi Tiết',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'ctaPC_target_detail',
      type: 'text',
      label: 'Target Chi Tiết',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Switch checkedChildren='New Tab' unCheckedChildren='Current Tab' />
          </Form.Item>
        )
      },
      hidden: false
    }
  ]
}

export const ItemsEditMobileForm = ({
  form,
  uploadProps,
  onChangeImage,
  baseValuePopupConfig,
  onChangeConfig,
  typeBooking,
  typeDetail,
  onChangeTypeBooking,
  onChangeTypeDetail,
  toggleModal
}: ItemsBaseFormProps) => {
  return [
    {
      id: 'imageUrl',
      type: 'upload',
      label: 'Hình ảnh Mobile',
      placeholder: '',
      require: true,
      enter: ({ label, id, placeholder, require }: any) => {
        return (
          <Form.Item name={id} label={label} valuePropName={'url'} extra=''>
            <MPImageFormItemMultiple
              {...uploadProps}
              placeholder={placeholder}
              multiple={false}
              maxCount={1}
              url={form.getFieldValue(id)}
              onChange={(value: any) => onChangeImage(id, value)}
              cropImage={false}
              className={styles['uploadWrapper']}
              customBody={
                <div className={styles['uploadBox']}>
                  <Image src={IconUploadImg} alt={''} priority />
                  <div className={styles['text']}>
                    Kéo tập tin vào đây hoặc nhấp vào bên dưới để tải lên
                    <div className={styles['extension']}>
                      .ai, .png, .jpg, .gif, .svg, .pdf, .eps, .jpeg
                      <br />
                      10mb max file size.
                    </div>
                  </div>
                  <div>
                    <MPButton typeCustom={'upload'}>Chọn tập tin</MPButton>
                  </div>
                </div>
              }
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_label_booking',
      type: 'text',
      label: 'Tiêu đề Đặt Khám',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false
    },
    {
      id: 'cta_action_booking',
      type: 'text',
      label: 'Cấu hình Đặt Khám',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng loại hình hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={(e) => {
                onChangeTypeBooking(e.target.value)
              }}
              options={[
                {
                  value: 'hospital',
                  label: 'Cơ sở y tế'
                },
                {
                  value: 'package',
                  label: 'Gói khám'
                },
                {
                  value: 'doctor',
                  label: 'Bác sĩ'
                },
                {
                  value: 'feature',
                  label: 'Tính năng'
                },
                {
                  value: 'booking',
                  label: 'Đặt lịch'
                },
                {
                  value: 'link',
                  label: 'Mở Link'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_partnerId_booking',
      type: 'text',
      label: 'Chọn Cơ Sở Y Tế',
      placeholder: 'Chọn Cơ Sở Y Tế',
      require: typeDetail !== 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Select
              placeholder={placeholder}
              showSearch
              filterOption={(input, option) =>
                getReplaceUTF8(
                  (option?.children as unknown as string).toLowerCase()
                ).includes(getReplaceUTF8(input?.toLowerCase()))
              }
              onChange={(value: any) => {
                onChangeConfig?.(typeBooking, { partnerId: value })
              }}
              options={[
                ...(baseValuePopupConfig?.hospitalList?.map(
                  (hospital: any) => ({
                    label: hospital.name,
                    value: hospital.partnerId
                  })
                ) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: ![
        'hospital',
        'feature',
        'package',
        'doctor',
        'feature',
        'booking'
      ].includes(typeBooking)
    },
    {
      id: 'cta_doctorId_booking',
      type: 'text',
      label: 'Chọn Bác Sĩ',
      placeholder: 'Chọn bác sĩ',
      require: typeBooking === 'doctor',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.doctorList?.map((item: any) => ({
                  label: item.name,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'doctor'
    },
    {
      id: 'cta_packageId_booking',
      type: 'text',
      label: 'Chọn Gói Khám',
      placeholder: 'Chọn gói khám',
      require: typeBooking === 'package',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.packageList?.map((item: any) => ({
                  label: item.title,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'package'
    },
    {
      id: 'cta_featureType_booking',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: 'Chọn tính năng',
      require: typeBooking === 'feature',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.name,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'feature'
    },
    {
      id: 'cta_treeId_booking',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: 'Chọn tính năng',
      require: typeBooking === 'booking',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.name,
                  value: item.slug
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'booking'
    },
    {
      id: 'cta_browser_booking',
      type: 'text',
      label: 'Điều hướng URL',
      placeholder: '',
      require: typeBooking === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Radio.Group
              options={[
                {
                  value: true,
                  label: 'Mở trình duyệt'
                },
                {
                  value: false,
                  label: 'Mở trong ứng dụng'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'link'
    },
    {
      id: 'cta_link_booking',
      type: 'text',
      label: 'URL',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: typeBooking === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'link'
    },
    {
      id: 'cta_textInApp_booking',
      type: 'text',
      label: 'Label trong ứng dụng',
      placeholder: 'Nhập tiêu đề trong ứng dụng',
      require: typeBooking === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeBooking !== 'link'
    },
    {
      id: 'cta_label_detail',
      type: 'text',
      label: 'Tiêu đề Chi Tiết',
      placeholder: 'Ex: Xem Chi tiết',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ validator: valid.trimRequired, required: require }]}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_action_detail',
      type: 'text',
      label: 'Cấu hình Chi tiết',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[
              {
                required: require,
                message: 'Vui lòng loại hình hiển thị!'
              }
            ]}
          >
            <Radio.Group
              onChange={(e) => {
                onChangeTypeDetail(e.target.value)
                form.resetFields(['cta_partnerId_detail'])
              }}
              options={[
                {
                  value: 'hospital',
                  label: 'Cơ sở y tế'
                },
                {
                  value: 'package',
                  label: 'Gói khám'
                },
                {
                  value: 'doctor',
                  label: 'Bác sĩ'
                },
                {
                  value: 'feature',
                  label: 'Tính năng'
                },
                {
                  value: 'booking',
                  label: 'Đặt lịch'
                },
                {
                  value: 'link',
                  label: 'Mở Link'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: false,
      group: false
    },
    {
      id: 'cta_partnerId_detail',
      type: 'text',
      label: 'Chọn Cơ Sở Y Tế',
      placeholder: 'Chọn cơ sở y tế',
      require: typeDetail !== 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              allowClear
              filterOption={(input, option) =>
                getReplaceUTF8(
                  (option?.children as unknown as string).toLowerCase()
                ).includes(getReplaceUTF8(input?.toLowerCase()))
              }
              onChange={(value: any) => {
                onChangeConfig?.(typeDetail, { partnerId: value })
              }}
              options={[
                ...(baseValuePopupConfig?.hospitalList?.map(
                  (hospital: any) => ({
                    label: hospital.name,
                    value: hospital.partnerId
                  })
                ) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: ![
        'hospital',
        'feature',
        'package',
        'doctor',
        'feature',
        'booking'
      ].includes(typeDetail)
    },
    {
      id: 'cta_doctorId_detail',
      type: 'text',
      label: 'Chọn Bác Sĩ',
      placeholder: 'Chọn bác sĩ',
      require: typeDetail === 'doctor',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={
                [
                  // ...(baseValuePopupConfig?.doctorList?.map((item: any) => ({
                  //   label: item.name,
                  //   value: item.id
                  // })) || '')
                ]
              }
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'doctor'
    },
    {
      id: 'cta_packageId_detail',
      type: 'text',
      label: 'Chọn Gói Khám',
      placeholder: 'Chọn gói khám',
      require: typeDetail === 'package',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.packageList?.map((item: any) => ({
                  label: item.title,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'package'
    },
    {
      id: 'cta_featureType_detail',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: '',
      require: typeDetail === 'feature',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.name,
                  value: item.type
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'feature'
    },
    {
      id: 'cta_treeId_detail',
      type: 'text',
      label: 'Chọn Tính Năng',
      placeholder: 'Chọn tính năng',
      require: typeDetail === 'booking',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Select
              placeholder={placeholder}
              showSearch
              options={[
                ...(baseValuePopupConfig?.featureList?.map((item: any) => ({
                  label: item.title,
                  value: item.id
                })) || [])
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'booking'
    },
    {
      id: 'cta_browser_detail',
      type: 'text',
      label: 'Điều hướng URL',
      placeholder: '',
      require: true,
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Radio.Group
              options={[
                {
                  value: true,
                  label: 'Mở trình duyệt'
                },
                {
                  value: false,
                  label: 'Mở trong ứng dụng'
                }
              ]}
            />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'link'
    },
    {
      id: 'cta_link_detail',
      type: 'text',
      label: 'URL',
      placeholder: 'Ex: https://medpro.vn/tin-tuc/.....',
      require: typeDetail === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item label={label} name={id} hidden={hidden}>
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'link'
    },
    {
      id: 'cta_textInApp_detail',
      type: 'text',
      label: 'Label trong ứng dụng',
      placeholder: 'Nhập tiêu đề trong ứng dụng',
      require: typeDetail === 'link',
      enter: ({ id, require, type, placeholder, label, hidden }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            hidden={hidden}
            rules={required(require, label)}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: typeDetail !== 'link'
    }
  ]
}

import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  Modal,
  Radio,
  Switch,
  type TableColumnsType,
  TablePaginationConfig,
  Tabs,
  Image,
  Tooltip,
  Form,
  TabsProps
} from 'antd'
import cx from 'classnames'
import { FiPlus } from 'react-icons/fi'
import dayjs from 'dayjs'
import {
  MPButton,
  normalizeTable,
  useDynamicContentHeight
} from '@medpro-libs/medpro-component-libs'
import MPTable, { DragHandle } from '../../../atoms/MPTable'
import styles from '../styles.module.less'
import { RadioChangeEvent } from 'antd/lib'
import {
  ItemsBaseForm,
  ItemsCreateForm,
  ItemsEditDesktopForm,
  ItemsEditMobileForm
} from './BaseItems'
import { get, isArray } from 'lodash'

export interface Props {
  headerRef: any
  heightContent: number
  popupList: any
  loading?: boolean
  uploadProps: any
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  methodsPopup: any
  methodsBase: any
  baseValuePopupConfig: any
}

const Popup = ({
  headerRef,
  heightContent,
  popupList,
  loading,
  uploadProps,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  methodsPopup,
  methodsBase,
  baseValuePopupConfig
}: Props) => {
  const filterRef = useRef<HTMLDivElement>(null)
  const { heightContent: dynHeightContent, heightTbBody } =
    useDynamicContentHeight([headerRef.current, filterRef.current], {
      key: 'table',
      heightContent: heightContent - 70
    })
  const [isOpenNew, setIsOpenNew] = useState(false)
  const [isOpenEdit, setIsOpenEdit] = useState(false)
  const [tabActiveKey, setTabActiveKey] = useState('ALL')
  const [platform, setPlatform] = useState('website')

  const [action, setAction] = useState('create')
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false)
  const [selectedPopup, setSelectedPopup] = useState<any>(null)
  const [loadingPlatform, setLoadingPlatform] = useState<boolean>(false)

  const onPressDelete = (row: any) => {
    Modal.confirm({
      title: 'Xác nhận xóa!',
      content: `Bạn có chắc chắn muốn xóa ?`,
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      centered: true,
      onOk() {
        return methodsPopup.deletePopup(row._id)
      }
    })
  }

  const onPressEdit = (row: any) => {
    toggleModal('edit', row)
    // const plat = platform === 'website' ? 'ctaPC' : 'cta'
    onChangeConfig('hospital', {})
    onChangeConfig(row?.cta?.[0]?.action, {
      partnerId: row?.cta?.[0]?.partnerId
    })
    onChangeConfig(row?.cta?.[1]?.action, {
      partnerId: row?.cta?.[1]?.partnerId
    })
  }

  const togglePlatform = (e: RadioChangeEvent) => {
    setLoadingPlatform(true)
    setPlatform(e.target.value)
    setTimeout(() => {
      setLoadingPlatform(false)
    }, 500)
  }

  const toggleModal = (action: string, selected?: string) => {
    setIsOpenModal(!isOpenModal)
    setAction(action)
    setSelectedPopup(selected)
  }

  const dataSource = useMemo(() => {
    if (popupList?.length) {
      return popupList.map((item: any, index: number) => ({
        ...item,
        key: item._id,
        startDate: item.startDate,
        endDate: item.endDate
      }))
    }
    return []
  }, [popupList, tabActiveKey])

  const onChangeTab = (activeKey: string) => {
    setTabActiveKey(activeKey)
    methodsPopup.getPopupList({ tab: activeKey })
  }

  const columns: TableColumnsType<any> = normalizeTable(
    [
      {
        title: 'STT',
        width: 70,
        key: 'position',
        align: 'center',
        fixed: 'left',
        onCell: () => ({ style: { padding: '12px 10px' } }),
        render: (text: any, record: any, index: number) => {
          return <div className={styles['groupPosition']}>{index + 1}</div>
        }
      },
      {
        title: 'Hình ảnh',
        width: 250,
        key: 'image',
        align: 'center',
        fixed: 'left',
        hidden: platform === 'mobile',
        onCell: () => ({ style: { padding: '12px 10px' } }),
        render: (row: any) => (
          <div className={styles['itemInfo']}>
            <DragHandle />
            <div className={styles['itemContent']}>
              <div className={styles['itemImage']}>
                <Image
                  src={row.imagePcUrl}
                  alt={row.name}
                  width={120}
                  height={60}
                />
              </div>
            </div>
          </div>
        )
      },
      {
        title: 'Hình ảnh mobile',
        width: 250,
        key: 'image',
        align: 'center',
        fixed: 'left',
        hidden: platform === 'website',
        onCell: () => ({ style: { padding: '12px 10px' } }),
        render: (row: any) => (
          <div className={styles['itemInfo']}>
            <DragHandle />
            <div className={styles['itemContent']}>
              <div className={styles['itemImage']}>
                <Image
                  src={row.imageUrl}
                  alt={row.name}
                  width={60}
                  height={60}
                />
              </div>
            </div>
          </div>
        )
      },
      {
        title: 'Thời gian hiển thị',
        key: 'displayPeriod',
        width: 350,
        align: 'center',
        render: (row: any) => {
          return (
            <div className={styles['groupPeriod']}>
              <div className={styles['start']}>
                <p>{dayjs(row?.startDate).format('HH:mm, DD/MM/YYYY')}</p>
              </div>
              <div className={styles['line']} />
              <div className={styles['end']}>
                <p>{dayjs(row?.endDate).format('HH:mm, DD/MM/YYYY')}</p>
              </div>
            </div>
          )
        }
      },
      {
        title: 'Trạng thái',
        key: 'status',
        width: 250,
        align: 'center',
        render: (row: any) => {
          return (
            <div
              className={cx(styles['groupStatus'], styles['status'])}
              style={{
                color: row?.tabStatus?.style?.color,
                backgroundColor: row?.tabStatus?.style?.backgroundColor,
                border: row?.tabStatus?.style?.border
              }}
            >
              {row?.tabStatus?.text}
            </div>
          )
        }
      },
      {
        title: 'Bật / Tắt',
        key: 'pin',
        align: 'center',
        width: 200,
        render: (row) => (
          <Switch
            disabled
            checked={row.status}
            checkedChildren='ON'
            unCheckedChildren='OFF'
          />
        )
      },
      {
        title: 'Thao tác',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 150,
        actionType: 'actionButton',
        actionDisplay: ['edit', 'delete']
      }
    ],
    onPressEdit,
    onPressDelete
  )

  const items: TabsProps['items'] & any = [
    {
      key: 'ALL',
      label: 'Tất cả',
      forceRender: true,
      children: null
    },
    {
      key: 'INPROGRESS',
      label: 'Đang hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'PENDING',
      label: 'Chờ hiển thị',
      forceRender: true,
      children: null
    },
    {
      key: 'EXPIRED',
      label: 'Hết hạn',
      forceRender: true,
      children: null
    }
  ]

  const renderModal = (action: string) => {
    switch (action) {
      case 'create':
        return (
          <MPCreatePopupModal
            action={action}
            isOpen={isOpenModal}
            onCancel={() => {
              setIsOpenModal(false)
              setAction('')
            }}
            onSubmit={(body) => {
              methodsPopup.createPopup(body)
              onChangeTab('ALL')
            }}
            loading={loading}
            uploadProps={uploadProps}
            platform={platform}
            methodsBase={methodsBase}
            baseValuePopupConfig={baseValuePopupConfig}
            toggleModal={toggleModal}
          />
        )
      case 'edit':
        return (
          <MPEditPopupModal
            action={action}
            isOpen={isOpenModal}
            onCancel={() => {
              setIsOpenModal(false)
              setAction('')
            }}
            onSubmit={(body) => {
              methodsPopup.updatePopup(body)
              onChangeTab('ALL')
            }}
            uploadProps={uploadProps}
            platform={platform}
            methodsBase={methodsBase}
            baseValuePopupConfig={baseValuePopupConfig}
            initialValues={selectedPopup}
            toggleModal={toggleModal}
          />
        )
      default:
        return null
    }
  }

  const onChangeConfig = (value: any, params?: any) => {
    switch (value) {
      case 'hospital':
        methodsBase.getHospitalList({})
        break
      case 'doctor':
        methodsBase.getDoctorList({ partnerId: params?.partnerId })
        break
      case 'feature':
        methodsBase.getFeatureList({ partnerId: 'medpro' })
        break
      case 'booking':
        methodsBase.getFeatureList({ partnerId: params?.partnerId })
        break
      case 'package':
        methodsBase.getPackageList({ partnerId: params?.partnerId })
        break
      default:
        break
    }
  }

  return (
    <>
      <div className={styles['compTabWrapper']}>
        <Tabs
          defaultActiveKey={'ALL'}
          onChange={onChangeTab}
          tabPosition={'top'}
          tabBarGutter={12}
          items={items}
        />
      </div>
      <div ref={filterRef} className={styles['filterWrapper']}>
        <Radio.Group
          className={styles['left']}
          onChange={togglePlatform}
          defaultValue={'website'}
          value={platform}
          style={{ marginBottom: 8 }}
          optionType='button'
          buttonStyle='solid'
        >
          <Radio.Button value='website'>Website</Radio.Button>
          <Radio.Button value='mobile'>Mobile</Radio.Button>
        </Radio.Group>
        <div className={styles['right']}>
          <div className={styles['actionGroup']}>
            <MPButton
              onClick={() => methodsPopup.getPopupList({ tab: 'ALL' })}
              typeCustom={'repair'}
            >
              <span>Tải lại</span>
            </MPButton>
            <MPButton
              onClick={() => toggleModal('create')}
              typeCustom={'primary'}
            >
              <FiPlus />
              <span>Tạo popup</span>
            </MPButton>
          </div>
        </div>
      </div>
      <div className={styles['tableWrapper']}>
        <MPTable
          type={'dragTable'}
          columns={columns}
          dataSource={dataSource}
          dynHeightContent={dynHeightContent}
          heightTbBody={heightTbBody}
          loading={loading || loadingPlatform}
          pagination={pagination}
          onChangePageEvent={onChangePageEvent}
          onChangeSizeEvent={onChangeSizeEvent}
        />
      </div>
      {renderModal(action)}
    </>
  )
}

const contentModal = (action: string, platform?: string) => {
  switch (action) {
    case 'edit':
      return {
        title: 'Chỉnh sửa popup ' + platform,
        centered: true,
        footer: false,
        className: styles['modalEditWrapper'],
        width: 800
      }
    case 'create':
      return {
        title: 'Tạo mới popup',
        centered: true,
        footer: false,
        className: styles['modalNewWrapper'],
        width: 800
      }
    default:
      return {}
  }
}

interface MPOtherManagementModalProps {
  isOpen: boolean
  onCancel: () => void
  onSubmit: (body: any) => void
  loading?: boolean
  initialValues?: any
  action: string
  uploadProps: any
  platform: string
  methodsBase: any
  baseValuePopupConfig: any
  toggleModal: (action: string, selected?: string) => void
}

const MPCreatePopupModal = ({
  isOpen,
  action,
  onSubmit,
  onCancel,
  loading = false,
  initialValues,
  uploadProps,
  platform,
  methodsBase,
  baseValuePopupConfig,
  toggleModal
}: MPOtherManagementModalProps) => {
  const [form] = Form.useForm()

  const [typeBooking, setTypeBooking] = useState('hospital')
  const [typeDetail, setTypeDetail] = useState('hospital')

  const onChangeTypeBooking = (value: any) => {
    setTypeBooking(value)
    if (['hospital', 'feature', 'package', 'doctor'].includes(value)) {
      onChangeConfig?.('hospital')
      form.resetFields([
        'cta_partnerId_booking',
        'cta_doctorId_booking',
        'cta_packageId_booking',
        'cta_featureType_booking'
      ])
    }
  }

  const onChangeTypeDetail = (value: any) => {
    setTypeDetail(value)
    if (['hospital', 'feature', 'package', 'doctor'].includes(value)) {
      onChangeConfig?.('hospital')
      form.resetFields([
        'cta_partnerId_detail',
        'cta_doctorId_detail',
        'cta_packageId_detail',
        'cta_featureType_detail'
      ])
    }
  }

  const onFinish = (values: any) => {
    const body = {
      status: Boolean(values.status),
      locale: 'vi',
      appId: 'medpro',
      startDate: dayjs(values.startDate),
      endDate: dayjs(values.endDate),
      imageUrl: values.imageUrl[0],
      imagePcUrl: values.imagePcUrl[0],
      cta: [
        {
          text: values.cta_label_booking,
          action: values.cta_action_booking,
          treeId: values.cta_treeId_booking,
          featureType: values.cta_featureType_booking,
          partnerId: values.cta_partnerId_booking,
          doctorId: values.cta_doctorId_booking,
          subjectId: values.cta_subjectId_booking,
          serviceId: values.cta_serviceId_booking,
          link: values.cta_link_booking,
          browser: values.cta_browser_booking,
          textInApp: values.cta_textInApp_booking
        },
        {
          text: values.cta_label_detail,
          action: values.cta_action_detail,
          treeId: values.cta_treeId_detail,
          featureType: values.cta_featureType_detail,
          partnerId: values.cta_partnerId_detail,
          doctorId: values.cta_doctorId_detail,
          subjectId: values.cta_subjectId_detail,
          serviceId: values.cta_serviceId_detail,
          link: values.cta_link_detail,
          browser: values.cta_browser_detail,
          textInApp: values.cta_textInApp_detail
        }
      ],
      ctaPC: [
        {
          link: values.ctaPC_link_booking,
          text: values.ctaPC_label_booking,
          target: values.ctaPC_target_booking
        },
        {
          link: values.ctaPC_link_detail,
          text: values.ctaPC_label_detail,
          target: values.ctaPC_target_detail
        }
      ]
    }
    onSubmit(body)
    toggleModal('create')
  }

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onChangeConfig = (value: any, params?: any) => {
    switch (value) {
      case 'hospital':
        methodsBase.getHospitalList({})
        break
      case 'doctor':
        methodsBase.getDoctorList({ partnerId: params?.partnerId })
        break
      case 'feature':
        methodsBase.getFeatureList({ partnerId: 'medpro' })
        break
      case 'booking':
        methodsBase.getFeatureList({ partnerId: params?.partnerId })
        break
      case 'package':
        methodsBase.getPackageList({ partnerId: params?.partnerId })
        break
      default:
        break
    }
  }

  return (
    <Modal {...contentModal(action)} open={isOpen} onCancel={onCancel}>
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
      >
        <div className={cx(styles['inputItems'], styles['group'])}>
          {MappingForm(
            ItemsBaseForm({
              form,
              uploadProps,
              onChangeImage,
              baseValuePopupConfig,
              onChangeConfig,
              typeBooking,
              typeDetail,
              onChangeTypeBooking,
              onChangeTypeDetail,
              toggleModal
            })
          )}
          {MappingForm(
            ItemsCreateForm({
              form,
              uploadProps,
              onChangeImage,
              baseValuePopupConfig,
              onChangeConfig,
              typeBooking,
              typeDetail,
              onChangeTypeBooking,
              onChangeTypeDetail,
              toggleModal
            })
          )}
        </div>

        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

const MPEditPopupModal = ({
  isOpen,
  action,
  onSubmit,
  onCancel,
  loading = false,
  initialValues,
  uploadProps,
  platform,
  methodsBase,
  baseValuePopupConfig,
  toggleModal
}: MPOtherManagementModalProps) => {
  const [form] = Form.useForm()

  const [typeBooking, setTypeBooking] = useState('hospital')
  const [typeDetail, setTypeDetail] = useState('hospital')

  useEffect(() => {
    onChangeTypeBooking(form.getFieldValue('cta_action_booking'))
    onChangeTypeDetail(form.getFieldValue('cta_action_detail'))
  }, [form])

  const onChangeTypeBooking = (value: any) => {
    setTypeBooking(value)
    if (['hospital', 'feature', 'package', 'doctor'].includes(value)) {
      onChangeConfig?.('hospital')
      form.resetFields([
        'cta_partnerId_booking',
        'cta_doctorId_booking',
        'cta_packageId_booking',
        'cta_featureType_booking'
      ])
    }
  }

  const onChangeTypeDetail = (value: any) => {
    setTypeDetail(value)
    if (['hospital', 'feature', 'package', 'doctor'].includes(value)) {
      onChangeConfig?.('hospital')
      form.resetFields([
        'cta_partnerId_detail',
        'cta_doctorId_detail',
        'cta_packageId_detail',
        'cta_featureType_detail'
      ])
    }
  }

  const onFinish = (values: any) => {
    const commonFields = {
      ...initialValues,
      status: Boolean(values.status),
      startDate: dayjs(values.datePeriod[0]),
      endDate: dayjs(values.datePeriod[1])
    }

    const buildWebsiteBody = () => ({
      ...commonFields,
      imagePcUrl: values.imagePcUrl,
      ctaPC: [
        {
          link: values.ctaPC_link_detail,
          text: values.ctaPC_label_detail,
          target: values.ctaPC_target_detail
        },
        {
          link: values.ctaPC_link_booking,
          text: values.ctaPC_label_booking,
          target: values.ctaPC_target_booking
        }
      ]
    })

    const buildCtaItem = (prefix: string) => ({
      text: values[`${prefix}_label`],
      action: values[`${prefix}_action`],
      treeId: values[`${prefix}_treeId`],
      featureType: values[`${prefix}_featureType`],
      partnerId: values[`${prefix}_partnerId`],
      doctorId: values[`${prefix}_doctorId`],
      subjectId: values[`${prefix}_subjectId`],
      serviceId: values[`${prefix}_serviceId`],
      link: values[`${prefix}_link`],
      browser: values[`${prefix}_browser`],
      textInApp: values[`${prefix}_textInApp`]
    })

    const buildMobileBody = () => ({
      ...commonFields,
      imagePc: values.imagePcUrl,
      cta: [buildCtaItem('cta_detail'), buildCtaItem('cta_booking')]
    })

    const body = platform === 'website' ? buildWebsiteBody() : buildMobileBody()
    onSubmit(body)
    toggleModal('edit')
  }

  const onChangeImage = (key: any, value: any) => {
    const URL = get(value, 'target.url')
    const replaceURL = isArray(URL) ? URL : value
    form.setFieldsValue({ [key]: replaceURL })
  }

  const onChangeConfig = (value: any, params?: any) => {
    console.log('value', value, params)
    switch (value) {
      case 'hospital':
        methodsBase.getHospitalList({})
        break
      case 'doctor':
        methodsBase.getDoctorList({ partnerId: params?.partnerId })
        break
      case 'feature':
        methodsBase.getFeatureList({ partnerId: 'medpro' })
        break
      case 'booking':
        methodsBase.getFeatureList({ partnerId: params?.partnerId })
        break
      case 'package':
        methodsBase.getPackageList({ partnerId: params?.partnerId })
        break
      default:
        break
    }
  }

  const transformInitialValues = useMemo(() => {
    const commonValues = {
      status: Boolean(initialValues?.status),
      datePeriod: [
        dayjs(initialValues?.startDate),
        dayjs(initialValues?.endDate)
      ]
    }

    if (platform === 'website') {
      return {
        ...commonValues,
        imagePcUrl: initialValues?.imagePcUrl,
        ctaPC_label_booking: initialValues?.ctaPC?.[0]?.text,
        ctaPC_link_booking: initialValues?.ctaPC?.[0]?.link,
        ctaPC_target_booking: initialValues?.ctaPC?.[0]?.target,
        ctaPC_label_detail: initialValues?.ctaPC?.[1]?.text,
        ctaPC_link_detail: initialValues?.ctaPC?.[1]?.link,
        ctaPC_target_detail: initialValues?.ctaPC?.[1]?.target
      }
    }

    if (platform === 'mobile') {
      const bookingCta = initialValues?.cta?.[0]
      const detailCta = initialValues?.cta?.[1]

      return {
        ...commonValues,
        imageUrl: initialValues?.imageUrl,
        cta_label_booking: bookingCta?.text,
        cta_action_booking: bookingCta?.action,
        cta_treeId_booking: bookingCta?.treeId,
        cta_featureType_booking: bookingCta?.featureType,
        cta_partnerId_booking: bookingCta?.partnerId,
        cta_doctorId_booking: bookingCta?.doctorId,
        cta_subjectId_booking: bookingCta?.subjectId,
        cta_browser_booking: bookingCta?.browser,
        cta_link_booking: bookingCta?.link,
        cta_textInApp_booking: bookingCta?.textInApp,

        cta_label_detail: detailCta?.text,
        cta_action_detail: detailCta?.action,
        cta_treeId_detail: detailCta?.treeId,
        cta_featureType_detail: detailCta?.featureType,
        cta_partnerId_detail: detailCta?.partnerId,
        cta_doctorId_detail: detailCta?.doctorId,
        cta_subjectId_detail: detailCta?.subjectId,
        cta_serviceId_detail: detailCta?.serviceId,
        cta_browser_detail: detailCta?.browser,
        cta_link_detail: detailCta?.link,
        cta_textInApp_detail: detailCta?.textInApp
      }
    }

    return {}
  }, [initialValues, platform])

  return (
    <Modal
      {...contentModal(action, platform)}
      open={isOpen}
      onCancel={onCancel}
    >
      <Form
        form={form}
        onFinish={onFinish}
        layout={'vertical'}
        className={cx(styles['formGroupWrapper'], styles['two'])}
        initialValues={transformInitialValues}
      >
        <div className={cx(styles['inputItems'], styles['group'])}>
          {MappingForm(
            ItemsBaseForm({
              form,
              uploadProps,
              onChangeImage,
              methodsBase,
              baseValuePopupConfig,
              typeBooking,
              typeDetail,
              onChangeTypeBooking,
              onChangeTypeDetail,
              toggleModal
            })
          )}
          {platform === 'website'
            ? MappingForm(
                ItemsEditDesktopForm({
                  form,
                  uploadProps,
                  onChangeImage: onChangeImage,
                  methodsBase,
                  baseValuePopupConfig,
                  typeBooking,
                  typeDetail,
                  onChangeTypeBooking,
                  onChangeTypeDetail,
                  toggleModal
                })
              )
            : MappingForm(
                ItemsEditMobileForm({
                  form,
                  uploadProps,
                  onChangeImage,
                  baseValuePopupConfig,
                  onChangeConfig,
                  typeBooking,
                  typeDetail,
                  onChangeTypeBooking,
                  onChangeTypeDetail,
                  toggleModal
                })
              )}
        </div>

        <div className={styles['groupAction']}>
          <Form.Item>
            <MPButton onClick={onCancel} typeCustom={'cancel'}>
              Đóng
            </MPButton>
          </Form.Item>
          <Form.Item>
            <MPButton
              htmlType='submit'
              typeCustom={'approval'}
              loading={loading}
            >
              Lưu
            </MPButton>
          </Form.Item>
        </div>
      </Form>
    </Modal>
  )
}

export const MappingForm = (item: any) => {
  return item
    .reduce((rows: any, item: any, index: number, array: any) => {
      if (!item.group) {
        rows.push([item])
      } else if (index % 2 === 0) {
        rows.push(array.slice(index, index + 2))
      }
      return rows
    }, [])
    .map((row: any, rowIndex: number) => (
      <div
        key={rowIndex}
        className={cx(
          styles['inputRow'],
          row.every((item: any) => item.hidden) ? styles['hidden'] : null
        )}
      >
        {row.map((item: any, itemIndex: number) => (
          <div key={itemIndex} className={styles['inputItem']}>
            {typeof item?.enter === 'function' ? item.enter(item) : null}
          </div>
        ))}
      </div>
    ))
}

export default Popup

import { getReplaceUTF8, MPButton } from '@medpro-libs/medpro-component-libs'
import { Select, TablePaginationConfig, Tabs } from 'antd'
import { size } from 'lodash'
import { useRef, useState } from 'react'
import Popup from './Popup'
import HomePageAnnouncement from './HomePageAnnouncement'
import styles from './styles.module.less'

const { Option } = Select
export interface Props {
  heightContent: number
  popupList: any
  hospitalList: any
  loading?: boolean
  uploadProps: any
  onPressViewDetail: (id: string) => Promise<any>
  onSubmitSearch: (value: string) => void
  onSubmitCreate: (values: any, cancelModal: any) => Promise<any>
  onSubmitUpdate: (values: any, cancelModal: any) => Promise<any>
  onSubmitDelete: (id: string) => Promise<any>
  onRefresh: () => Promise<any>
  pagination?: TablePaginationConfig
  onChangePageEvent: (value: any) => void
  onChangeSizeEvent: (value: any) => void
  methodsPopup: any
  methodsBase: any
  baseValuePopupConfig: any
}

const MPOtherManagementCard = ({
  heightContent,
  popupList,
  hospitalList,
  loading,
  uploadProps,
  onPressViewDetail,
  onSubmitSearch,
  onSubmitCreate,
  onSubmitUpdate,
  onSubmitDelete,
  onRefresh,
  pagination,
  onChangePageEvent,
  onChangeSizeEvent,
  methodsPopup,
  methodsBase,
  baseValuePopupConfig
}: Props) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const [tabActiveKey, setTabActiveKey] = useState('1')

  const items = [
    {
      key: '1',
      label: 'Quản lý popup',
      forceRender: true,
      children: null
    },
    {
      key: '2',
      label: 'Thông báo đầu trang chủ',
      forceRender: true,
      children: null
    }
  ]

  const renderTabPane = () => {
    switch (tabActiveKey) {
      case '1':
        return (
          <Popup
            headerRef={headerRef}
            heightContent={heightContent}
            popupList={popupList}
            loading={loading}
            uploadProps={uploadProps}
            onPressViewDetail={onPressViewDetail}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
            methodsPopup={methodsPopup}
            methodsBase={methodsBase}
            baseValuePopupConfig={baseValuePopupConfig}
          />
        )
      case '2':
        return (
          <HomePageAnnouncement
            headerRef={headerRef}
            heightContent={heightContent}
            hospitalList={hospitalList}
            loading={loading}
            onPressViewDetail={onPressViewDetail}
            onSubmitCreate={onSubmitCreate}
            onSubmitUpdate={onSubmitUpdate}
            onSubmitDelete={onSubmitDelete}
            onRefresh={onRefresh}
            pagination={pagination}
            onChangePageEvent={onChangePageEvent}
            onChangeSizeEvent={onChangeSizeEvent}
          />
        )

      default:
        return null
    }
  }

  return (
    <div className={styles['otherManagementPage']}>
      <div ref={headerRef} className={styles['pageHeader']}>
        <div className={styles['pageTitle']}>
          <h2>Quản lý khác</h2>
        </div>
        <div className={styles['pageActions']}>
          <MPButton typeCustom={'cancel'}>
            <span>Reset cache</span>
          </MPButton>
          <Select
            defaultValue={'medpro'}
            showSearch
            filterOption={(input, option: any) =>
              getReplaceUTF8(
                (option?.children as unknown as string).toLowerCase()
              ).includes(getReplaceUTF8(input.toLowerCase()))
            }
            options={[{ value: 'medpro', label: 'Medpro' }]}
          />
        </div>
      </div>
      <div className={styles['pageBody']}>
        <div className={styles['tabsWrapper']}>
          <Tabs
            defaultActiveKey={'1'}
            onChange={setTabActiveKey}
            tabPosition={'top'}
            items={items}
            type={'card'}
          />
        </div>
        {renderTabPane()}
      </div>
    </div>
  )
}

export default MPOtherManagementCard

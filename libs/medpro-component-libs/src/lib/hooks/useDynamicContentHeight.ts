import { useCallback, useLayoutEffect, useState } from 'react'

export function useDynamicContentHeight(
  refs: (HTMLElement | null)[],
  type?: any
) {
  const [heightContent, setHeightContent] = useState(0)
  const [heightTbBody, setHeightTbBody] = useState(0)

  const updateHeight = useCallback(() => {
    const occupiedHeight = refs.reduce((total, el) => {
      return total + (el?.getBoundingClientRect().height || 0)
    }, 0)

    const padding = 0
    let available = window.innerHeight - occupiedHeight - padding
    if (type?.key === 'table') {
      available = type.heightContent - occupiedHeight - 40
      setHeightTbBody(available - 112)
    }
    setHeightContent(available > 0 ? available : 0)
  }, [refs, type])

  useLayoutEffect(() => {
    updateHeight()
    window.addEventListener('resize', updateHeight)
    const observers = refs.filter(Boolean).map((el) => {
      const observer = new ResizeObserver(() => updateHeight())
      observer.observe(el!)
      return observer
    })

    return () => {
      window.removeEventListener('resize', updateHeight)
      observers.forEach((observer) => observer.disconnect())
    }
  }, [refs, updateHeight])

  return { heightContent, heightTbBody }
}

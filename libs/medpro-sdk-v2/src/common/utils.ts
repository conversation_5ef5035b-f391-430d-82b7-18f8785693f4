import type { AxiosRequestConfig, AxiosResponse, Method } from 'axios'
import axios from 'axios'
import type { HeadersParams } from '../interfaces'

/**
 * Remove trailing slash from given string,
 *
 * Ex:
 *  input: 'https://localhost/'
 *  output: 'https://localhost'
 *
 * @param {string} str String to convert
 *
 * @return {string} Stripped string.
 */
export const stripTrailingSlash = (str: string): string =>
  str.endsWith('/') ? str.slice(0, -1) : str

/**
 * Given a JSON object, create query string.
 *
 * Ex:
 *   input: { limit: 1, offset: 2 }
 *   output: 'limit=1&offset=2'
 *
 * @param {object} obj Key-value pairs for query string.
 *
 * @return {string} Query string.
 */
export const jsonToQueryString = (obj: Record<string, any> = {}): string =>
  Object.keys(obj)
    .map(
      (key) =>
        `${encodeURIComponent(key)}=${
          obj[key] !== null && obj[key] !== undefined
            ? encodeURIComponent(obj[key])
            : ''
        }`
    )
    .join('&')

const makeRequest = async function makeHTTPRequest(
  options: AxiosRequestConfig,
  callBack: any
): Promise<AxiosResponse> {
  const request = await axios.request(options)
  callBack(Date.now())

  return request
}

/**
 * Make a request using Basic Authorization header. Return the response as JSON.
 *
 * @param url
 * @param {string} method Method of the request. Ex: GET, POST, PATCH ...
 * @param headersCfg
 * @param data
 *
 * @return {Promise<request.ResponseAsJSON>}
 */
export const basicAuthRequest = async function basicAuthHTTPRequest(
  url: string,
  method: Method,
  headersCfg: HeadersParams,
  data?: any
): Promise<AxiosResponse> {
  const config: AxiosRequestConfig & { headers: HeadersParams } = {
    url,
    method,
    data,
    headers: {
      partnerid: '',
      appid: '',
      momoid: '',
      cskhtoken: '',
      osid: '',
      ostoken: '',
      locale: '',
      platform: '',
      version: ''
    }
  }

  if (headersCfg.partnerid) {
    config.headers.partnerid = headersCfg.partnerid
  }

  if (headersCfg.appid) {
    config.headers.appid = headersCfg.appid
  }

  if (headersCfg.momoid) {
    config.headers.momoid = headersCfg.momoid
  }

  if (headersCfg.cskhtoken) {
    config.headers.cskhtoken = headersCfg.cskhtoken
  }

  if (headersCfg.osid) {
    config.headers.osid = headersCfg.osid
  }

  if (headersCfg.ostoken) {
    config.headers.ostoken = headersCfg.ostoken
  }

  if (headersCfg.platform) {
    config.headers.platform = headersCfg.platform
  }

  if (headersCfg.version) {
    config.headers.version = headersCfg.version
  }

  if (headersCfg.token) {
    config.headers.Authorization = `Bearer ${headersCfg.token}`
  }

  if (headersCfg.locale) {
    config.headers.locale = headersCfg.locale
  }

  if (headersCfg.responseType) {
    config.responseType = headersCfg?.responseType
  }

  if (headersCfg.refcode) {
    config.headers.refcode = headersCfg.refcode
  }

  if (headersCfg.type) {
    config.headers.type = headersCfg.type
  }

  if (headersCfg.file) {
    config.headers['Content-Type'] = 'multipart/form-data'
  }

  const startTime = Date.now()

  return makeRequest(config, (a: any) => {
    if (
      headersCfg.timeOut &&
      Math.abs(a - startTime) > headersCfg.timeOut &&
      headersCfg.onTimeOut
    )
      headersCfg.onTimeOut({
        ...config,
        timeResponse: Math.abs(a - startTime)
      })
  })
}

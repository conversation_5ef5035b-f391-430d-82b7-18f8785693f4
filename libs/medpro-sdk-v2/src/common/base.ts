import type { ClientOptions } from '../interfaces'
import { API_ROOT } from './constants'
import { jsonToQueryString, stripTrailingSlash } from './utils'

export class Base {
  private defaultClientOptions = {
    apiRoot: API_ROOT,
    apiV3Root: API_ROOT,
    partnerid: '',
    appid: '',
    osid: '',
    ostoken: '',
    locale: '',
    token: '',
    cskhtoken: '',
    platform: '',
    version: ''
  }

  protected options: ClientOptions

  constructor(options?: ClientOptions) {
    this.options = { ...this.defaultClientOptions, ...(options || {}) }
    this.options.apiRoot = stripTrailingSlash(this.options.apiRoot || '')
    this.options.apiV3Root = stripTrailingSlash(this.options.apiV3Root || '')
  }

  api(path: string, param?: any): string {
    const query = param ? `?${jsonToQueryString(param)}` : ''

    return `${this.options.apiRoot}/${path}${query}`
  }

  apiV3(path: string, param?: any): string {
    const query = param ? `?${jsonToQueryString(param)}` : ''

    return `${this.options.apiV3Root}/${path}${query}`
  }
}

import { API_ROOT } from './common/constants'
import type { ClientOptions } from './interfaces'
import type { IStorage } from './modules/storage/storage'
import { Storage } from './modules/storage/storage'
import type { IMedproId } from './modules/medpro-id/medpro-id'
import { MedproId } from './modules/medpro-id/medpro-id'
import type { IHospital } from './modules/hospital/hospital'
import { Hospital } from './modules/hospital/hospital'
import type { ISite } from './modules/site/site'
import { Site } from './modules/site/site'
import type { IUser } from './modules/user/user'
import { User } from './modules/user/user'
import type { IRole } from './modules/role/role'
import { Role } from './modules/role/role'
import type { IPermission } from './modules/permission/permission'
import { Permission } from './modules/permission/permission'
import type { IModule } from './modules/module/module'
import { Module } from './modules/module/module'
import type { IEvaluatePartner } from './modules/evaluate/evaluates'
import { EvaluatePartner } from './modules/evaluate/evaluates'
import type { IBookingGuide } from './modules/booking-guide/booking-guide'
import { BookingGuide } from './modules/booking-guide/booking-guide'
import type { IFeature } from './modules/feature/feature'
import { Feature } from './modules/feature/feature'
import type { IAnchor } from './modules/anchor/anchor'
import { Anchor } from './modules/anchor/anchor'
import type { ITotal } from './modules/total/total'
import { Total } from './modules/total/total'
import type { IBanner } from './modules/banner/banner'
import { Banner } from './modules/banner/banner'
import type { IReference } from './modules/reference/reference'
import { Reference } from './modules/reference/reference'
import type { IService } from './modules/service/service'
import { Service } from './modules/service/service'
import type { IBannerBlog } from './modules/banner-blog/bannerBlog'
import { BannerBlog } from './modules/banner-blog/bannerBlog'
import type { IListing } from './modules/listing/listing'
import { Listing } from './modules/listing/listing'
import type { IHomepageModule } from './modules/homepage-module/HomePageModule'
import { HomePageModule } from './modules/homepage-module/HomePageModule'

export class Client {
  options: ClientOptions
  medproId!: IMedproId
  storage!: IStorage
  partner!: IHospital
  evaluatePartner!: IEvaluatePartner
  site!: ISite
  user!: IUser
  role!: IRole
  permission!: IPermission
  module!: IModule
  bookingGuide!: IBookingGuide
  feature!: IFeature
  anchor!: IAnchor
  hospital!: IHospital
  total!: ITotal
  banner!: IBanner
  reference!: IReference
  service!: IService
  homepageModule!: IHomepageModule
  bannerBlog!: IBannerBlog
  listing!: IListing

  constructor(options?: ClientOptions) {
    this.options = {
      ...{
        apiRoot: API_ROOT,
        apiV3Root: API_ROOT,
        partnerid: '',
        appid: '',
        osid: '',
        ostoken: '',
        evaluatePartnerId: '',
        locale: '',
        token: '',
        cskhtoken: '',
        platform: '',
        version: '',
        refcode: ''
      },
      ...(options || {})
    }
    this.init()
  }

  init() {
    this.medproId = new MedproId(this.options)
    this.storage = new Storage(this.options)
    this.partner = new Hospital(this.options)
    this.evaluatePartner = new EvaluatePartner(this.options)
    this.site = new Site(this.options)
    this.user = new User(this.options)
    this.role = new Role(this.options)
    this.permission = new Permission(this.options)
    this.module = new Module(this.options)
    this.bookingGuide = new BookingGuide(this.options)
    this.feature = new Feature(this.options)
    this.anchor = new Anchor(this.options)
    this.hospital = new Hospital(this.options)
    this.total = new Total(this.options)
    this.banner = new Banner(this.options)
    this.bannerBlog = new BannerBlog(this.options)
    this.reference = new Reference(this.options)
    this.service = new Service(this.options)
    this.listing = new Listing(this.options)
    this.homepageModule = new HomePageModule(this.options)
  }

  setDomain(url: string) {
    this.options.apiRoot = url
    this.init()
  }

  setToken(token: string) {
    this.options.token = token
    this.init()
  }

  setPartner(partnerid: string) {
    this.options.partnerid = partnerid
    this.init()
  }

  setEvaluatePartner(evaluatePartnerId: string) {
    this.options.evaluatePartnerId = evaluatePartnerId
    this.init()
  }

  setAppId(appid: string) {
    this.options.appid = appid
    this.init()
  }

  setCskhToken(cskhtoken: string) {
    this.options.cskhtoken = cskhtoken
    this.init()
  }

  setOneSignal(osid: string, ostoken: string) {
    this.options.osid = osid
    this.options.ostoken = ostoken
    this.init()
  }

  setLocale(locale: string) {
    this.options.locale = locale
    this.init()
  }

  setVersion(version: string) {
    this.options.version = version
    this.init()
  }

  setPlatform(platform: string) {
    this.options.platform = platform
    this.init()
  }

  setRefCode(refCode: string) {
    this.options.refcode = refCode
    this.init()
  }

  getOptions(): ClientOptions {
    return this.options
  }
}

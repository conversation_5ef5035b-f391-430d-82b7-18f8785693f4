import type { AxiosResponse } from 'axios'
import {
  ClientOptions,
  HeadersParams,
  ICreateModuleQuery,
  IDeleteModuleQuery,
  IModuleInfoQuery,
  IUpdateModulePermissionQuery,
  IUpdateModuleQuery
} from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_MODULE,
  DELETE_MODULE,
  GET_ALL_MODULE,
  GET_MODULE_INFO,
  GET_MODULE_MENU_ASSIGNABLE,
  UPDATE_MODULE,
  UPDATE_MODULE_PERMISSION
} from './constants'
import { Method } from '../../common/constants'

export interface IModule {
  getModuleList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  getModuleInfo(
    data: IModuleInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getModuleMenuAssignable(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  create(
    data: ICreateModuleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  delete(
    data: IDeleteModuleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  update(
    data: IUpdateModuleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updateModulePermission(
    data: IUpdateModulePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class Module extends Base implements IModule {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getModuleList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(`${GET_ALL_MODULE}`, data), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getModuleInfo(
    data: IModuleInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_MODULE_INFO}/${data.id}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getModuleMenuAssignable(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_MODULE_MENU_ASSIGNABLE}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  create(
    data: ICreateModuleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_MODULE}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  delete(
    data: IDeleteModuleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_MODULE}/${data.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }

  update(
    data: IUpdateModuleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_MODULE}/${data.id}`),
      Method.PUT,
      { ...this.options, ...headers },
      data
    )
  }

  updateModulePermission(
    data: IUpdateModulePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_MODULE_PERMISSION}`),
      Method.PUT,
      { ...this.options, ...headers },
      data
    )
  }
}

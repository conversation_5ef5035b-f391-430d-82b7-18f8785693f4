import type { AxiosResponse } from 'axios'
import {
  ClientOptions,
  HeadersParams,
  ICreatePermissionQuery,
  IDeletePermissionQuery,
  IUpdatePermissionQuery
} from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_PERMISSION,
  DELETE_PERMISSION,
  GET_ALL_PERMISSION,
  UPDATE_PERMISSION
} from './constants'
import { Method } from '../../common/constants'

export interface IPermission {
  getPermissionList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  create(
    data: ICreatePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  delete(
    data: IDeletePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  update(
    data: IUpdatePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class Permission extends Base implements IPermission {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getPermissionList(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_PERMISSION}`, data),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  create(
    data: ICreatePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_PERMISSION}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  delete(
    data: IDeletePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_PERMISSION}/${data.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }

  update(
    data: IUpdatePermissionQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_PERMISSION}/${data.id}`),
      Method.PUT,
      { ...this.options, ...headers },
      data
    )
  }
}

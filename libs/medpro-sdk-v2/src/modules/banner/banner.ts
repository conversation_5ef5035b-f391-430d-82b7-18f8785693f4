import type { AxiosResponse } from 'axios'
import { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_HOME_BANNER,
  DELETE_HOME_BANNER,
  GET_HOME_BANNER,
  SORT_ORDER_HOME_BANNER,
  UPDATE_HOME_BANNER
} from './constants'
import { Method } from '../../common/constants'

export interface IBanner {
  getHomeBanner(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  sortOrderHomeBanner(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  create(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  delete(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Banner extends Base implements IBanner {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getHomeBanner(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_HOME_BANNER}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  sortOrderHomeBanner(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${SORT_ORDER_HOME_BANNER}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  create(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_HOME_BANNER}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_HOME_BANNER}/${data.id}`),
      Method.PATCH,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  delete(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_HOME_BANNER}/${data.type}/${data.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }
}

import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '../../interfaces'
import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_POPUP,
  DELETE_POPUP,
  GET_POPUP_LIST,
  UPDATE_POPUP
} from './constants'
import { IBasePopupBody, IPopupListQuery } from './interfaces'

export interface ITotal {
  getPopupList(
    params?: IPopupListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  createPopup(
    body: IBasePopupBody,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updatePopup(
    body: IBasePopupBody,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  deletePopup(id: string, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Total extends Base implements ITotal {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getPopupList(
    params?: IPopupListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_POPUP_LIST}/${params?.partnerId}`, { tab: params?.tab }),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  createPopup(body: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(CREATE_POPUP),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  updatePopup(body: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_POPUP),
      Method.PUT,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  deletePopup(_id: string, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(`${DELETE_POPUP}/${_id}`), Method.DELETE, {
      ...this.options,
      ...headers
    })
  }
}

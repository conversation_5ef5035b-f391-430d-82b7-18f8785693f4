export interface IPopupListQuery {
  partnerId: string
  tab?: string
}

export interface IBasePopupCTA {
  text: string
  action: string
  treeId?: string
  featureType?: string
  partnerId?: string
  doctorId?: string
  subjectId?: string
  serviceId?: string
  link?: string
}

export interface IBasePopupCTAPC {
  link: string
  text: string
  target: string
}

export interface IBasePopupBody {
  status: number
  partnerId: string
  platform: string
  startDate: string
  endDate: string
  imageUrl: string
  imagePcUrl: string
  cta: IBasePopupCTA[]
  ctaPC: IBasePopupCTAPC[]
}

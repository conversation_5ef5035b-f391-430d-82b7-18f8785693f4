export interface IHospitalInfoQuery {
  partnerId: string
}

export interface IHospitalDescriptionInfoQuery {
  partnerId: string
}

export interface IFaqQuery {
  partnerId: string
}

export interface IBaseFaqQuery {
  locale: string
  question: string
  answer: string
  status: number
  sortOrder: number
  partnerId: string
  isDeleted: boolean
}

export type IFaqCreateQuery = IBaseFaqQuery

export type IFaqUpdateQuery = IBaseFaqQuery

export interface IFaqDeleteQuery {
  _id: string
}

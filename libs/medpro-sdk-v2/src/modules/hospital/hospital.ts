import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '../../interfaces'
import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import {
  IFaqCreateQuery,
  IFaqDeleteQuery,
  IFaqQuery,
  IFaqUpdateQuery,
  IHospitalDescriptionInfoQuery,
  IHospitalInfoQuery
} from './interfaces'
import {
  ADD_SPECIALTY_LIST,
  DELETE_SPECIALTY_LIST,
  GET_ALL_HOSPITAL,
  GET_FAQ,
  GET_HOSPITAL_DESCRIPTION_INFO,
  GET_HOSPITAL_INFO,
  GET_SPECIALTY_LIST,
  PRICE_LIST,
  UPDATE_HOSPITAL,
  UPDATE_HOSPITAL_DESCRIPTION,
  UPDATE_SPECIALTY_LIST
} from './constants'

export interface IHospital {
  getHospitalList(data?: any, headers?: HeadersParams): Promise<AxiosResponse>

  // Get chuyên khoa
  getSpecialtyList(data?: any, headers?: HeadersParams): Promise<AxiosResponse>

  createSpecialty(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  updateSpecialty(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  deleteSpecialty(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  getHospitalInfo(
    data: IHospitalInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getHospitalDescriptionInfo(
    data: IHospitalDescriptionInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  createPriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  updatePriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  deletePriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  getPriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  updateDescription(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  getFaq(data: IFaqQuery, headers?: HeadersParams): Promise<AxiosResponse>

  createFaq(
    data: IFaqCreateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updateFaq(
    data: IFaqCreateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  deleteFaq(
    data: IFaqDeleteQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class Hospital extends Base implements IHospital {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getFaq(data: IFaqQuery, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(GET_FAQ, data), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  createFaq(
    data: IFaqCreateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_FAQ),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  updateFaq(
    data: IFaqUpdateQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(GET_FAQ),
      Method.PUT,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  deleteFaq(
    data: IFaqDeleteQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_FAQ}/${data._id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getHospitalList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_HOSPITAL}`, data),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  // Get chuyên khoa
  getSpecialtyList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_SPECIALTY_LIST}/${data?.hospitalId}`),
      Method.GET,
      { ...this.options, ...headers }
    )
  }

  // Create chuyên khoa
  createSpecialty(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${ADD_SPECIALTY_LIST}/${data?.hospitalId}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  // Update chuyên khoa
  updateSpecialty(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_SPECIALTY_LIST}/${data?.hospitalId}`),
      Method.PATCH,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  // Delete chuyên khoa
  deleteSpecialty(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_SPECIALTY_LIST}/${data?.hospitalId}/${data?.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getHospitalInfo(
    data: IHospitalInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_HOSPITAL_INFO}/${data.partnerId}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getHospitalDescriptionInfo(
    data: IHospitalDescriptionInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_HOSPITAL_DESCRIPTION_INFO}`, data),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_HOSPITAL}`),
      Method.PUT,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  updateDescription(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_HOSPITAL_DESCRIPTION}/${data?.id}`),
      Method.PATCH,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  createPriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${PRICE_LIST}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  getPriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(`${PRICE_LIST}`, data), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  updatePriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${PRICE_LIST}/${data.id}`),
      Method.PATCH,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  deletePriceList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${PRICE_LIST}/${data?.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }
}

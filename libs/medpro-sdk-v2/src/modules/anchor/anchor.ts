import type { AxiosResponse } from 'axios'
import { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import { GET_PARTNER_ANCHOR_INFO, UPDATE_PARTNER_ANCHOR } from './constants'
import { Method } from '../../common/constants'

export interface IAnchor {
  getPartnerAnchorInfo(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Anchor extends Base implements IAnchor {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getPartnerAnchorInfo(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_PARTNER_ANCHOR_INFO}/${data.partnerId}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_PARTNER_ANCHOR}/${data?.partnerId}`),
      Method.PATCH,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }
}

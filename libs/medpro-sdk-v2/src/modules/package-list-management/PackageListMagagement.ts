import type { AxiosResponse } from 'axios'
import { ClientOptions, HeadersParams } from '../../interfaces'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import { GET_PACKAGE_LIST } from './constants'
import { Method } from '../../common/constants'

export interface IPackageListManagement {
  getPackageList(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class PackageListManagement
  extends Base
  implements IPackageListManagement
{
  constructor(options?: ClientOptions) {
    super(options)
  }

  getPackageList(query: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_PACKAGE_LIST}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      query
    )
  }
}

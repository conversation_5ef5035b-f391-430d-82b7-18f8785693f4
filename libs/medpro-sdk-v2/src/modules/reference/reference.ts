import type { AxiosResponse } from 'axios'
import { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  GET_DOCTOR_REFERENCE_LIST,
  GET_SERVICE_REFERENCE_LIST,
  GET_SUBJECT_REFERENCE_LIST
} from './constants'
import { Method } from '../../common/constants'

export interface IReference {
  getSubjectList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  getServiceList(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  getDoctorList(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Reference extends Base implements IReference {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getSubjectList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_SUBJECT_REFERENCE_LIST}`, data),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getServiceList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_SERVICE_REFERENCE_LIST}`, data),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getDoctorList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_DOCTOR_REFERENCE_LIST}`, data),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }
}

/* eslint-disable @nx/enforce-module-boundaries */
import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_CATEGORY_LISTING,
  DELETE_CATEGORY_LISTING,
  GET_ALL_CATEGORY_LISTING,
  GET_ALL_LISTING_TAB,
  SORT_ORDER_CATEGORY_LISTING,
  UPDATE_CATEGORY_LISTING
} from './constants'

export interface IListing {
  getListingTabInfo(data?: any, headers?: HeadersParams): Promise<AxiosResponse>

  getCategoryListingList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  sortOrderCategoryListing(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  create(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  delete(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Listing extends Base implements IListing {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getListingTabInfo(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_LISTING_TAB}`, data),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getCategoryListingList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_CATEGORY_LISTING}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  sortOrderCategoryListing(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${SORT_ORDER_CATEGORY_LISTING}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  create(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_CATEGORY_LISTING}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_CATEGORY_LISTING}/${data.id}`),
      Method.PATCH,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  delete(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_CATEGORY_LISTING}/${data.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }
}

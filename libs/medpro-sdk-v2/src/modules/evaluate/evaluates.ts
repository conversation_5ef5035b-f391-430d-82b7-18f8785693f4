import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import { IEvaluatePartnerInfoQuery } from './interfaces'
import { GET_ALL_EVALUATE_HOSPITAL } from './constants'

export interface IEvaluatePartner {
  getEvaluatePartnerList(
    query?: IEvaluatePartnerInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class EvaluatePartner extends Base implements IEvaluatePartner {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getEvaluatePartnerList(
    query?: IEvaluatePartnerInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_EVALUATE_HOSPITAL}`, query),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }
}

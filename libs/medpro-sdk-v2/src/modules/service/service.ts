import type { AxiosResponse } from 'axios'
import { ClientOptions, HeadersParams } from '../../interfaces'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import { GET_DOCTOR_LIST, GET_PACKAGE_LIST } from './constants'
import { Method } from '../../common/constants'
import { IDoctorListQuery, IPackageListQuery } from './interfaces'

export interface IService {
  getPackageList(
    params: IPackageListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getDoctorList(
    params: IDoctorListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class Service extends Base implements IService {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getPackageList(
    params: IPackageListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(`${GET_PACKAGE_LIST}`), Method.GET, {
      ...this.options,
      ...headers,
      partnerid: params?.partnerId,
    })
  }

  getDoctorList(
    params: IDoctorListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(`${GET_DOCTOR_LIST}`), Method.GET, {
      partnerid: params?.partnerId,
      ...this.options,
      ...headers
    })
  }
}

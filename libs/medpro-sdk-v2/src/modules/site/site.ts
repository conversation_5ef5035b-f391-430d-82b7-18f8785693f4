import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import { GET_ALL_CITY, GET_ALL_DISTRICT, GET_ALL_WARD } from './constants'
import type { ICityQuery, IDistrictQuery, IWardQuery } from './interfaces'

export interface ISite {
  getCityList(data: ICityQuery, headers?: HeadersParams): Promise<AxiosResponse>

  getDistrictList(
    data: IDistrictQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getWardList(data: IWardQuery, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Site extends Base implements ISite {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getCityList(
    data: ICityQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.apiV3(GET_ALL_CITY, data), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getDistrictList(
    data: IDistrictQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.apiV3(GET_ALL_DISTRICT, data), Method.GET, {
      ...this.options,
      ...headers
    })
  }

  getWardList(
    data: IWardQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(this.apiV3(GET_ALL_WARD, data), Method.GET, {
      ...this.options,
      ...headers
    })
  }
}

import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '@medpro-sdk-v2';
import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import { UPLOAD_FILE } from './constants'

export interface IStorage {
  uploadFile(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Storage extends Base implements IStorage {
  constructor(options?: ClientOptions) {
    super(options)
  }

  public uploadFile(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPLOAD_FILE),
      Method.POST,
      {
        ...this.options,
        ...headers,
        file: true
      },
      data
    )
  }
}

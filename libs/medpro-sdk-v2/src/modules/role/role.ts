import type { AxiosResponse } from 'axios'
import {
  ClientOptions,
  HeadersParams,
  IUpdateRolePartnerQuery,
  IDeleteRoleQuery,
  ICreateRoleQuery,
  IRoleInfoQuery
} from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_ROLE,
  DELETE_ROLE,
  GET_ALL_ROLE,
  GET_ALL_ROLE_PARTNER,
  GET_ALL_ROLE_PERMISSION,
  GET_ROLE_INFO,
  UPDATE_ROLE_PARTNER
} from './constants'
import { Method } from '../../common/constants'

export interface IRole {
  getRoleList(data?: any, headers?: HeadersParams): Promise<AxiosResponse>

  getRolePartnerList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getRolePermissionList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getRoleInfo(
    data: IRoleInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  updateRolePartner(
    data: IUpdateRolePartnerQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  create(
    data: ICreateRoleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  delete(
    data: IDeleteRoleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class Role extends Base implements IRole {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getRoleList(data?: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_ROLE}`),
      Method.GET,
      { ...this.options, ...headers },
      data
    )
  }

  getRolePartnerList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_ROLE_PARTNER}`),
      Method.GET,
      { ...this.options, ...headers },
      data
    )
  }

  getRolePermissionList(
    data?: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_ROLE_PERMISSION}/${data?.id}`),
      Method.GET,
      { ...this.options, ...headers },
      data
    )
  }

  getRoleInfo(
    data: IRoleInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ROLE_INFO}/${data.id}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  updateRolePartner(
    data: IUpdateRolePartnerQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_ROLE_PARTNER}`),
      Method.PUT,
      { ...this.options, ...headers },
      data
    )
  }

  create(
    data: ICreateRoleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_ROLE}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  delete(
    data: IDeleteRoleQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_ROLE}/${data.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }
}

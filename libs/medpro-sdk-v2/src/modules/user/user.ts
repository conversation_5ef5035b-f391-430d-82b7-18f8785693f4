import type { AxiosResponse } from 'axios'
import {
  ClientOptions,
  HeadersParams,
  ICreateUserQuery,
  IDeleteUserQuery,
  IUpdateUserQuery,
  IUserInfoQuery,
  IUserQuery
} from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_USER,
  DELETE_USER,
  GET_ALL_USER,
  GET_ALL_USER_SEARCH,
  GET_USER_INFO,
  UPDATE_USER
} from './constants'
import { Method } from '../../common/constants'

export interface IUser {
  getUserList(data: IUserQuery, headers?: HeadersParams): Promise<AxiosResponse>

  getUserInfo(
    data: IUserInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  create(
    data: ICreateUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  delete(
    data: IDeleteUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  update(
    data: IUpdateUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class User extends Base implements IUser {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getUserList(
    data: IUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${data?.isSearch ? GET_ALL_USER_SEARCH : GET_ALL_USER}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  getUserInfo(
    data: IUserInfoQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_USER_INFO}/${data.id}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  create(
    data: ICreateUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_USER}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  delete(
    data: IDeleteUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_USER}/${data.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }

  update(
    data: IUpdateUserQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_USER}/${data.id}`),
      Method.PUT,
      { ...this.options, ...headers },
      data
    )
  }
}

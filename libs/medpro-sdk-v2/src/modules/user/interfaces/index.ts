export interface IUserQuery {
  pageIndex: number
  pageSize: number
  name?: string
  isSearch?: boolean
}

export interface IUserInfoQuery {
  id: string
}

export interface ICreateUserQuery {
  password: string
  partnerId: string
  name: string
  email: string
  active: boolean
  accessSecret: string
  type: string
}

export interface IDeleteUserQuery {
  id: string
}

export interface IUpdateUserQuery {
  id: string
  name: string
  nameUser: string
  email: string
  active: boolean
}


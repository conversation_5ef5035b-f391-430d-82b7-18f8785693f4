import type { AxiosResponse } from 'axios'
import { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import { GET_HOME_BANNER_BLOG } from './constants'
import { Method } from '../../common/constants'

export interface IBannerBlog {
  getHomeBannerBlog(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class BannerBlog extends Base implements IBannerBlog {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getHomeBannerBlog(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_HOME_BANNER_BLOG}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }
}

import type { AxiosResponse } from 'axios'
import { ClientOptions, HeadersParams } from '../../interfaces'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_PACKAGE_LIST,
  GET_HOSPITAL_LIST,
  GET_PARTNER_LIST,
  CREATE_HOSPITAL_LIST,
  TELEMED_DOCTOR_IN_MONTH,
  ADD_DOCTOR_TELEMED,
  UPDATE_DOCTOR_TELEMED,
  GET_ALL_DOCTOR_TELEMED,
  SORT_ORDER_HOME_PGAE_MODULE
} from './constants'
import { Method } from '../../common/constants'
import { IGetAllDoctorTelemedParams } from './interfaces'
import {
  IBaseDoctorTelemedParams,
  IGetListDoctorTelemedParams
} from './interfaces'

export interface IHomepageModule {
  // API short table
  sortOrderHomePgaeModule(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  // TTHT và đồng hành
  getPackageList(data: any, headers?: HeadersParams): Promise<AxiosResponse>
  createPackage(data: any, headers?: HeadersParams): Promise<AxiosResponse>
  updatePackage(data: any, headers?: HeadersParams): Promise<AxiosResponse>
  deletePackage(id: string, headers?: HeadersParams): Promise<AxiosResponse>
  // Cơ sở tế yêu thích
  getHospitalList(data: any, headers?: HeadersParams): Promise<AxiosResponse>
  createHospital(data: any, headers?: HeadersParams): Promise<AxiosResponse>
  updateHospital(
    id: string,
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  deleteHospital(id: string, headers?: HeadersParams): Promise<AxiosResponse>
  getListDoctorTelemed(
    body: IGetListDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  addDoctorTelemed(
    data: IBaseDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  updateDoctorTelemed(
    body: IBaseDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  deleteDoctorTelemed(
    id: string,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
  getAllDoctorTelemed(
    params: IGetAllDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class HomePageModule extends Base implements IHomepageModule {
  constructor(options?: ClientOptions) {
    super(options)
  }
  // Short table
  sortOrderHomePgaeModule(
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${SORT_ORDER_HOME_PGAE_MODULE}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }
  // TTHT và đồng hành
  getPackageList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_PARTNER_LIST}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }
  createPackage(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_PACKAGE_LIST}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }
  updatePackage(
    id: string,
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_PARTNER_LIST}/${id}`),
      Method.PATCH,
      { ...this.options, ...headers },
      data
    )
  }
  deletePackage(id: string, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_PARTNER_LIST}/${id}`),
      Method.DELETE,
      { ...this.options, ...headers }
    )
  }
  // Cơ sở tế yêu thích
  getHospitalList(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_HOSPITAL_LIST}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }
  createHospital(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_HOSPITAL_LIST}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }
  updateHospital(
    id: string,
    data: any,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_HOSPITAL_LIST}/${id}`),
      Method.PATCH,
      { ...this.options, ...headers },
      data
    )
  }
  deleteHospital(id: string, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_HOSPITAL_LIST}/${id}`),
      Method.DELETE,
      { ...this.options, ...headers }
    )
  }
  getListDoctorTelemed(
    body: IGetListDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${TELEMED_DOCTOR_IN_MONTH}`),
      Method.POST,
      {
        ...this.options,
        ...headers
      },
      body
    )
  }

  addDoctorTelemed(
    body: IBaseDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${ADD_DOCTOR_TELEMED}`),
      Method.POST,
      { ...this.options, ...headers },
      body
    )
  }
  updateDoctorTelemed(
    body: IBaseDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_DOCTOR_TELEMED}/${body._id}`),
      Method.PATCH,
      { ...this.options, ...headers },
      body
    )
  }
  deleteDoctorTelemed(
    id: string,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${TELEMED_DOCTOR_IN_MONTH}/${id}`),
      Method.DELETE,
      { ...this.options, ...headers }
    )
  }
  getAllDoctorTelemed(
    params: IGetAllDoctorTelemedParams,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_DOCTOR_TELEMED}`, params),
      Method.GET,
      { ...this.options, ...headers }
    )
  }
}

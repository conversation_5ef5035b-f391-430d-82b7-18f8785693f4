export interface IHomepageModule {
  pageIndex: number
  pageSize: number
  search?: string
  status?: string
  type?: string
  category?: string
  sort?: string
  order?: string
}

export interface IGetListDoctorTelemedParams {
  repo: string
  tab: string
}

export interface IGetAllDoctorTelemedParams {
  treeId: string
}

export interface IBaseDoctorTelemedParams {
  repo: string
  fromDate: string
  toDate: string
  status: boolean
  doctorId: string
  display: boolean
  _id: string
}

/* eslint-disable @nx/enforce-module-boundaries */
import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { Method } from '../../common/constants'
import { basicAuthRequest } from '../../common/utils'
import { IGetListFeatureQuery } from './interfaces'
import { GET_ALL_FEATURE, GET_LIST_FEATURE, UPDATE_FEATURE } from './constants'

export interface IFeature {
  getList(
    params?: IGetListFeatureQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  getFeatureList(query?: any, headers?: HeadersParams): Promise<AxiosResponse>

  updateItem(body: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class Feature extends Base implements IFeature {
  constructor(options?: ClientOptions) {
    super(options)
  }

  updateItem(body: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(UPDATE_FEATURE),
      Method.PUT,
      {
        ...this.options,
        ...headers
      },
      body
    ).then((response: AxiosResponse) => {
      return response.data as any
    })
  }

  getList(
    params: IGetListFeatureQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.apiV3(`${GET_LIST_FEATURE}/${params.partnerId}`),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  getFeatureList(query?: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(this.api(`${GET_ALL_FEATURE}`, query), Method.GET, {
      ...this.options,
      ...headers
    })
  }
}

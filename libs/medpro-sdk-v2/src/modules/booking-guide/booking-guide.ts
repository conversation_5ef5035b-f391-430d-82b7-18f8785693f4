import type { AxiosResponse } from 'axios'
import {
  ClientOptions,
  HeadersParams,
  IBookingGuideListQuery
} from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import {
  CREATE_BOOKING_GUIDE,
  DELETE_BOOKING_GUIDE,
  GET_ALL_BOOKING_GUIDE,
  UPDATE_BOOKING_GUIDE
} from './constants'
import { Method } from '../../common/constants'

export interface IBookingGuide {
  getBookingGuideList(
    data?: IBookingGuideListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>

  create(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse>

  delete(data: any, headers?: HeadersParams): Promise<AxiosResponse>
}

export class BookingGuide extends Base implements IBookingGuide {
  constructor(options?: ClientOptions) {
    super(options)
  }

  getBookingGuideList(
    data?: IBookingGuideListQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${GET_ALL_BOOKING_GUIDE}`, data),
      Method.GET,
      {
        ...this.options,
        ...headers
      }
    )
  }

  create(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${CREATE_BOOKING_GUIDE}`),
      Method.POST,
      { ...this.options, ...headers },
      data
    )
  }

  update(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${UPDATE_BOOKING_GUIDE}`),
      Method.PUT,
      {
        ...this.options,
        ...headers
      },
      data
    )
  }

  delete(data: any, headers?: HeadersParams): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(`${DELETE_BOOKING_GUIDE}/${data.id}`),
      Method.DELETE,
      {
        ...this.options,
        ...headers
      }
    )
  }
}

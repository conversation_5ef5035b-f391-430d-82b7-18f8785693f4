import type { AxiosResponse } from 'axios'
import type { ClientOptions, HeadersParams } from '@medpro-sdk-v2'
import { Base } from '../../common/base'
import { basicAuthRequest } from '../../common/utils'
import { LOGIN_WITH_EMAIL_PASSWORD } from './constants'
import type { ILoginWithEmailPasswordQuery } from './interfaces'

export interface IMedproId {
  loginWithEmailPassword(
    data: ILoginWithEmailPasswordQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse>
}

export class MedproId extends Base implements IMedproId {
  constructor(options?: ClientOptions) {
    super(options)
  }

  loginWithEmailPassword(
    data: ILoginWithEmailPasswordQuery,
    headers?: HeadersParams
  ): Promise<AxiosResponse> {
    return basicAuthRequest(
      this.api(LOGIN_WITH_EMAIL_PASSWORD),
      'POST',
      { ...this.options, ...headers },
      data
    )
  }
}

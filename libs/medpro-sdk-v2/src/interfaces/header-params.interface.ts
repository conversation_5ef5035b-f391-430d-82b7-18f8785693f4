import { ResponseType } from 'axios'

export interface HeadersParams {
  type?: string
  token?: string
  partnerid?: string
  appid?: string
  momoid?: string
  file?: boolean
  cskhtoken?: string
  osid?: string
  ostoken?: string
  locale?: string
  platform?: string
  version?: string
  refcode?: string
  onTimeOut?: (param: any) => void
  timeOut?: number
  responseType?: ResponseType
  apiRoot?: string
  apiV3Root?: string
}

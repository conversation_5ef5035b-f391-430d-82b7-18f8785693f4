import { useEffect, useState } from 'react'
import { Client } from '@medpro-sdk-v2'
import { showError } from '@medpro-libs/medpro-component-libs'
import clientSDK from '../config/medproSdk'

export const useClientSDK = (session: any) => {
  const [client, setClient] = useState<Client>()
  useEffect(() => {
    const initClient = async () => {
      try {
        const sdk = await clientSDK(session)
        setClient(sdk)
      } catch (error: any) {
        console.error('Error initializing client SDK: ', error)
        showError(error)
      }
    }
    void initClient()
  }, [session])

  return { client }
}

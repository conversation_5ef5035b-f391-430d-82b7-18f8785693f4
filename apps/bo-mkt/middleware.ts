import { NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { PageRoutes } from './shared/routes'

export async function middleware(req: any) {
  const token = await getToken({ req, secret: 'secret' })

  // If no token, redirect to login
  if (!token) {
    return NextResponse.redirect(new URL(PageRoutes.login.path, req.url))
  }

  // Allow to continue if user authenticated
  return NextResponse.next()
}

// Apply middleware to routes that need protection
export const config = {
  matcher: ['/dashboard/:path*']
}

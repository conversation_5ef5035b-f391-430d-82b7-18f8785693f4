import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { Client } from '@medpro-sdk-v2'
import { showError } from '@medpro-libs/medpro-component-libs'
import clientSDK from '../../config/medproSdk'
import { Hospital } from './interface'
import { hospitalActions } from './slice'
import { totalActions } from '../total/slice'
import { siteActions } from '../site/slice'

function* getHospitalSaga(action: any): any {
  const loadingKey = 'getHospitalInfo'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    yield put(totalActions.loadingTrue({ key: loadingKey }))
    const { partnerId } = action.payload
    const hospital = yield call(() =>
      client.partner.getHospitalInfo({ partnerId })
    )
    const hospitalDescription = yield call(() =>
      client.partner.getHospitalDescriptionInfo({ partnerId })
    )
    const province: Hospital = yield call(() =>
      client.site.getCityList({ country_code: '203' })
    )
    let district: Hospital | null = null
    if (hospital?.data?.city_id) {
      district = yield call(() =>
        client.site.getDistrictList({ city_id: hospital?.data?.city_id })
      )
    }
    let ward: Hospital | null = null
    if (hospital?.data?.district_id) {
      ward = yield call(() =>
        client.site.getWardList({ district_id: hospital?.data?.district_id })
      )
    }
    yield put(
      hospitalActions.setHospitalInfo({
        ...hospital?.data,
        websiteInformation: hospitalDescription?.data
      })
    )
    yield put(siteActions.setProvinces(province))
    yield put(siteActions.setDistricts(district))
    yield put(siteActions.setWards(ward))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  } catch (err: any) {
    showError(err)
    yield put(
      totalActions.loadingError({
        description: err.message,
        key: loadingKey
      })
    )
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  }
}

function* getHospitalSagaWatcher() {
  yield takeLatest(hospitalActions.getHospitalInfo, getHospitalSaga)
}

function* getPriceListSaga(action: any) {
  const loadingKey = 'getPriceList'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    yield put(totalActions.loadingTrue({ key: loadingKey }))
    const { hospitalId } = action.payload
    const { data } = yield call(() =>
      client.partner.getPriceList({ hospitalId })
    )
    yield put(hospitalActions.setPriceList(data))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  } catch (err: any) {
    showError(err)
    yield put(
      totalActions.loadingError({
        description: err.message,
        key: loadingKey
      })
    )
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  }
}

function* getPriceListSagaWatcher() {
  yield takeLatest(hospitalActions.getPriceList, getPriceListSaga)
}

function* getHospitalListSaga(action?: any) {
  const loadingKey = 'getHospitalList'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    yield put(totalActions.loadingTrue({ key: loadingKey }))
    const { data } = yield call(() => client.partner.getHospitalList())
    const hospitals = data.map((h: any) => {
      return { ...h, textSearch: h.name + h.partnerId }
    })
    yield put(hospitalActions.setHospitalList(hospitals))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  } catch (err: any) {
    showError(err)
    yield put(
      totalActions.loadingError({
        description: err.message,
        key: loadingKey
      })
    )
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  }
}

function* getHospitalListSagaWatcher() {
  yield takeLatest(hospitalActions.getHospitalList, getHospitalListSaga)
}

function* getFAQList(action: any) {
  const loadingKey = 'getFAQList'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    const { partnerId } = action.payload
    yield put(totalActions.loadingTrue({ key: loadingKey }))
    const { data } = yield call(() =>
      client.hospital.getFaq({
        partnerId
      })
    )
    yield put(hospitalActions.setFAQList(data))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  } catch (err) {
    yield put(hospitalActions.setFAQList([]))
    showError(err)
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  }
}

function* FAQListdWatcher() {
  yield takeLatest(hospitalActions.getFAQList, getFAQList)
}

const hospitalSaga = function* root() {
  yield all([
    fork(getHospitalSagaWatcher),
    fork(getHospitalListSagaWatcher),
    fork(getPriceListSagaWatcher),
    fork(getHospitalListSagaWatcher),
    fork(getPriceListSagaWatcher),
    fork(FAQListdWatcher)
  ])
}

export default hospitalSaga

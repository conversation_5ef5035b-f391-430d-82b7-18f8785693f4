import { createAction, createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { TotalState } from './interface'

const initialState: TotalState = {
  loading: {
    key: '',
    description: '',
    status: false
  }
}

interface LoadingPayload {
  key: string
  description?: string
}

createAction<any>('persist/REHYDRATE')
export const totalSlice = createSlice({
  name: 'total',
  initialState,
  reducers: {
    loadingError: (state, action: PayloadAction<LoadingPayload>) => {
      state.loading = {
        ...action.payload,
        status: false,
        description: action.payload.description || 'Lỗi hệ thống'
      }
    },
    loadingTrue: (state, action: PayloadAction<LoadingPayload>) => {
      state.loading = {
        ...action.payload,
        status: true,
        description: action.payload.description || 'Vui lòng đợi...'
      }
    },
    loadingFalse: (state, action: PayloadAction<LoadingPayload>) => {
      state.loading = {
        ...action.payload,
        status: false,
        description: ''
      }
    }
  }
})

export const totalActions = totalSlice.actions
export const totalReducer = totalSlice.reducer

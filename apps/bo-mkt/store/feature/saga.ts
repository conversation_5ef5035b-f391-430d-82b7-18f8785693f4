/* eslint-disable @typescript-eslint/no-explicit-any */
import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { Client } from '@medpro-sdk-v2'
import clientSDK from '../../config/medproSdk'
import { featureActions } from './slice'
import { totalActions } from '../total/slice'
import { showError } from '@medpro-libs/medpro-component-libs'
import { message } from 'antd'

// FEATURE

function* getListFeature(action: any): Generator<any, void, any> {
  const loadingKey = 'getListFeature'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    yield put(
      totalActions.loadingTrue({
        key: loadingKey,
        description: 'Vui lòng đợi...'
      })
    )
    const response = yield call(() =>
      client.feature.getList({
        partnerId: action.payload.partnerId
      })
    )
    yield put(featureActions.setListFeature(response.data.features))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  } catch (err: any) {
    showError(err)
    yield put(
      totalActions.loadingError({ description: err.message, key: loadingKey })
    )
  }
}

function* getListFeatureWatcher() {
  yield takeLatest(featureActions.getListFeature, getListFeature)
}

function* updateFeature(action: any) {
  console.log('action', action)
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    console.log('action.payload :>> ', action.payload)
    yield call(() => client.feature.updateItem(action.payload))

    yield put(
      featureActions.getListFeature({
        partnerId: action.payload.partnerId
      })
    )
    message.success('Cập nhật feature thành công!')
  } catch (err: any) {
    showError(err)
  }
}

function* updateFeatureWatcher() {
  yield takeLatest(featureActions.updateFeature, updateFeature)
}

// function* addFeature(action: any) {
//   try {
//     const { data } = yield call(() =>
//       boClient.feature.createItem(action.payload)
//     )

//     yield put(
//       featureActions.getListFeature({
//         partnerId: action.payload.partnerId
//       })
//     )
//     message.success('Thêm Feature thành công!')
//   } catch (err) {
//     console.info(err)
//   }
// }

// function* addFeatureWatcher() {
//   yield takeLatest(featureActions.addFeature, addFeature)
// }

// function* deleteFeature(action: any) {
//   try {
//     const { data } = yield call(() =>
//       boClient.feature.deleteItem(action.payload)
//     )
//     console.info('okk')

//     yield put(
//       featureActions.getListFeature({
//         partnerId: action.payload.partnerId
//       })
//     )
//     message.warning('Xóa feature thành công!')
//   } catch (err) {
//     console.info(err)
//   }
// }

// function* deleteFeatureWatcher() {
//   yield takeLatest(featureActions.deleteFeature, deleteFeature)
// }

// function* getListFeatureForVietUserSaga(action: any) {
//   try {
//     const { data } = yield call(() =>
//       boClient.globalSetting.getListFeatureForVietUserOnly({
//         partnerId: action.payload
//       })
//     )
//     if (data) yield put(featureActions.setListFeatureForVietUser(data))
//   } catch (error) {
//     showError(error)
//   }
// }

// function* getListFeatureForVietUserWatcher() {
//   yield takeLatest(
//     featureActions.getListFeatureForVietUser,
//     getListFeatureForVietUserSaga
//   )
// }
const featureSaga = function* root() {
  yield all([
    fork(getListFeatureWatcher),
    // fork(addFeatureWatcher),
    // fork(deleteFeatureWatcher),
    fork(updateFeatureWatcher)
    // fork(getListFeatureForVietUserWatcher)
  ])
}

export default featureSaga

export interface featureState {
  listFeatureByPartner: Item_Feature[]
  // listFeatureForVietUser?: any
}

interface Item_Feature {
  message?: string
  disabled: boolean
  children: any[]
  bookingLimit: number
  position: string
  translate: TranslateItem[]
  type: string
  name: string
  image: string
  priority: number
  status: boolean
  mobileStatus: boolean
  createdAt: string
  updatedAt: string
  mobileIcon: string
  mobileRoute: string
  webRoute: string
  slug: string
  displayIcon?: string
  id: string
}

interface TranslateItem {
  _id?: string
  locale?: string
  name?: string
  index?: number
}

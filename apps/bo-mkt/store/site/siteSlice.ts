import type { PayloadAction } from '@reduxjs/toolkit'
import { createAction, createSlice } from '@reduxjs/toolkit'
import { SiteState } from './interface'

const initialState: SiteState = {}
createAction<any>('persist/REHYDRATE')
export const siteSlice = createSlice({
  name: 'site',
  initialState,
  reducers: {
    // Redux Toolkit allows us to write "mutating" logic in reducers. It
    // doesn't actually mutate the state because it uses the Immer library,
    // which detects changes to a "draft state" and produces a brand new
    // immutable state based off those changes
    setProvinces: (state, action: PayloadAction<any>) => {
      state.provinces = action.payload
    },
    setDistricts: (state, action: PayloadAction<any>) => {
      state.districts = action.payload
    },
    setWards: (state, action: PayloadAction<any>) => {
      state.wards = action.payload
    }
  }
})

export const siteActions = siteSlice.actions
export const siteReducer = siteSlice.reducer

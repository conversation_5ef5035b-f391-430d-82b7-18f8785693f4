import type { PayloadAction } from '@reduxjs/toolkit'
import { createAction, createSlice } from '@reduxjs/toolkit'
import { RoleState } from './interface'

const initialState: RoleState = {
  list: []
}
createAction<any>('persist/REHYDRATE')
export const roleSlice = createSlice({
  name: 'role',
  initialState,
  reducers: {
    // Redux Toolkit allows us to write "mutating" logic in reducers. It
    // doesn't actually mutate the state because it uses the Immer library,
    // which detects changes to a "draft state" and produces a brand new
    // immutable state based off those changes
    getRoleList: (_state, _action?: PayloadAction<any>) => {
      // run saga
    },
    setRoleList: (state, action: PayloadAction<any[]>) => {
      state.list = action.payload
    }
  }
})

export const roleActions = roleSlice.actions
export const roleReducer = roleSlice.reducer

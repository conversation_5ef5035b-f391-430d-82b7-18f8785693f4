import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { Client } from '@medpro-sdk-v2'
import { showError } from '@medpro-libs/medpro-component-libs'
import clientSDK from '../../config/medproSdk'
import { roleActions } from './slice'
import { totalActions } from '../total/slice'

function* getRoleListSaga(action?: any) {
  const loadingKey = 'getRoleList'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    yield put(totalActions.loadingTrue({ key: loadingKey }))
    const { data } = yield call(() => client.role.getRolePartnerList({}, {
      partnerid: 'medpro'
    }))
    const roles = data.map((h: any) => {
      return { ...h, value: h._id, label: h.name }
    })
    yield put(roleActions.setRoleList(roles))
    yield put(totalActions.loadingFalse({ key: loading<PERSON>ey }))
  } catch (err: any) {
    showError(err)
    yield put(
      totalActions.loadingError({
        description: err.message,
        key: loading<PERSON>ey
      })
    )
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  }
}

function* getRoleListSagaWatcher() {
  yield takeLatest(roleActions.getRoleList, getRoleListSaga)
}

const roleSaga = function* root() {
  yield all([fork(getRoleListSagaWatcher)])
}

export default roleSaga

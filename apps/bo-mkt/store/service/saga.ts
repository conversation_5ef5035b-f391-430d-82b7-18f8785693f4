import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { Client } from '@medpro-sdk-v2'
import { showError } from '@medpro-libs/medpro-component-libs'
import clientSDK from '../../config/medproSdk'
import { serviceActions } from './slice'
import { totalActions } from '../total/slice'

function* getListPackage(action: any) {
  const loadingKey = 'getListPackage'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    yield put(totalActions.loadingTrue({ key: loadingKey }))
    const { data } = yield call(() =>
      client.service.getPackageList(action.payload?.partnerId)
    )
    yield put(serviceActions.setListPackage(data))

    yield put(totalActions.loadingFalse({ key: loadingKey }))
  } catch (err) {
    yield put(serviceActions.setListPackage([]))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  }
}

function* packageWatcher() {
  yield takeLatest(serviceActions.getListPackage, getListPackage)
}

function* getListDoctor(action: any) {
  const loadingKey = 'getListDoctor'
  try {
    const client: Client = yield call(clientSDK, action?.payload)
    yield put(totalActions.loadingTrue({ key: loadingKey }))
    const { data } = yield call(() =>
      client.service.getDoctorList(action.payload?.partnerId)
    )
    yield put(serviceActions.setListDoctor(data))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  } catch (err) {
    yield put(serviceActions.setListDoctor([]))
    yield put(totalActions.loadingFalse({ key: loadingKey }))
  }
}

function* doctorWatcher() {
  yield takeLatest(serviceActions.getListDoctor, getListDoctor)
}

const serviceSaga = function* root() {
  yield all([fork(packageWatcher), fork(doctorWatcher)])
}

export default serviceSaga

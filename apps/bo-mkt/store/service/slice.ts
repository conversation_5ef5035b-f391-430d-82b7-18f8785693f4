import type { PayloadAction } from '@reduxjs/toolkit'
import { createAction, createSlice } from '@reduxjs/toolkit'
import { serviceState } from './interface'

const initialState: serviceState = {
  listPackage: [],
  listService: [],
  listSubject: [],
  listDoctor: [],
  loading: false,
  error: null
}
createAction<any>('persist/REHYDRATE')
export const serviceSlice = createSlice({
  name: 'service',
  initialState,
  reducers: {
    // Redux Toolkit allows us to write "mutating" logic in reducers. It
    // doesn't actually mutate the state because it uses the Immer library,
    // which detects changes to a "draft state" and produces a brand new
    // immutable state based off those changes
    getListPackage: (_state, _action: PayloadAction<any>) => {
      // run saga
    },
    setListPackage: (_state, _action: PayloadAction<any[]>) => {
      _state.listPackage = _action.payload
    },
    getListDoctor: (_state, _action: PayloadAction<any>) => {
      // run saga
    },
    setListDoctor: (_state, _action: PayloadAction<any[]>) => {
      _state.listDoctor = _action.payload
    }
  }
})

export const serviceActions = serviceSlice.actions
export const serviceReducer = serviceSlice.reducer

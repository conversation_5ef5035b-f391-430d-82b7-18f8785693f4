import { fork, all } from 'redux-saga/effects'
import hospitalSaga from './hospital/saga'
import totalSaga from './total/saga'
import roleSaga from './role/saga'
import featureSaga from './feature/saga'
import serviceSaga from './service/saga'

export default function* rootSaga(): Generator {
  yield all([
    fork(hospitalSaga),
    fork(roleSaga),
    fork(totalSaga),
    fork(featureSaga),
    fork(serviceSaga)
  ])
}

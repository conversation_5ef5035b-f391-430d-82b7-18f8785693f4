import { BellOutlined } from '@ant-design/icons'
import { Bad<PERSON>, Dropdown, Card, Avatar } from 'antd'
import cx from 'classnames'
import styles from './styles.module.less'

const notifications = [
  {
    id: 1,
    name: '<PERSON> Tremble',
    time: '18:30 PM',
    message: 'Sent an amount of $210 for his Appointment',
    doctor: 'Dr. <PERSON>',
    avatar: 'https://i.pravatar.cc/40?img=1',
    read: true
  },
  {
    id: 2,
    name: '<PERSON> Tremble',
    time: '12 Min Ago',
    message: 'has booked her appointment to',
    doctor: 'Dr. <PERSON><PERSON><PERSON>',
    avatar: 'https://i.pravatar.cc/40?img=2',
    read: false
  },
  {
    id: 3,
    name: '<PERSON> Tremble',
    time: '6 Min Ago',
    message: 'Sent an amount of $210 for his Appointment',
    doctor: '',
    avatar: 'https://i.pravatar.cc/40?img=3',
    read: true
  },
  {
    id: 4,
    name: '<PERSON>',
    time: '6 Min Ago',
    message: 'Sent an amount of $210 for his Appointment',
    doctor: '',
    avatar: 'https://i.pravatar.cc/40?img=3',
    read: false
  },
  {
    id: 5,
    name: '<PERSON>ble',
    time: '6 Min Ago',
    message: 'Sent an amount of $210 for his Appointment',
    doctor: '',
    avatar: 'https://i.pravatar.cc/40?img=3',
    read: false
  },
  {
    id: 6,
    name: 'Travis Tremble',
    time: '6 Min Ago',
    message: 'Sent an amount of $210 for his Appointment',
    doctor: '',
    avatar: 'https://i.pravatar.cc/40?img=3',
    read: false
  }
]

export const BadgeNotify = () => {
  const menuItems = {
    items: [
      {
        key: 'notifications',
        label: (
          <div className={styles['dropdownMenu']}>
            <Card title='Thông báo' className={styles['card']}>
              {notifications.map((notify: any) => (
                <div
                  key={notify.id}
                  className={cx(
                    styles['notificationItem'],
                    !notify?.read ? styles['notRead'] : null
                  )}
                >
                  <Avatar size={50} shape='square' src={notify.avatar} />
                  <div className={styles['notificationContent']}>
                    <div className={styles['notificationHeader']}>
                      <span className={styles['name']}>{notify.name}</span>
                      <span className={styles['time']}>{notify.time}</span>
                    </div>
                    <div className={styles['message']}>{notify.message}</div>
                    {notify.doctor && (
                      <div className={styles['doctor']}>{notify.doctor}</div>
                    )}
                  </div>
                </div>
              ))}
            </Card>
          </div>
        )
      }
    ]
  }

  return (
    <Dropdown
      menu={menuItems}
      trigger={['click']}
      overlayClassName={styles['dropdownBadgeNotify']}
      placement={'bottom'}
    >
      <Badge dot className={styles['badgeNotify']}>
        <BellOutlined style={{ fontSize: 20 }} />
      </Badge>
    </Dropdown>
  )
}

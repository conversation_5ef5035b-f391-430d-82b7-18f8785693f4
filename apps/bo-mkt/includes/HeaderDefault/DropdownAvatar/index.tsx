import React from 'react'
import { signOut } from 'next-auth/react'
import { Dropdown, Space } from 'antd'
import type { MenuProps } from 'antd'
import { Avatar } from 'antd'
import { UserOutlined, LogoutOutlined } from '@ant-design/icons'
import { useDispatch } from 'react-redux'
// import { PageRoutes } from '../../../shared/routes'
// import { currentEnv } from '../../../config/envs'
import { hospitalActions } from '../../../store/hospital/slice'
import styles from './styles.module.less'

export const DropdownAvatar = (props: any) => {
  const dispatch = useDispatch()
  const onPressSignOut = () => {
    dispatch(hospitalActions.setHospitalSelected(null))
    return signOut()
  }

  const items: MenuProps['items'] = [
    {
      label: (
        <Space>
          <UserOutlined />
          {props.session?.userName}
        </Space>
      ),
      key: '0'
    },
    {
      type: 'divider'
    },
    {
      label: (
        <Space onClick={onPressSignOut}>
          <LogoutOutlined />
          <PERSON><PERSON>ng xuất
        </Space>
      ),
      key: '3'
    }
  ]

  return (
    <Dropdown
      menu={{
        items
      }}
      trigger={['click']}
      overlayClassName={styles['dropdownAvatar']}
    >
      <a onClick={(e) => e.preventDefault()} className={styles['avatar']}>
        <Avatar
          style={{ backgroundColor: '#87d068' }}
          icon={<UserOutlined />}
        />
      </a>
    </Dropdown>
  )
}

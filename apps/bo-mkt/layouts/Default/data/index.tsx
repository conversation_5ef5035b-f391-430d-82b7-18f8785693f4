import { GiSettingsKnobs } from 'react-icons/gi'
import { PageRoutes } from '../../../shared/routes'

export const menus = [
  {
    key: PageRoutes.dashboard.listingManagement.path,
    icon: <GiSettingsKnobs />,
    label: '<PERSON><PERSON>ản lý gói listing',
    children: [
      {
        key: PageRoutes.dashboard.listingManagement.screenPosition.path,
        label: 'Vị trí hiển thị'
      },
      {
        key: PageRoutes.dashboard.listingManagement.bannerBlog.path,
        label: 'Banner blog'
      },
      {
        key: PageRoutes.dashboard.listingManagement.homeBanner.path,
        label: 'Banner trang chủ'
      },
      {
        key: PageRoutes.dashboard.listingManagement.brandPromotion.path,
        label: 'Quảng bá thương hiệu'
      },
      {
        key: PageRoutes.dashboard.listingManagement.otherManagement.path,
        label: '<PERSON>uản lý khác'
      }
    ]
  }
]

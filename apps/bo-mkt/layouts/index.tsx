import React, { ReactElement, ReactNode } from 'react'
import { DefaultLayout } from './Default'
import { NotAuthLayout } from './NotAuth'

export const getDefaultLayout = (
  page: ReactElement,
  props?: any
): ReactNode => <DefaultLayout {...props}>{page}</DefaultLayout>

export const getNotAuthLayout = (
  page: ReactElement,
  props?: any
): ReactNode => <NotAuthLayout {...props}>{page}</NotAuthLayout>

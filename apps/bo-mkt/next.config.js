//@ts-check

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next')

// This plugin is needed until this PR is merged.
// https://github.com/vercel/next.js/pull/23185
const { withLess } = require('@nx/next/plugins/with-less')
const withTM = require('next-transpile-modules')([
  'antd',
  'rc-util',
  'rc-form',
  'validator',
  'rc-pagination',
  'rc-picker',
  'rc-input',
  'rc-tree',
  'rc-table',
  'rc-tooltip',
  'rc-collapse',
  'rc-tabs',
  'rc-resize-observer',
  'rc-trigger',
  'rc-motion',
  '@ant-design/icons-svg'
])

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  nx: {
    svgr: false // Disable SVGR if not needed
  },
  webpack: (config, { defaultLoaders }) => {
    // Add locale files from antd and rc-picker to be transpiled correctly
    config.module.rules.push({
      test: /node_modules\/(antd|rc-pagination|rc-picker|@ant-design\/icons-svg)\/es\/locale\/.*\.js$/,
      use: defaultLoaders.babel // Transpile locale and icon files with Babel
    })

    // Add general rule for handling ES Modules
    config.module.rules.push({
      test: /\.m?js$/,
      resolve: {
        fullySpecified: false // Ensures ES Modules are resolved correctly
      }
    })

    // Important: return the modified config
    return config
  },
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      { protocol: 'https', hostname: 'bo-api.medpro.com.vn' },
      {
        protocol: 'https',
        hostname: 'medpro.com.vn'
      },
      { protocol: 'https', hostname: 'cdn-pkh.longvan.net' },
      {
        protocol: 'https',
        hostname: 's3-hcm1-r1.longvan.net'
      },
      { protocol: 'https', hostname: 'portal.medpro.com.vn' },
      {
        protocol: 'https',
        hostname: 'cdn.medpro.vn'
      },
      { protocol: 'https', hostname: 'files-testing.medpro.com.vn' },
      {
        protocol: 'https',
        hostname: 'resource-testing.medpro.com.vn'
      },
      { protocol: 'https', hostname: 'bo-api-testing.medpro.com.vn' },
      {
        protocol: 'https',
        hostname: 'testing-partner.s3-hcm1-r1.longvan.net'
      }
    ]
  },
  publicRuntimeConfig: {
    modifiedDate: new Date().toISOString()
  },
  env: {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL
  }
}

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withLess,
  withTM,
  withNx
]

module.exports = composePlugins(...plugins)(nextConfig)

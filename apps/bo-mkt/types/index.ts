import { NextPage } from 'next'
import { ReactElement, ReactNode } from 'react'
import { AppProps } from 'next/app'

export type NextPageWithLayout<P = any, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement, props: any) => ReactNode
}

export type AppPropsWithLayout = AppProps<any> & {
  Component: NextPageWithLayout<any>
  appInfo?: AppInfo
  router?: any
}

export type AppInfo = {
  appId: string
  partnerId?: string
}

import App from 'next/app'
import Head from 'next/head'
import React, { useMemo } from 'react'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { persistStore } from 'redux-persist'
import { getSession, SessionProvider } from 'next-auth/react'
import { ConfigProvider } from 'antd'
import Favicon from '../components/icons/favicon'
import { wrapper } from '../store'
import { getDefaultLayout } from '../layouts'
import { AppPropsWithLayout } from '../types'
import ReduxWrapper from '../hocs/ReduxWrapper'
import '../styles/styles.less'
import '../styles/ant-override.less'

function CustomApp({ Component, router, ...rest }: AppPropsWithLayout) {
  const { store, props } = wrapper.useWrappedStore(rest)
  const { pageProps } = props
  const persistor = useMemo(() => persistStore(store), [store])
  return (
    <>
      <Head>
        <Favicon />
        <meta name='viewport' content='viewport-fit=auto' />
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <title>Medpro - Marketing Tool</title>
      </Head>
      <SessionProvider session={null}>
        <ConfigProvider>
          <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
              <AuthWrapper
                Component={Component}
                pageProps={pageProps}
                router={router}
              />
            </PersistGate>
          </Provider>
        </ConfigProvider>
      </SessionProvider>
    </>
  )
}

function AuthWrapper({ Component, pageProps, router }: AppPropsWithLayout) {
  const getLayout = Component.getLayout ?? getDefaultLayout
  const site = useMemo(() => {
    return getLayout(
      <Component {...pageProps} session={pageProps.session} router={router} />
    )
  }, [getLayout, Component, pageProps, router])

  return <ReduxWrapper>{site}</ReduxWrapper>
}

CustomApp.getInitialProps = async (appContext: any) => {
  const session = await getSession(appContext.ctx)
  const appProps = await App.getInitialProps(appContext)
  return {
    ...appProps,
    pageProps: {
      ...appProps.pageProps,
      session
    }
  }
}

export default CustomApp

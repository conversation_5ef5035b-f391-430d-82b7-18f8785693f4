import { MPBannerBlog, showError } from '@medpro-libs/medpro-component-libs'
import { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useClientSDK } from '../../../../hooks/useClientSDK'
import { useAppSelector } from '../../../../store/hooks'

const hospitalList = [
  {
    partnerId: 'xcvxcvqqweqsdadfjfj43534',
    position: 1,
    name: 'Medpro',
    displayPeriod: {
      startDate: '12:00 07/04/2024',
      endDate: '22:00 17/04/2024'
    },
    status: 'active',
    pin: true,
    image:
      'https://resource-testing.medpro.com.vn/static/images/vanhanh/web/logo.png'
  },
  {
    partnerId: 'abc123',
    position: 1,
    name: '<PERSON><PERSON><PERSON> vi<PERSON><PERSON><PERSON>',
    displayPeriod: {
      startDate: '12:00 07/04/2024',
      endDate: '22:00 17/04/2024'
    },
    status: 'active',
    pin: true,
    image:
      'https://resource-testing.medpro.com.vn/static/images/vanhanh/web/logo.png'
  },
  {
    partnerId: 'asdasdasd',
    position: 2,
    name: 'Bệnh viện Quận Bình Thạnh',
    displayPeriod: {
      startDate: '12:00 07/04/2024',
      endDate: '22:00 17/04/2024'
    },
    status: 'active',
    pin: false,
    image:
      'https://resource-testing.medpro.com.vn/static/images/vanhanh/web/logo.png'
  },
  {
    partnerId: 'asdas4123123dasd',
    position: 3,
    name: 'Bệnh viện Nhi Đồng 1',
    displayPeriod: {
      startDate: '12:00 07/04/2024',
      endDate: '22:00 17/04/2024'
    },
    status: 'inactive',
    pin: true,
    image:
      'https://resource-testing.medpro.com.vn/static/images/vanhanh/web/logo.png'
  },
  {
    partnerId: 'xcvxcvwert4323423',
    position: 4,
    name: 'Bệnh viện Nhi Đồng 1',
    displayPeriod: {
      startDate: '12:00 07/04/2024',
      endDate: '22:00 17/04/2024'
    },
    status: 'pending',
    pin: true,
    image:
      'https://resource-testing.medpro.com.vn/static/images/vanhanh/web/logo.png'
  },
  {
    partnerId: 'czxvxcv445566456345',
    position: 5,
    name: 'Bệnh viện Nhi Đồng 1',
    displayPeriod: {
      startDate: '12:00 07/04/2024',
      endDate: '22:00 17/04/2024'
    },
    status: 'pending',
    pin: true,
    image:
      'https://resource-testing.medpro.com.vn/static/images/vanhanh/web/logo.png'
  },
  {
    partnerId: 'iopiop456fterg',
    position: 6,
    name: 'Bệnh viện Da Liễu TP.Hồ Chí Minh',
    displayPeriod: {
      startDate: '12:00 07/04/2024',
      endDate: '22:00 17/04/2024'
    },
    status: 'pending',
    pin: true,
    image:
      'https://resource-testing.medpro.com.vn/static/images/vanhanh/web/logo.png'
  }
] as any

export default function BannerBlogPage(props: any) {
  const { session, heightContent } = props
  const { client } = useClientSDK(session)
  const dispatch = useDispatch()
  const loading = useAppSelector((state) => state?.total?.loading)
  // #region useState
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })
  const [homeBannerBlog, setHomeBannerBlog] = useState<any>([])
  // #region Fetch Data
  const fetchHomeBannerBlog = useCallback(async () => {
    try {
      const res = await client?.bannerBlog.getHomeBannerBlog({})
      if (res && res.data) {
        setHomeBannerBlog(res.data)
      }
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.bannerBlog])
  // #region UseEffect
  useEffect(() => {
    fetchHomeBannerBlog()
  }, [fetchHomeBannerBlog])
  // #endregion
  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    // setFetchLoading(true)
  }

  const onSubmitSearch = async (value: string) => {
    console.log(11111, 'hello-1234234234')
  }

  const onRefresh = async () => {
    console.log(11111, 'hello-12')
  }

  const onPressViewDetail = async (id: string) => {
    console.log(11111, 'hello-1')
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    console.log(11111, 'hello-3333')
  }

  const onSubmitDelete = async (id: string) => {
    console.log(11111, 'hello-5666')
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    console.log(11111, 'hello-77777')
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  return (
    <MPBannerBlog
      heightContent={heightContent}
      hospitalList={hospitalList}
      homeBannerBlog={homeBannerBlog}
      loading={fetchLoading || loading?.status}
      onPressViewDetail={onPressViewDetail}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total:
          hospitalList && hospitalList?.totalRows
            ? hospitalList?.totalRows
            : hospitalList?.length
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
    />
  )
}

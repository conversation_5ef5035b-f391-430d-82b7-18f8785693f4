import { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import {
  MPHomeBanner,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useClientSDK } from '../../../../hooks/useClientSDK'
import { useAppSelector } from '../../../../store/hooks'
import { hospitalActions } from '../../../../store/hospital/slice'
import { featureActions } from '../../../../store/feature/slice'
import { getUploadProps } from '../../../../utils/method'

export default function HomeBannerPage(props: any) {
  const { session, heightContent, environment } = props
  const { client } = useClientSDK(session)
  const dispatch = useDispatch()
  const hospitalList = useAppSelector((state) => state?.hospital?.list)
  const featurePartnerList = useAppSelector(
    (state) => state?.feature?.listFeatureByPartner
  )
  const loading = useAppSelector((state) => state?.total?.loading)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [homeBannerConfig, setHomeBannerConfig] = useState<any>(undefined)
  const [uploadProps, setUploadProps] = useState<any>(null)
  const [selectedPartner, setSelectedPartner] = useState<any>('medpro')
  const [selectedPlatform, setSelectedPlatform] = useState<any>('desktop')
  const [selectedFilterTab, setSelectedFilterTab] = useState<any>('ALL')
  const [tabActiveKey, setTabActiveKey] = useState('banners_home')
  const [doctorReferenceList, setDoctorReferenceList] = useState<any>([])
  const [serviceReferenceList, setServiceReferenceList] = useState<any>([])
  const [subjectReferenceList, setSubjectReferenceList] = useState<any>([])
  const [servicePackageList, setServicePackageList] = useState<any>([])
  const [featureList, setFeatureList] = useState<any>([])
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 1000
  })

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    // setFetchLoading(true)
  }

  const fetchHomeBannerConfig = useCallback(async () => {
    try {
      const response = await client?.banner.getHomeBanner(
        {
          repo: environment?.code,
          type: tabActiveKey,
          platform: selectedPlatform,
          tab: selectedFilterTab
        },
        {
          appid: selectedPartner
        }
      )
      if (response && response.data) {
        setHomeBannerConfig(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [
    client?.banner,
    environment?.code,
    selectedFilterTab,
    selectedPartner,
    selectedPlatform,
    tabActiveKey
  ])

  const fetchReferenceList = useCallback(
    async ({ partnerId }: any) => {
      try {
        const resDoctorList = await client?.reference.getDoctorList({
          partnerId
        })
        if (resDoctorList && resDoctorList.data) {
          setDoctorReferenceList(resDoctorList.data)
        }
        const resServiceList = await client?.reference.getServiceList({
          partnerId
        })
        if (resServiceList && resServiceList.data) {
          setServiceReferenceList(resServiceList.data)
        }
        const resSubjectList = await client?.reference.getSubjectList({
          partnerId
        })
        if (resSubjectList && resSubjectList.data) {
          setSubjectReferenceList(resSubjectList.data)
        }
        const resServicePackageList = await client?.service.getPackageList({
          partnerId
        })
        if (resServicePackageList && resServicePackageList.data) {
          setServicePackageList(resServicePackageList.data)
        }
        setFetchLoading(false)
      } catch (error: any) {
        setFetchLoading(false)
        showError(error)
      }
    },
    [client?.reference, client?.service]
  )

  const fetchFeatureList = useCallback(async () => {
    try {
      const response = await client?.feature.getList({
        partnerId: selectedPartner
      })
      if (response && response.data) {
        setFeatureList(response.data?.features)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.feature, selectedPartner])

  useEffect(() => {
    if (session) {
      getUploadProps(session).then(setUploadProps)
    }
  }, [session])

  useEffect(() => {
    setFetchLoading(true)
    void fetchFeatureList()
    void fetchHomeBannerConfig()
  }, [fetchHomeBannerConfig, fetchFeatureList])

  useEffect(() => {
    if (session) {
      dispatch(hospitalActions.getHospitalList({ ...session }))
    }
  }, [session, dispatch])

  const onSubmitSearch = async (value: string) => {
    console.log('@@@onSubmitSearch')
  }

  const onRefresh = async () => {
    setFetchLoading(true)
    await fetchHomeBannerConfig()
  }

  const onResetCache = async () => {
    console.log('@@@onResetCache')
  }

  const onPressViewDetail = async (id: string) => {
    console.log('@@@onPressViewDetail: ', id)
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      let data: any
      if (selectedPlatform === 'desktop') {
        let start, end
        if (values?.timePicker) {
          ;[start, end] = values.timePicker
        }
        data = {
          cta: {
            browser: false,
            url: values?.url,
            target: values?.target
          },
          alt: values?.alt,
          platform: selectedPlatform,
          imageUrl: Array.isArray(values?.banner)
            ? values?.banner[0]
            : values?.banner,
          type: tabActiveKey,
          display: values?.display,
          status: values?.status,
          fromDate:
            !values?.display && start?.toISOString?.()
              ? start?.toISOString?.()
              : null,
          toDate:
            !values?.display && end?.toISOString?.()
              ? end?.toISOString?.()
              : null
        }
      } else {
        let start, end
        if (values?.timePicker) {
          ;[start, end] = values.timePicker
        }
        data = {
          cta: {
            browser: values?.browser,
            url: values?.url,
            link: values?.link,
            target: values?.target,
            type: values?.type,
            action: values?.action,
            partnerId: values?.partnerId,
            subjectId: values?.subjectId,
            doctorId: values?.doctorId,
            serviceId: values?.serviceId,
            treeId: values?.treeId
          },
          platform: selectedPlatform,
          alt: values?.alt,
          imageUrl: Array.isArray(values?.banner)
            ? values?.banner[0]
            : values?.banner,
          type: tabActiveKey,
          display: values?.display,
          status: values?.status,
          fromDate:
            !values?.display && start?.toISOString?.()
              ? start?.toISOString?.()
              : null,
          toDate:
            !values?.display && end?.toISOString?.()
              ? end?.toISOString?.()
              : null
        }
      }
      data.repo = environment?.code
      await client?.banner.create({ ...data }, { appid: selectedPartner })
      openNotification('success', { message: 'Tạo mới thành công' })
      await onRefresh()
      cancelModal()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitDelete = async (id: string) => {
    setFetchLoading(true)
    try {
      await client?.banner.delete({
        id,
        type: tabActiveKey
      })
      openNotification('success', { message: 'Xóa thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      let data: any
      if (selectedPlatform === 'desktop') {
        let start, end
        if (values?.timePicker) {
          ;[start, end] = values.timePicker
        }
        data = {
          cta: {
            browser: false,
            url: values?.url,
            target: values?.target
          },
          alt: values?.alt,
          platform: selectedPlatform,
          imageUrl: Array.isArray(values?.banner)
            ? values?.banner[0]
            : values?.banner,
          type: tabActiveKey,
          display: values?.display,
          status: values?.status,
          fromDate:
            !values?.display && start?.toISOString?.()
              ? start?.toISOString?.()
              : null,
          toDate:
            !values?.display && end?.toISOString?.()
              ? end?.toISOString?.()
              : null,
          id: values?.id
        }
      } else {
        let start, end
        if (values?.timePicker) {
          ;[start, end] = values.timePicker
        }
        data = {
          cta: {
            browser: values?.browser,
            url: values?.url,
            link: values?.link,
            target: values?.target,
            type: values?.type,
            action: values?.action,
            partnerId: values?.partnerId,
            subjectId: values?.subjectId,
            doctorId: values?.doctorId,
            serviceId: values?.serviceId,
            treeId: values?.treeId
          },
          platform: selectedPlatform,
          alt: values?.alt,
          imageUrl: Array.isArray(values?.banner)
            ? values?.banner[0]
            : values?.banner,
          type: tabActiveKey,
          display: values?.display,
          status: values?.status,
          fromDate:
            !values?.display && start?.toISOString?.()
              ? start?.toISOString?.()
              : null,
          toDate:
            !values?.display && end?.toISOString?.()
              ? end?.toISOString?.()
              : null,
          id: values?.id
        }
      }
      data.repo = environment?.code
      await client?.banner.update({ ...data }, { appid: selectedPartner })
      openNotification('success', { message: 'Cập nhật thành công' })
      await onRefresh()
      cancelModal()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  const formSelectPartner = async (value: any) => {
    if (value) {
      setFetchLoading(true)
      if (value.action === 'booking') {
        dispatch(
          featureActions.getListFeature({
            ...session,
            partnerId: value?.partnerId
          })
        )
      }
      await fetchReferenceList({ partnerId: value?.partnerId })
    } else {
      dispatch(featureActions.setListFeature([]))
      setDoctorReferenceList([])
      setServiceReferenceList([])
      setSubjectReferenceList([])
    }
  }

  const onHandleChangeTab = async (key: string, level: any) => {
    if (level === 'generalTab') {
      setTabActiveKey(key)
    }
    if (level === 'platformTab') {
      setSelectedPlatform(key)
      await onRefresh()
    }
    if (level === 'filterTab') {
      setSelectedFilterTab(key)
      await onRefresh()
    }
  }

  const onDragSortTable = async (data: any) => {
    if (data.length <= 1) {
      return false
    }
    setFetchLoading(true)
    try {
      const ids = data && data.length ? data.map((item: any) => item._id) : []
      await client?.banner.sortOrderHomeBanner(
        {
          ids,
          repo: environment?.code,
          type: tabActiveKey
        },
        { appid: selectedPartner }
      )
      openNotification('success', { message: 'Thao tác thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  return (
    <MPHomeBanner
      heightContent={heightContent}
      homeBannerConfig={homeBannerConfig}
      selectedPartner={selectedPartner}
      setSelectedPartner={setSelectedPartner}
      uploadProps={uploadProps}
      hospitalList={hospitalList}
      featurePartnerList={featurePartnerList}
      featureList={featureList}
      loading={fetchLoading || loading?.status}
      onPressViewDetail={onPressViewDetail}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
      formSelectPartner={formSelectPartner}
      doctorReferenceList={doctorReferenceList}
      serviceReferenceList={serviceReferenceList}
      subjectReferenceList={subjectReferenceList}
      servicePackageList={servicePackageList}
      onHandleChangeTab={onHandleChangeTab}
      setTabActiveKey={setTabActiveKey}
      tabActiveKey={tabActiveKey}
      selectedFilterTab={selectedFilterTab}
      setSelectedFilterTab={setSelectedFilterTab}
      onDragSortTable={onDragSortTable}
      onResetCache={onResetCache}
    />
  )
}

import { useCallback, useEffect, useState } from 'react'
import {
  MPScreenPosition,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useDispatch } from 'react-redux'
import { useClientSDK } from '../../../../hooks/useClientSDK'
import { useAppSelector } from '../../../../store/hooks'
import { hospitalActions } from '../../../../store/hospital/slice'

export default function ScreenPositionPage(props: any) {
  const { session, heightContent } = props
  const { client } = useClientSDK(session)
  const dispatch = useDispatch()
  const hospitalList = useAppSelector((state) => state?.hospital?.list)
  const loading = useAppSelector((state) => state?.total?.loading)
  const [selectedPartner, setSelectedPartner] = useState<any>('medpro')
  const [tabActiveKey, setTabActiveKey] = useState('booking.date')
  const [selectedFilterTab, setSelectedFilterTab] = useState<any>('ALL')
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [listingTabInfo, setListingTabInfo] = useState<any>([])
  const [catListingList, setCatListingList] = useState<any>([])
  const [doctorReferenceList, setDoctorReferenceList] = useState<any>([])
  const [serviceReferenceList, setServiceReferenceList] = useState<any>([])
  const [subjectReferenceList, setSubjectReferenceList] = useState<any>([])
  const [servicePackageList, setServicePackageList] = useState<any>([])
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    // setFetchLoading(true)
  }

  const fetchListingTabInfo = useCallback(async () => {
    try {
      const response = await client?.listing.getListingTabInfo({})
      if (response && response.data) {
        setListingTabInfo(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.listing])

  const fetchCategoryListingList = useCallback(async () => {
    try {
      const response = await client?.listing.getCategoryListingList({
        type: tabActiveKey,
        tab: selectedFilterTab
      })
      if (response && response.data) {
        setCatListingList(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.listing, selectedFilterTab, tabActiveKey])

  const fetchReferenceList = useCallback(
    async ({ partnerId }: any) => {
      try {
        const resDoctorList = await client?.reference.getDoctorList({
          partnerId
        })
        if (resDoctorList && resDoctorList.data) {
          setDoctorReferenceList(resDoctorList.data)
        }
        const resServiceList = await client?.reference.getServiceList({
          partnerId
        })
        if (resServiceList && resServiceList.data) {
          setServiceReferenceList(resServiceList.data)
        }
        const resSubjectList = await client?.reference.getSubjectList({
          partnerId
        })
        if (resSubjectList && resSubjectList.data) {
          setSubjectReferenceList(resSubjectList.data)
        }
        const resServicePackageList = await client?.service.getPackageList({
          partnerId
        })
        if (resServicePackageList && resServicePackageList.data) {
          setServicePackageList(resServicePackageList.data)
        }
        setFetchLoading(false)
      } catch (error: any) {
        setFetchLoading(false)
        showError(error)
      }
    },
    [client?.reference, client?.service]
  )

  useEffect(() => {
    setFetchLoading(true)
    void fetchListingTabInfo()
    void fetchCategoryListingList()
  }, [fetchListingTabInfo, fetchCategoryListingList])

  useEffect(() => {
    if (session) {
      dispatch(hospitalActions.getHospitalList({ ...session }))
    }
  }, [session, dispatch])

  const onSubmitSearch = async (value: string) => {
    console.log('@@@onSubmitSearch: ', value)
  }

  const onRefresh = async () => {
    setFetchLoading(true)
    await fetchCategoryListingList()
  }

  const onPressViewDetail = async (id: string) => {
    console.log('@@@onPressViewDetail: ', id)
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      let start, end
      if (values?.timePicker) {
        ;[start, end] = values.timePicker
      }
      const data = {
        ...values,
        display: values?.display,
        status: values?.status,
        fromDate:
          !values?.display && start?.toISOString?.()
            ? start?.toISOString?.()
            : null,
        toDate:
          !values?.display && end?.toISOString?.() ? end?.toISOString?.() : null
      }
      await client?.listing.create({ ...data })
      openNotification('success', { message: 'Tạo mới thành công' })
      await onRefresh()
      cancelModal()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitDelete = async (id: string) => {
    setFetchLoading(true)
    try {
      await client?.listing.delete({
        id
      })
      openNotification('success', { message: 'Xóa thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      let start, end
      const { timePicker, ...rest } = values
      if (timePicker) {
        ;[start, end] = timePicker
      }
      const data = {
        ...rest,
        display: values?.display,
        status: values?.status,
        fromDate:
          !values?.display && start?.toISOString?.()
            ? start?.toISOString?.()
            : null,
        toDate:
          !values?.display && end?.toISOString?.() ? end?.toISOString?.() : null
      }
      await client?.listing.update({ ...data })
      openNotification('success', { message: 'Cập nhật thành công' })
      await onRefresh()
      cancelModal()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  const onHandleChangeTab = async (key: string, level: any) => {
    if (level === 'generalTab') {
      setTabActiveKey(key)
    }
    if (level === 'filterTab') {
      setSelectedFilterTab(key)
      await onRefresh()
    }
  }

  const formSelectPartner = async (value: any) => {
    if (value) {
      setFetchLoading(true)
      await fetchReferenceList({ partnerId: value?.partnerId })
    } else {
      setDoctorReferenceList([])
      setServiceReferenceList([])
      setSubjectReferenceList([])
    }
  }

  const onDragSortTable = async (data: any) => {
    if (data.length <= 1) {
      return false
    }
    setFetchLoading(true)
    try {
      const ids = data && data.length ? data.map((item: any) => item._id) : []
      await client?.listing.sortOrderCategoryListing({
        listId: ids
      })
      openNotification('success', { message: 'Thao tác thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  return (
    <MPScreenPosition
      heightContent={heightContent}
      hospitalList={hospitalList}
      catListingList={catListingList}
      loading={fetchLoading || loading?.status}
      onPressViewDetail={onPressViewDetail}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
      listingTabInfo={listingTabInfo}
      setTabActiveKey={setTabActiveKey}
      tabActiveKey={tabActiveKey}
      onHandleChangeTab={onHandleChangeTab}
      formSelectPartner={formSelectPartner}
      onDragSortTable={onDragSortTable}
      selectedPartner={selectedPartner}
      setSelectedPartner={setSelectedPartner}
    />
  )
}

import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  MPOtherManagement,
  showError,
  showMessage
} from '@medpro-libs/medpro-component-libs'
import { useClientSDK } from '../../../../hooks/useClientSDK'
import { useAppSelector } from '../../../../store/hooks'
import { getUploadProps } from '../../../../utils/method'

export default function OtherManagementPage(props: any) {
  const { session, heightContent } = props
  const { client } = useClientSDK(session)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [popupList, setPopupList] = useState<any>([])
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })
  const [uploadProps, setUploadProps] = useState<any>(null)

  const loading = useAppSelector((state) => state?.total?.loading)

  const [hospitalList, setHospitalList] = useState<any>([])
  const [packageList, setPackageList] = useState<any>([])
  const [doctorList, setDoctorList] = useState<any>([])
  const [featureList, setFeatureList] = useState<any>([])

  useEffect(() => {
    if (session) {
      getUploadProps(session).then(setUploadProps)
      methodsPopup.getPopupList({ tab: 'ALL' })
    }
  }, [session])

  const fetchPopupList = useCallback(async () => {
    try {
      await methodsPopup.getPopupList({ tab: 'ALL' })
    } catch (error) {
      console.error('Error fetching popup list:', error)
    }
  }, [client?.total])

  useEffect(() => {
    fetchPopupList()
  }, [fetchPopupList])

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    // setFetchLoading(true)
  }

  const methodsBase = {
    getHospitalList: async () => {
      try {
        const response = await client?.partner.getHospitalList({})
        setHospitalList(response?.data)
      } catch (error) {
        console.error('error', error)
      }
    },
    getFeatureList: async ({ partnerId }: any) => {
      try {
        const response = await client?.feature.getList({ partnerId })
        setFeatureList(response?.data?.features)
      } catch (error) {
        console.error('error', error)
      }
    },
    getDoctorList: async ({ partnerId }: any) => {
      try {
        const response = await client?.reference.getDoctorList({ partnerId })
        setDoctorList(response?.data)
      } catch (error) {
        console.error('error', error)
      }
    },
    getPackageList: async ({ partnerId }: any) => {
      try {
        const response = await client?.service.getPackageList({ partnerId })
        console.log('response', response)
        setPackageList(response?.data)
      } catch (error) {
        console.error('error', error)
      }
    }
  }

  const baseValuePopupConfig = useMemo(() => {
    return {
      hospitalList: hospitalList,
      packageList: packageList,
      popupList: popupList,
      doctorList: doctorList,
      featureList: featureList?.filter((item: any) => !item.disabled)
    }
  }, [hospitalList, packageList, popupList, doctorList, featureList])

  const methodsPopup = {
    getPopupList: async ({ tab }: any) => {
      setFetchLoading(true)
      try {
        const response = await client?.total.getPopupList({
          partnerId: 'medpro',
          tab
        })
        setPopupList(response?.data)

        setFetchLoading(false)
      } catch (error) {
        console.error('error', error)
        setPopupList([])

        setFetchLoading(false)
      }
    },
    createPopup: async (body: any) => {
      try {
        await client?.total.createPopup(body)
        showMessage('Thêm popup thành công', 'success')
      } catch (error) {
        showError(error)
      }
    },
    updatePopup: async (values: any) => {
      try {
        await client?.total.updatePopup(values)
        showMessage('Chỉnh sửa popup thành công', 'success')

        await methodsPopup.getPopupList({ tab: 'ALL' })
      } catch (error) {
        showError(error)
      }
    },
    deletePopup: async (_id: string) => {
      try {
        await client?.total.deletePopup(_id)
        showMessage('Xóa popup thành công', 'success')

        await methodsPopup.getPopupList({ tab: 'ALL' })
      } catch (error) {
        showError(error)
      }
    }
  }
  const onSubmitSearch = async (value: string) => {
    console.log(11111, 'hello-1234234234')
  }

  const onRefresh = async () => {
    console.log(11111, 'hello-12')
  }

  const onPressViewDetail = async (id: string) => {
    console.log(11111, 'hello-1')
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    console.log(11111, 'hello-3333')
  }

  const onSubmitDelete = async (id: string) => {
    console.log(11111, 'hello-5666')
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    console.log(11111, 'hello-77777')
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  return (
    <MPOtherManagement
      heightContent={heightContent}
      popupList={popupList}
      hospitalList={hospitalList}
      loading={fetchLoading || loading?.status}
      uploadProps={uploadProps}
      onPressViewDetail={onPressViewDetail}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total:
          popupList && popupList?.totalRows
            ? popupList?.totalRows
            : popupList?.length
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
      methodsPopup={methodsPopup}
      methodsBase={methodsBase}
      baseValuePopupConfig={baseValuePopupConfig}
    />
  )
}

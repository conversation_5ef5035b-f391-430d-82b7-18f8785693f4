import { useRouter } from 'next/router'
import { MPButton } from '@medpro-libs/medpro-component-libs'
import React from 'react'
import { PageRoutes } from '../shared/routes'
import { getNotAuthLayout } from '../layouts'

export default function Custom404() {
  const router = useRouter()
  const onBackHome = () => {
    return router.push(PageRoutes.login.path)
  }
  return (
    <div
      style={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        padding: '20px',
        backgroundColor: '#f9f9f9'
      }}
    >
      <h1 style={{ fontSize: '6rem', marginBottom: '1rem', color: '#000000' }}>
        404
      </h1>
      <p style={{ fontSize: '1.5rem', marginBottom: '1.5rem', color: '#333' }}>
        Trang bạn tìm không tồn tại hoặc đã bị di chuyển!
      </p>
      <MPButton
        style={{ minHeight: 45, fontSize: 16 }}
        onClick={onBackHome}
        typeCustom={'cancel'}
        size={'large'}
      >
        Quay về trang chủ
      </MPButton>
    </div>
  )
}

Custom404.getLayout = getNotAuthLayout

import { Button, Form, Input, notification } from 'antd'
import { size } from 'lodash'
import { getServerSession } from 'next-auth'
import { signIn } from 'next-auth/react'
import { useSearchParams } from 'next/navigation'
import { useRouter } from 'next/router'
import { getNotAuthLayout } from '../../layouts'
import { PageRoutes } from '../../shared/routes'
import { authOptions } from '../api/auth/[...nextauth]'
import styles from './styles.module.less'

const LoginPage = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams.get('callbackUrl') || '/'
  const [api, contextHolder] = notification.useNotification()

  const onFinish = async (values: any) => {
    try {
      const result = await signIn('credentials', {
        redirect: false,
        phone: values.phone,
        password: values.password,
        callbackUrl
      })
      if (result && result.ok) {
        return router.push(PageRoutes.dashboard.listingManagement.screenPosition.path)
      } else {
        api.error({
          message: 'Đăng nhập thất bại',
          description: result?.error || 'Đã có lỗi xảy ra.',
          showProgress: true,
          pauseOnHover: true
        })
      }
    } catch (e: any) {
      api.error({
        message: 'Đăng nhập thất bại',
        description: e?.message || 'Đã xảy ra lỗi. Vui lòng thử lại sau!',
        showProgress: true,
        pauseOnHover: true
      })
    }
  }

  return (
    <div className={styles['pageContainer']}>
      {contextHolder}
      <div className={styles['cardWrapper']}>
        <div>
          <div className={styles['formWrapper']}>
            <div className={styles['logo']}>
              <a href={PageRoutes.home.path}>
                <img
                  src='https://bo-api.medpro.com.vn/static/images/medpro/web/header_logo.svg'
                  alt='logo'
                />
              </a>
            </div>
            <Form layout='vertical' onFinish={onFinish}>
              <Form.Item
                label={'Tên đăng nhập'}
                name={'phone'}
                rules={[
                  { required: true, message: 'Vui lòng nhập thông tin!' }
                ]}
              >
                <Input
                  placeholder={'Nhập tên đăng nhập ...'}
                  autoComplete={'off'}
                />
              </Form.Item>
              <Form.Item
                label={'Mật khẩu'}
                name={'password'}
                rules={[
                  {
                    required: true,
                    validator: (_: any, value: any) => {
                      if (!value) {
                        return Promise.reject(
                          new Error('Vui lòng nhập mật khẩu!')
                        )
                      } else {
                        if (size(value) < 6) {
                          return Promise.reject(new Error('Mật khẩu quá ngắn!'))
                        }
                      }
                      return Promise.resolve()
                    }
                  }
                ]}
              >
                <Input.Password
                  placeholder={'Nhập mật khẩu ...'}
                  autoComplete={'off'}
                />
              </Form.Item>
              <Form.Item className={styles['btnItem']}>
                <Button type={'primary'} htmlType={'submit'} block={true}>
                  Đăng nhập
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
      </div>
    </div>
  )
}

LoginPage.getLayout = getNotAuthLayout

export async function getServerSideProps(context: any) {
  const session = await getServerSession(context.req, context.res, authOptions)
  if (session) {
    return {
      redirect: {
        destination: PageRoutes.dashboard.listingManagement.screenPosition.path,
        permanent: false
      }
    }
  }
  return {
    props: {}
  }
}

export default LoginPage

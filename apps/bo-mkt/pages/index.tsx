import { GetServerSideProps } from 'next'
import { PageRoutes } from '../shared/routes/index'

const Home = () => null

export const getServerSideProps: GetServerSideProps = async (context) => {
  // Check if the current URL is not the login page
  if (context.resolvedUrl !== PageRoutes.login.path) {
    return {
      redirect: {
        destination: PageRoutes.login.path,
        permanent: false
      }
    }
  }

  return { props: {} }
}

export default Home

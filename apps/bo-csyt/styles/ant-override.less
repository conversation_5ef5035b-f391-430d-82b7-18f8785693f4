.ant-form {
  .ant-form-item {
    @media only screen and (max-width: 768px) {
      max-width: unset !important;
    }

    &:last-child {
      margin-bottom: 0 !important;
    }

    .ant-form-item-required::before {
      visibility: hidden;
      margin-inline-end: 0 !important;
      content: '' !important;
    }

    .ant-form-item-required::after {
      content: '*' !important;
      display: inline-block !important;
      margin-inline-end: 0 !important;
      margin-inline-start: 4px !important;
      color: #ff4d4f !important;
      font-size: 14px !important;
      font-family: SimSun, sans-serif !important;
      line-height: 1 !important;
      margin-right: 5px !important;
      visibility: visible !important;
    }

    .ant-form-item-label {
      display: flex;
      height: 35px;
      font-weight: 500;
    }

    .ant-select {
      &.ant-select-single {
        height: auto;
      }

      .ant-select-selector .ant-select-selection-search-input {
        min-height: 30px;
      }

      .ant-select-selector {
        border-radius: 8px;
        min-height: 38px;
      }
    }

    .ant-input-number {
      width: 100%;
    }

    .ant-picker {
      width: 100%;

      input {
        min-height: 28px !important;
      }
    }

    .ant-form-item-control-input {
      border-radius: 8px;

      .ant-input-affix-wrapper {
        padding: 0 11px;

        .ant-input {
          padding: 4px 0;
        }
      }

      input {
        border-radius: 8px;
        min-height: 38px;
      }

      .ant-input-outlined:focus {
        border-color: #1677ff;
      }
    }
  }
}

.ant-tabs {
  .ant-tabs-nav {
    .ant-tabs-nav-wrap {
      .ant-tabs-nav-list {
        .ant-tabs-tab-active {
          font-weight: 500;
        }
      }
    }
  }
}

.ant-collapse {
  border: none;
  background-color: transparent;

  .ant-collapse-item {
    border-bottom: none;
    border-radius: 8px;
    background-color: aliceblue;
    margin-bottom: 10px;

    &:first-child {
      border-radius: 8px;
    }

    &:last-child {
      border-radius: 8px;
    }

    .ant-collapse-header {
      .ant-collapse-header-text {
        font-weight: 500;
      }
    }

    .ant-collapse-content {
      border: none;
    }
  }
}

.ant-table {
  box-shadow: 0 0 10px #b7c0ce33;
  border-radius: 0 !important;
  //scrollbar-color: unset !important;

  .ant-table-thead > tr > th {
    border-bottom: none;
    color: #656565;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .ant-table-tbody > tr > td {
    border-bottom: none;
    border-top: 1px solid #f0f0f0;
    padding: 12px 15px;

    &:first-child {
      font-weight: 600;
      color: #1b5a90;
    }
  }

  .ant-table-container table {
    border-radius: 0;
  }

  .ant-table-container table > thead > tr:first-child > *:first-child {
    border-start-start-radius: 0;
  }

  .ant-table-container table > thead > tr:first-child > *:last-child {
    border-start-end-radius: 0;
  }

  .ant-table-thead {
    tr {
      th {
        background: #f6f6f6;
        text-align: center;
      }
    }
  }

  .ant-table-container {
    border-start-start-radius: 0;
    border-start-end-radius: 0;
    //overflow: hidden hidden !important;

    //&:hover {
    //  overflow: auto hidden !important;
    //}

    .ant-table-header {
      border-radius: 0;
    }

    .ant-table-tbody,
    .ant-table-body {
      .groupAction {
        display: flex;
        justify-content: center;
        gap: 12px;

        .btn {
          border-radius: 50%;
          flex: 1 0 auto;
          max-width: 30px;
        }

        .btnView {
          border-radius: 50%;
          padding: 5px;

          &:hover,
          &:focus,
          &:active {
            border-color: #f1c40f;

            svg {
              fill: #f1c40f;
            }
          }

          svg {
            fill: grey;
            width: 17px;
            height: 17px;
          }
        }

        .btnEdit {
          border-radius: 50%;
          padding: 5px;

          &:hover,
          &:focus,
          &:active {
            border-color: #40a9ff;

            svg {
              fill: #40a9ff;
            }
          }

          svg {
            fill: grey;
            width: 17px;
            height: 17px;
          }
        }

        .btnDelete {
          border-radius: 50%;
          padding: 5px;

          &:hover,
          &:focus,
          &:active {
            border-color: #fd766a;

            svg {
              fill: #fd766a;
            }
          }

          svg {
            fill: grey;
            width: 17px;
            height: 17px;
          }
        }
      }

      .groupActionText {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        .actionText {
          cursor: pointer;
          font-weight: 500;
          font-size: 14px;
          line-height: 16px;
          color: #007AFF;
        }
      }
    }
  }
}

.ant-table-wrapper {
  .ant-spin-nested-loading {
    height: 100%;

    .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .ant-table {
        height: 100%;
        overflow: hidden;
        border: 1px solid #f0f0f0;

        .ant-table-container {
          height: 100%;

          .ant-table-body {
            height: 100%;

            table {
              padding-bottom: 2px;
            }
          }
        }
      }
    }
  }
}

.ant-table-pagination {
  margin: 15px 0 10px 0 !important;
}

.ant-spin {
  .ant-spin-dot {
    position: fixed !important;
    inset-inline-start: unset !important;;
  }
}

.ant-modal-root {
  .ant-modal-wrap {
    padding-bottom: 10px;
    padding-top: 10px;
  }
}

import { Client } from '@medpro-sdk-v2'
import { currentEnv } from './envs'

const apiRoot = currentEnv.API_BE
const apiV3Root = currentEnv.API_V3_BE
const clientSDK = async (session: any): Promise<Client> => {
  const token = session?.access_token || ''
  const initOptions = {
    apiRoot,
    apiV3Root,
    appid: 'cskh',
    appidMedpro: 'medpro',
    platform: 'pc',
    token
  }
  return new Client(initOptions)
}
export default clientSDK

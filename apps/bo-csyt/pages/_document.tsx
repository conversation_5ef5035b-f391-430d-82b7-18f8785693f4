import Document, {
  DocumentContext,
  DocumentInitialProps,
  Head,
  Html,
  Main,
  NextScript
} from 'next/document'
import { createCache, extractStyle, StyleProvider } from '@ant-design/cssinjs'
import React from 'react'
import Favicon from '../components/icons/favicon'

class CustomDocument extends Document {
  static async getInitialProps(
    ctx: DocumentContext
  ): Promise<DocumentInitialProps> {
    const cache = createCache()
    const originalRenderPage = ctx.renderPage
    ctx.renderPage = () =>
      originalRenderPage({
        enhanceApp: (App) => (props) =>
          (
            <StyleProvider cache={cache}>
              <App {...props} />
            </StyleProvider>
          )
      })
    const initialProps = await Document.getInitialProps(ctx)
    const style = extractStyle(cache, true)
    return {
      ...initialProps,
      styles: (
        <>
          {initialProps.styles}
          <style dangerouslySetInnerHTML={{ __html: style }} />
        </>
      )
    }
  }

  render(): JSX.Element {
    return (
      <Html lang='vi'>
        <Head>
          <Favicon />
          <meta charSet='utf-8' />
          <meta content='IE=edge' />
          <meta name='theme-color' content='#00b5f1' />{' '}
          <link rel='preconnect' href='https://fonts.googleapis.com' />
          <link
            rel='preconnect'
            href='https://fonts.gstatic.com'
            crossOrigin=''
          />
        </Head>

        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    )
  }
}

export default CustomDocument

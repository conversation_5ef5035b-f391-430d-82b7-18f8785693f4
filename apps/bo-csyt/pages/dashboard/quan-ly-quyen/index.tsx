import { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import {
  MPRoleManagement,
  openNotification,
  showError,
  handlePermissionTree
} from '@medpro-libs/medpro-component-libs'
import { useClientSDK } from '../../../hooks/useClientSDK'
import { useAppSelector } from '../../../store/hooks'
import { hospitalActions } from '../../../store/hospital/slice'

export default function RoleManagementPage(props: any) {
  const { session, heightContent } = props
  const { client } = useClientSDK(session)
  const dispatch = useDispatch()
  const loading = useAppSelector((state) => state?.total?.loading)
  const hospitalList = useAppSelector((state) => state?.hospital?.list)
  const [permissionList, setPermissionList] = useState<any>([])
  const [rolePermissionList, setRolePermissionList] = useState<any>([])
  const [roleList, setRoleList] = useState<any>(undefined)
  const [roleDetail, setRoleDetail] = useState<any>(undefined)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    setFetchLoading(true)
  }

  const fetchRoleList = useCallback(async () => {
    try {
      const response = await client?.role.getRoleList({})
      if (response && response.data) {
        setRoleList(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.role])

  const fetchModuleAssignableList = useCallback(async () => {
    try {
      const response = await client?.module.getModuleMenuAssignable({})
      if (response && response.data) {
        setPermissionList(handlePermissionTree(response.data))
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.module])

  const fetchRolePermissionList = useCallback(async (id: string) => {
    try {
      const response = await client?.role.getRolePermissionList({id})
      if (response && response.data) {
        setRolePermissionList(handlePermissionTree(response.data))
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.role])

  useEffect(() => {
    setFetchLoading(true)
    dispatch(hospitalActions.getHospitalList({...session}))
    void fetchRoleList()
  }, [dispatch, fetchRoleList, session])

  const onSubmitSearch = async (value: string) => {
    if (value) {
      setRoleList(
        roleList.filter((item: any) => {
          return item.name.toLowerCase().includes(value.toLowerCase())
        })
      )
    } else {
      await onRefresh()
    }
  }

  const onRefresh = async () => {
    setFetchLoading(true)
    await fetchRoleList()
  }

  const onPressViewDetail = async (id: string) => {
    setFetchLoading(true)
    try {
      const response = await client?.role.getRoleInfo({
        id
      })
      if (response && response.data) {
        setRoleDetail(response.data)
        await fetchRolePermissionList(response.data?._id)
        await fetchModuleAssignableList()
      }

    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.role.create({
        ...values
      })
      openNotification('success', { message: 'Tạo mới thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitDelete = async (id: string) => {
    setFetchLoading(true)
    try {
      await client?.role.delete({
        id
      })
      openNotification('success', { message: 'Xóa thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.module.update({
        ...values
      })
      openNotification('success', { message: 'Cập nhật thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitTransferModule = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.module.updateModulePermission({
        ...values
      })
      openNotification('success', { message: 'Cập nhật thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  return (
    <MPRoleManagement
      heightContent={heightContent}
      roleList={roleList?.rows || roleList || []}
      hospitalList={hospitalList || []}
      permissionList={permissionList || []}
      rolePermissionList={rolePermissionList || []}
      roleDetail={roleDetail}
      loading={fetchLoading || loading?.status}
      onPressViewDetail={onPressViewDetail}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      onSubmitTransferModule={onSubmitTransferModule}
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total:
          roleList && roleList?.totalRows
            ? roleList?.totalRows
            : roleList?.length
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
    />
  )
}

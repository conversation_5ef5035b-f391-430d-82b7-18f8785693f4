import React, { useCallback, useEffect, useState } from 'react'
import {
  MPGeneralSettings,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useDispatch } from 'react-redux'
import { useRouter } from 'next/router'
import { useAppSelector } from '../../../store/hooks'
import { hospitalActions } from '../../../store/hospital/slice'
import { siteActions } from '../../../store/site/slice'
import { useClientSDK } from '../../../hooks/useClientSDK'
import { getUploadProps } from '../../../utils/method'
import { totalActions } from '../../../store/total/slice'

export default function GeneralSettingsPage({ session, heightContent }: any) {
  const { client } = useClientSDK(session)
  const router = useRouter()
  const dispatch = useDispatch()

  //#region State & Selectors
  const hospitalInfo = useAppSelector(
    (state) => state.hospital.selectedHospital
  )
  const hospitalList = useAppSelector((state) => state?.hospital?.list)
  const priceList = useAppSelector((state) => state.hospital.priceList)
  const provinceList = useAppSelector((state) => state?.site?.provinces)
  const districtList = useAppSelector((state) => state?.site?.districts)
  const wardList = useAppSelector((state) => state?.site?.wards)
  const loading = useAppSelector((state) => state?.total?.loading)
  const questionList = useAppSelector((state) => state.hospital.questionList)
  const faqCategories =
    useAppSelector((state) => state.hospital.faqCategories) || []
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [uploadProps, setUploadProps] = useState<any>(null)
  const [bookingGuideList, setBookingGuideList] = useState<any>([])
  const [partnerAnchorInfo, setPartnerAnchorInfo] = useState<any>([])
  const [specialtyList, setSpecialtyList] = useState<any>([])
  //#endregion

  //#region Effects
  useEffect(() => {
    if (session) {
      getUploadProps(session).then(setUploadProps)
      dispatch(hospitalActions.getHospitalList({ ...session }))
    }
  }, [session, dispatch])
  //#endregion

  //#region Fetch Location Data
  const fetchDistrictList = useCallback(
    async (value: any) => {
      try {
        const response = await client?.site.getDistrictList({ city_id: value })
        dispatch(siteActions.setDistricts(response))
      } catch (error: any) {
        showError(error)
      }
    },
    [client?.site, dispatch]
  )

  const fetchWardList = useCallback(
    async (value: any) => {
      try {
        const response = await client?.site.getWardList({ district_id: value })
        dispatch(siteActions.setWards(response))
      } catch (error: any) {
        showError(error)
      }
    },
    [client?.site, dispatch]
  )

  //#region Fetch Specialty List
  const fetchSpecialtyList = async (hospitalId: any) => {
    const response = await client?.hospital.getSpecialtyList({
      hospitalId
    })
    if (response && response.data) {
      setSpecialtyList(response.data)
    }
  }

  const onChangeCity = async (value: any) => {
    await fetchDistrictList(value)
  }

  const onChangeDistrict = async (value: any) => {
    await fetchWardList(value)
  }
  //#endregion

  const fetchBookingGuideList = useCallback(
    async (partnerid: any) => {
      try {
        const response = await client?.bookingGuide.getBookingGuideList(
          { pageSize: 1000, pageIndex: 0 },
          { partnerid, type: 'guide-booking' }
        )
        if (response && response.data) {
          setBookingGuideList(response.data)
        }
        setFetchLoading(false)
      } catch (error: any) {
        setFetchLoading(false)
        showError(error)
      }
    },
    [client?.bookingGuide]
  )

  const fetchPartnerAnchorInfo = useCallback(
    async (partnerId: any) => {
      try {
        const response = await client?.anchor.getPartnerAnchorInfo({
          partnerId
        })
        if (response && response.data) {
          setPartnerAnchorInfo(response.data)
        }
        setFetchLoading(false)
      } catch (error: any) {
        setFetchLoading(false)
        showError(error)
      }
    },
    [client?.anchor]
  )

  const fetchUpdatePartnerAnchorInfo = useCallback(
    async (values: any) => {
      try {
        const response = await client?.anchor.update({
          ...values
        })
        if (response && response.data) {
          setPartnerAnchorInfo(response.data)
          await fetchPartnerAnchorInfo(values?.partnerId)
        }
        setFetchLoading(false)
      } catch (error: any) {
        setFetchLoading(false)
        showError(error)
      }
    },
    [client?.anchor, fetchPartnerAnchorInfo]
  )
  //#endregion

  //#region FAQ Management
  const createFAQ = async (values: any) => {
    try {
      await client?.hospital.createFaq({
        ...values,
        partnerId: hospitalInfo?.partnerId,
        locale: 'vi'
      })
      openNotification('success', { message: 'Thêm câu hỏi thành công' })
      dispatch(
        hospitalActions.getFAQList({
          partnerId: hospitalInfo?.partnerId,
          ...session
        })
      )
    } catch (error: any) {
      showError(error)
    }
  }

  const updateFAQ = async (values: any) => {
    try {
      await client?.hospital.updateFaq({
        ...values,
        partnerId: hospitalInfo?.partnerId,
        locale: 'vi'
      })
      openNotification('success', { message: 'Chỉnh sửa câu hỏi thành công' })
      dispatch(
        hospitalActions.getFAQList({
          partnerId: hospitalInfo?.partnerId,
          ...session
        })
      )
    } catch (error: any) {
      showError(error)
    }
  }

  const deleteFAQ = async (_id: string) => {
    try {
      await client?.hospital.deleteFaq({
        _id
      })
      openNotification('success', { message: 'Xóa câu hỏi thành công' })
      dispatch(
        hospitalActions.getFAQList({
          partnerId: hospitalInfo?.partnerId,
          ...session
        })
      )
    } catch (error: any) {
      showError(error)
    }
  }
  //#endregion

  //#region Form Submissions
  const onHandleBasicFormSubmit = async (values: any) => {
    dispatch(totalActions.loadingTrue({ key: 'onFormSubmit' }))
    try {
      const { description, ...rest } = values
      await client?.partner.update({
        ...rest,
        partnerId: hospitalInfo?.partnerId,
        circleLogo: Array.isArray(rest?.circleLogo)
          ? rest?.circleLogo[0]
          : rest?.circleLogo,
        newHospitalTypes: rest.newHospitalTypes
          ? rest.newHospitalTypes.map((num: any) => Number(num))
          : []
      })
      await client?.partner.updateDescription({
        id: hospitalInfo?.websiteInformation?._id,
        description
      })
      openNotification('success', { message: 'Thành công' })
      dispatch(
        hospitalActions.getHospitalInfo({
          partnerId: hospitalInfo?.partnerId,
          ...session
        })
      )
    } catch (err: any) {
      showError(err)
    }
  }

  const onHandleMediaFormSubmit = async (values: any) => {
    dispatch(totalActions.loadingTrue({ key: 'onFormSubmit' }))
    try {
      const { images, banner } = values
      await client?.partner.updateDescription({
        id: hospitalInfo?.websiteInformation?._id,
        images,
        banner
      })
      openNotification('success', { message: 'Thành công' })
      dispatch(
        hospitalActions.getHospitalInfo({
          partnerId: hospitalInfo?.partnerId,
          ...session
        })
      )
    } catch (err: any) {
      showError(err)
    }
  }

  // Thêm mới chuyên khoa
  const onHandleActionSpecialty = async (value: any) => {
    setFetchLoading(true)
    try {
      let response
      let message = 'Thao tác chuyên khoa thành công'
      switch (value?.action) {
        case 'create':
          response = await client?.hospital.createSpecialty({
            ...value,
            cta: {},
            icon: Array.isArray(value?.icon) ? value?.icon[0] : value?.icon,
            hospitalId: hospitalInfo?._id
          })

          message = 'Thêm chuyên khoa thành công'
          break
        case 'edit':
          response = await client?.hospital.updateSpecialty({
            ...value,
            cta: {},
            icon: Array.isArray(value?.icon) ? value?.icon[0] : value?.icon,
            hospitalId: hospitalInfo?._id
          })
          message = 'Cập nhật chuyên khoa thành công'
          break
        case 'delete':
          response = await client?.hospital.deleteSpecialty({
            id: value?.id,
            hospitalId: hospitalInfo?._id
          })
          message = 'Xóa chuyên khoa thành công'
          break
        default:
          break
      }
      if (response) {
        openNotification('success', { message: message })
        await fetchSpecialtyList(hospitalInfo?._id)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onHandlePriceListAction = async (values: any) => {
    setFetchLoading(true)
    try {
      let response
      let message = 'Thao tác bảng giá thành công'
      switch (values?.action) {
        case 'create':
          response = await client?.partner.createPriceList({
            ...values,
            hospitalId: hospitalInfo?._id
          })
          message = 'Thêm bảng giá thành công'
          break
        case 'edit':
          response = await client?.partner.updatePriceList({
            ...values,
            hospitalId: hospitalInfo?._id
          })
          message = 'Cập nhật bảng giá thành công'
          break
        case 'delete':
          response = await client?.partner.deletePriceList({
            id: values?.id
          })
          message = 'Xóa bảng giá thành công'
          break
        default:
          break
      }

      if (response) {
        openNotification('success', { message: message })
        dispatch(
          hospitalActions.getPriceList({
            hospitalId: hospitalInfo?._id,
            ...session
          })
        )
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onHandleBookingGuideAction = async (values: any, cancelModal?: any) => {
    setFetchLoading(true)
    try {
      let response
      let message = ''
      switch (values?.action) {
        case 'create':
          response = await client?.bookingGuide.create(
            {
              ...values,
              partnerId: hospitalInfo?.partnerId,
              imageUrl: Array.isArray(values.imageUrl)
                ? values.imageUrl[0]
                : values.imageUrl
            },
            { partnerid: hospitalInfo?.partnerId, type: values.type }
          )
          message = 'Thêm bước thông tin quy trình thành công'
          break
        case 'update':
          response = await client?.bookingGuide.update({
            ...values,
            imageUrl: Array.isArray(values.imageUrl)
              ? values.imageUrl[0]
              : values.imageUrl
          })
          message = 'Cập nhật thông tin quy trình thành công'
          break
        case 'delete':
          response = await client?.bookingGuide.delete({
            ...values
          })
          message = 'Xóa thông tin quy trình thành công'
          break
        default:
          break
      }
      if (response) {
        if (cancelModal) {
          cancelModal()
        }
        openNotification('success', { message: message })
        await fetchBookingGuideList(hospitalInfo?.partnerId)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onHandleIntroduceAction = async (values: any) => {
    dispatch(totalActions.loadingTrue({ key: 'onFormSubmit' }))
    try {
      const { tabId, tabTitle, ...rest } = values
      await client?.partner.updateDescription({
        id: hospitalInfo?.websiteInformation?._id,
        ...rest
      })
      await fetchUpdatePartnerAnchorInfo({
        partnerId: hospitalInfo?.partnerId,
        id: tabId,
        title: tabTitle
      })
      openNotification('success', { message: 'Thành công' })
      dispatch(
        hospitalActions.getHospitalInfo({ partnerId: hospitalInfo?.partnerId, ...session })
      )
    } catch (err: any) {
      showError(err)
    }
  }

  const onHandleBenefitAction = async (values: any) => {
    dispatch(totalActions.loadingTrue({ key: 'onFormSubmit' }))
    try {
      const { tabId, tabTitle, ...rest } = values
      await client?.partner.updateDescription({
        id: hospitalInfo?.websiteInformation?._id,
        ...rest
      })
      await fetchUpdatePartnerAnchorInfo({
        partnerId: hospitalInfo?.partnerId,
        id: tabId,
        title: tabTitle
      })
      openNotification('success', { message: 'Thành công' })
      dispatch(
        hospitalActions.getHospitalInfo({ partnerId: hospitalInfo?.partnerId, ...session })
      )
    } catch (err: any) {
      showError(err)
    }
  }

  const onHandleQuestionAction = async (value: any, cancelModal?: any) => {
    setFetchLoading(true)
    try {
      let response
      let message = ''
      switch (value?.action) {
        case 'create':
          await createFAQ(value)
          message = 'Thêm câu hỏi thành công'
          break
        case 'update':
          await updateFAQ(value)
          message = 'Cập nhật câu hỏi thành công'
          break
        case 'delete':
          await deleteFAQ(value?._id)
          message = 'Xóa câu hỏi thành công'
          break
        case 'refetch':
          dispatch(
            hospitalActions.getFAQList({ partnerId: hospitalInfo?.partnerId, ...session })
          )
          message = 'Lấy câu hỏi thành công'
          break
        default:
          break
      }
      if (response) {
        if (cancelModal) {
          cancelModal()
        }
        openNotification('success', { message: message })
        await fetchBookingGuideList(hospitalInfo?.partnerId)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }
  //#endregion

  //#region Tab Managementd
  const onHandleChangeTab = async (key: string, level: any) => {
    switch (level) {
      case 'generalTab':
        switch (key) {
          case '1':
            dispatch(
              hospitalActions.getHospitalInfo({
                partnerId: hospitalInfo?.partnerId, ...session
              })
            )
            await fetchPartnerAnchorInfo(hospitalInfo?.partnerId)
            break
          case '2':
            setFetchLoading(true)
            await fetchPartnerAnchorInfo(hospitalInfo?.partnerId)
            break
          default:
            break
        }
        break
      case 'moduleAnchorTab':
        switch (key) {
          case '#chuyen-khoa':
            await fetchSpecialtyList(hospitalInfo?._id)
            break
          case '#bang-gia':
            dispatch(
              hospitalActions.getPriceList({
                hospitalId: hospitalInfo?._id,
                ...session
              })
            )
            break
          case '#huong-dan':
            setFetchLoading(true)
            await fetchBookingGuideList(hospitalInfo?.partnerId)
            break
          case '#cau-hoi':
            dispatch(
              hospitalActions.getFAQList({ partnerId: hospitalInfo?.partnerId, ...session })
            )
            break
          default:
            break
        }
        break
      default:
        break
    }
    if (level === 'moduleAnchorTab') {
      void router.replace(
        {
          pathname: router.pathname,
          query: { anchorTab: key }
        },
        undefined,
        { shallow: true }
      )
    }
  }

  const debouncedFetchUpdateTab = async (values: any) => {
    setFetchLoading(true)
    await fetchUpdatePartnerAnchorInfo({
      partnerId: hospitalInfo?.partnerId,
      id: values.tabId,
      title: values.tabTitle
    })
  }

  const onHandleChangePartner = async (value: any) => {
    dispatch(hospitalActions.getHospitalInfo({ partnerId: value, ...session }))
    dispatch(hospitalActions.setHospitalSelected({ partnerId: value }))
    await fetchPartnerAnchorInfo(value)
    await router.replace(
      {
        pathname: router.pathname,
        query: { generalTab: 1 }
      },
      undefined,
      { shallow: true }
    )
  }
  //#endregion

  return (
    <MPGeneralSettings
      heightContent={heightContent}
      session={session}
      data={hospitalInfo}
      partnerAnchorInfo={partnerAnchorInfo}
      specialtyList={specialtyList}
      hospitalList={hospitalList}
      bookingGuideList={bookingGuideList}
      provinceList={provinceList}
      districtList={districtList}
      uploadProps={uploadProps}
      wardList={wardList}
      dataQuestion={questionList}
      faqCategories={[]}
      loading={fetchLoading || loading?.status}
      priceList={priceList}
      onChangeCity={onChangeCity}
      onChangeDistrict={onChangeDistrict}
      onHandleBasicFormSubmit={onHandleBasicFormSubmit}
      onHandleMediaFormSubmit={onHandleMediaFormSubmit}
      onHandleChangePartner={onHandleChangePartner}
      onHandlePriceListAction={onHandlePriceListAction}
      onHandleBookingGuideAction={onHandleBookingGuideAction}
      onHandleBenefitAction={onHandleBenefitAction}
      onHandleIntroduceAction={onHandleIntroduceAction}
      onHandleChangeTab={onHandleChangeTab}
      debouncedFetchUpdateTab={debouncedFetchUpdateTab}
      onHandleQuestionAction={onHandleQuestionAction}
      onHandleActionSpecialty={onHandleActionSpecialty}
    />
  )
}

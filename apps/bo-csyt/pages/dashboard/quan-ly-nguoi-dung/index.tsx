import { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import {
  MPUserManagement,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useClientSDK } from '../../../hooks/useClientSDK'
import { hospitalActions } from '../../../store/hospital/slice'
import { roleActions } from '../../../store/role/slice'
import { useAppSelector } from '../../../store/hooks'

export default function UserManagementPage(props: any) {
  const { session, heightContent } = props
  const { client } = useClientSDK(session)
  const dispatch = useDispatch()
  const loading = useAppSelector((state) => state?.total?.loading)
  const hospitalList = useAppSelector((state) => state?.hospital?.list)
  const roleList = useAppSelector((state) => state?.role?.list)
  const [userList, setUserList] = useState<any>(undefined)
  const [userDetail, setUserDetail] = useState<any>(undefined)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>({
    isSearch: false,
    pageIndex: 0,
    pageSize: 10
  })

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    setFetchLoading(true)
  }

  const fetchUserList = useCallback(async () => {
    try {
      const response = await client?.user.getUserList({
        ...filter
      })
      if (response && response.data) {
        setUserList(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.user, filter])

  useEffect(() => {
    setFetchLoading(true)
    dispatch(hospitalActions.getHospitalList({ ...session }))
    dispatch(roleActions.getRoleList({ ...session }))
    void fetchUserList()
  }, [dispatch, fetchUserList, session])

  const onSubmitSearch = (value: string) => {
    if (value && value.trim()) {
      handleFilter({
        isSearch: true,
        name: value
      })
    } else {
      handleFilter({
        isSearch: false,
        name: undefined
      })
    }
  }

  const onRefresh = async () => {
    setFetchLoading(true)
    await fetchUserList()
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.user.create({
        ...values,
        accessSecret: 'boofficepkh',
        type: 'local',
        active: !!values.active
      })
      openNotification('success', { message: 'Tạo mới thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitDelete = async (id: string) => {
    setFetchLoading(true)
    try {
      await client?.user.delete({
        id
      })
      openNotification('success', { message: 'Xóa thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.user.update({
        ...values,
        nameUser: values.name,
        active: !!values.active
      })
      await client?.role.updateRolePartner({
        userId: values?.id,
        roleIds: values?.roles || []
      })
      openNotification('success', { message: 'Cập nhật thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onPressDetail = async (id: string) => {
    setFetchLoading(true)
    try {
      const response = await client?.user.getUserInfo({
        id
      })
      if (response && response.data) {
        setUserDetail(response.data)
      }
      setFetchLoading(false)
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  return (
    <MPUserManagement
      heightContent={heightContent}
      userList={userList?.rows || userList || []}
      userDetail={userDetail}
      loading={fetchLoading || loading?.status}
      hospitalList={hospitalList}
      roleList={roleList}
      onPressDetail={onPressDetail}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total: userList?.totalRows || 0
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
    />
  )
}

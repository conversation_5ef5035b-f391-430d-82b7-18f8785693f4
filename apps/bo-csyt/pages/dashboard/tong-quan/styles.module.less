.pageWrapper {
  .sectionOne {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    max-height: 450px;

    @media only screen and (max-width: 1200px) {
      flex-direction: column;
      max-height: unset;
      margin-bottom: 10px;
    }

    .left {
      width: 33.33333333%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      @media only screen and (max-width: 1200px) {
        width: 100%;
      }

      .widgetBox {
        border-radius: 10px;
        padding: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0;
        width: 100%;
        background: #efefef73;
        box-shadow: 3px 5px 5px 3px rgb(227 227 227);

        @media only screen and (max-width: 1200px) {
          margin-bottom: 10px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .contentInfo {
          h6 {
            color: #012047;
            font-size: 16px;
            font-weight: normal;
          }

          h4 {
            color: #012047;
            font-weight: 600;
            margin: 5px 0;
            font-size: 24px;
          }

          .textSuccess, .textDanger {
            display: flex;
            gap: 2px;
          }

          .textSuccess {
            color: #04BD6C;
          }

          .textDanger {
            color: red;
          }
        }

        .icon {
          width: 75px;
          height: 75px;
          border-radius: 10px;
          background: #F9F9F9;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 40px;
        }
      }
    }

    .right {
      width: 66.66666667%;

      @media only screen and (max-width: 1200px) {
        width: 100%;
      }

      .cardHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .viewMore {
          cursor: pointer;
          color: #0E82FD;
          text-decoration: underline;
          font-weight: 500;
          font-size: 14px;
        }
      }

      .card {
        height: 100%;
        overflow: hidden;

        :global {
          .ant-card-head {
            position: sticky;
            top: 0;
            z-index: 9;
            background: #fff;
          }
        }

        &:hover {
          overflow: auto;
        }

        .contentItems {
          .item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px;
            cursor: pointer;

            @media only screen and (max-width: 991px) {
              flex-direction: column;
              align-items: flex-start;
            }

            &:hover {
              background: #d2e0ed;
              border-radius: 10px;
            }

            &:last-child {
              margin-bottom: 0;
            }

            .infoProfile {
              display: flex;
              align-items: center;
              gap: 5px;
              min-width: 250px;

              .nameInfo {
                .bookingNumber {
                  color: #0E82FD;
                  font-size: 12px;
                  font-weight: 500;
                  cursor: pointer;
                }

                h5 {
                  font-size: 16px;
                  font-weight: bold;
                }
              }
            }

            .infoAppointment {
              h6 {
                font-size: 14px;
                color: #012047;
              }

              .infoOther {
                display: flex;
                flex-flow: wrap;
                gap: 6px;

                :global {
                  .ant-tag {
                    margin-inline-end: 0;
                    letter-spacing: 0.5px;
                    border-radius: 4px;
                    border: none;
                    font-size: 10px;
                  }
                }

                .specialty {
                  background: #0E82FD;
                  color: #ffffff;
                }

                .canceled {
                  background: red;
                  color: #ffffff;
                }

                .paid {
                  background: green;
                  color: #ffffff;
                }

                .doctor {
                  background: lightseagreen;
                  color: #ffffff;
                }

                .paymentAt {
                  background: orange;
                  color: #ffffff;
                }
              }
            }
          }
        }
      }
    }
  }

  .sectionTwo {
    display: flex;
    gap: 10px;
    margin-bottom: 0;
    max-height: 430px;

    @media only screen and (max-width: 1200px) {
      flex-direction: column;
      max-height: unset;
      margin-bottom: 10px;
    }

    .left {
      width: 41.66666667%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      @media only screen and (max-width: 1200px) {
        width: 100%;
      }

      .chartWrapper {
        border: 1px solid #E6E8EE;
        padding: 0;
        border-radius: 10px;

        .chartDate {
          color: #012047;
          font-size: 20px;
          font-weight: 600;
          padding: 12px 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          span {
            font-size: 16px;
          }
        }

        .chartBody {
          padding: 0 20px 20px 20px;
        }
      }
    }

    .right {
      width: 58.33333333%;

      @media only screen and (max-width: 1200px) {
        width: 100%;
      }

      .cardHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .viewMore {
          cursor: pointer;
          color: #0E82FD;
          text-decoration: underline;
          font-weight: 500;
          font-size: 14px;
        }
      }

      .card {
        height: 100%;
        overflow: hidden;

        :global {
          .ant-card-head {
            position: sticky;
            top: 0;
            z-index: 9;
            background: #fff;
          }
        }

        &:hover {
          overflow: auto;
        }

        .contentItems {
          .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-bottom: 10px;
            padding: 8px;
            gap: 8px;
            cursor: pointer;

            &:last-child {
              margin-bottom: 0;
            }

            &:hover {
              background: #0e82fd;
              border-radius: 10px;
              color: #ffffff;

              .infoProfile {
                .nameInfo {
                  .patientNumber {
                    color: #ffffff;
                  }
                }
              }

              .infoPatient {
                h6 {
                  color: #ffffff;
                }
              }
            }

            @media only screen and (max-width: 768px) {
              flex-direction: column;
              align-items: flex-start;
            }

            .infoProfile {
              display: flex;
              align-items: center;
              gap: 5px;
              min-width: 200px;
              width: 100%;

              .nameInfo {
                .patientNumber {
                  color: #0E82FD;
                  font-size: 12px;
                  font-weight: 500;
                  cursor: pointer;
                }

                h5 {
                  font-size: 16px;
                  font-weight: bold;
                }
              }
            }

            .infoPatient {
              h6 {
                font-size: 14px;
                color: #012047;
              }
            }
          }
        }
      }
    }
  }
}

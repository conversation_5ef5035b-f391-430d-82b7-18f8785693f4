import {
  FaUserInjured,
  <PERSON>a<PERSON><PERSON><PERSON>p,
  FaArrowDown,
  FaUser<PERSON>lock,
  FaCalendarDays
} from 'react-icons/fa6'
import { Avatar, Card, Tabs, Tag } from 'antd'
import { Bar } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend
} from 'chart.js'
import styles from './styles.module.less'

ChartJS.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend)

export default function OverviewPage() {
  const dataAppointment = {
    labels: ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'CN'],
    datasets: [
      {
        label: 'Cuộc hẹn',
        data: [30, 50, 40, 60, 80, 70, 60],
        backgroundColor: '#249ffb',
        borderColor: '#249ffb',
        borderWidth: 1,
        borderRadius: 10
      }
    ]
  }

  const dataPatient = {
    labels: ['Thứ 2', '<PERSON>h<PERSON> 3', '<PERSON><PERSON><PERSON> 4', '<PERSON><PERSON><PERSON> 5', '<PERSON><PERSON><PERSON> 6', 'Thứ 7', 'CN'],
    datasets: [
      {
        label: 'Bệnh nhân',
        data: [35, 55, 40, 60, 75, 65, 80],
        backgroundColor: '#ffa500',
        borderColor: '#ffa500',
        borderWidth: 1,
        borderRadius: 10
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 1000,
      easing: 'easeOutQuad'
    },
    plugins: {
      legend: {
        position: 'bottom',
        display: false
      },
      tooltip: {
        enabled: true
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 20
        }
      }
    }
  } as any

  return (
    <div className={styles['pageWrapper']}>
      <div className={styles['sectionOne']}>
        <div className={styles['left']}>
          <div className={styles['widgetBox']}>
            <div className={styles['contentInfo']}>
              <h6>Tổng bệnh nhân</h6>
              <h4>978</h4>
              <div className={styles['textSuccess']}>
                <FaArrowUp /> <span>15% Tuần trước</span>
              </div>
            </div>
            <div className={styles['icon']}>
              <FaUserInjured />
            </div>
          </div>
          <div className={styles['widgetBox']}>
            <div className={styles['contentInfo']}>
              <h6>Bệnh nhân hôm nay</h6>
              <h4>80</h4>
              <div className={styles['textDanger']}>
                <FaArrowDown /> <span>15% Hôm qua</span>
              </div>
            </div>
            <div className={styles['icon']}>
              <FaUserClock />
            </div>
          </div>
          <div className={styles['widgetBox']}>
            <div className={styles['contentInfo']}>
              <h6>Đặt lịch hôm nay</h6>
              <h4>50</h4>
              <div className={styles['textSuccess']}>
                <FaArrowUp /> <span>20% Hôm qua</span>
              </div>
            </div>
            <div className={styles['icon']}>
              <FaCalendarDays />
            </div>
          </div>
        </div>
        <div className={styles['right']}>
          <Card
            title={
              <div className={styles['cardHeader']}>
                <div className={styles['label']}>Danh sách đặt lịch</div>
                <div className={styles['viewMore']}>Xem thêm</div>
              </div>
            }
            className={styles['card']}
          >
            <div className={styles['contentItems']}>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile01.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['bookingNumber']}>#Apt0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoAppointment']}>
                  <h6>10:30 17/04/2025</h6>
                  <div className={styles['infoOther']}>
                    <Tag className={styles['specialty']}>Bệnh lý cột sống</Tag>
                    <Tag className={styles['canceled']}>Đã hủy</Tag>
                    <Tag className={styles['doctor']}>Dr Edalin Hendry</Tag>
                  </div>
                </div>
              </div>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile01.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['bookingNumber']}>#Apt0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoAppointment']}>
                  <h6>10:30 17/04/2025</h6>
                  <div className={styles['infoOther']}>
                    <Tag className={styles['specialty']}>
                      Chấn thương chỉnh hình
                    </Tag>
                    <Tag className={styles['paid']}>Đã thanh toán</Tag>
                    <Tag className={styles['doctor']}>Dr Edalin Hendry</Tag>
                  </div>
                </div>
              </div>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile04.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['bookingNumber']}>#Apt0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoAppointment']}>
                  <h6>10:30 17/04/2025</h6>
                  <div className={styles['infoOther']}>
                    <Tag className={styles['specialty']}>
                      Chấn thương chỉnh hình
                    </Tag>
                    <Tag className={styles['paymentAt']}>
                      Thanh toán tại cơ sở
                    </Tag>
                    <Tag className={styles['doctor']}>Dr Edalin Hendry</Tag>
                  </div>
                </div>
              </div>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile04.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['bookingNumber']}>#Apt0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoAppointment']}>
                  <h6>10:30 17/04/2025</h6>
                  <div className={styles['infoOther']}>
                    <Tag className={styles['specialty']}>
                      Chấn thương chỉnh hình
                    </Tag>
                    <Tag className={styles['paymentAt']}>
                      Thanh toán tại cơ sở
                    </Tag>
                    <Tag className={styles['doctor']}>Dr Edalin Hendry</Tag>
                  </div>
                </div>
              </div>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile04.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['bookingNumber']}>#Apt0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoAppointment']}>
                  <h6>10:30 17/04/2025</h6>
                  <div className={styles['infoOther']}>
                    <Tag className={styles['specialty']}>
                      Chấn thương chỉnh hình
                    </Tag>
                    <Tag className={styles['paymentAt']}>
                      Thanh toán tại cơ sở
                    </Tag>
                    <Tag className={styles['doctor']}>Dr Edalin Hendry</Tag>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
      <div className={styles['sectionTwo']}>
        <div className={styles['left']}>
          <div className={styles['chartWrapper']}>
            <div className={styles['chartDate']}>
              Tuần <span>17/03 - 23/03</span>
            </div>
            <div className={styles['chartBody']}>
              <Tabs
                defaultActiveKey='1'
                items={[
                  {
                    label: `Các cuộc hẹn`,
                    key: '1',
                    children: (
                      <div style={{ width: '100%', minHeight: '300px' }}>
                        <Bar data={dataAppointment} options={options} />
                      </div>
                    )
                  },
                  {
                    label: `Bệnh nhân`,
                    key: '2',
                    children: (
                      <div style={{ width: '100%', minHeight: '300px' }}>
                        <Bar data={dataPatient} options={options} />
                      </div>
                    )
                  }
                ]}
              />
            </div>
          </div>
        </div>
        <div className={styles['right']}>
          <Card
            title={
              <div className={styles['cardHeader']}>
                <div className={styles['label']}>Bệnh nhân mới</div>
                <div className={styles['viewMore']}>Xem thêm</div>
              </div>
            }
            className={styles['card']}
          >
            <div className={styles['contentItems']}>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile01.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['patientNumber']}>#Patient0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Email</h6>
                  <div><EMAIL></div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Điện thoại</h6>
                  <div>**********</div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Ngày sinh</h6>
                  <div>14/02/1978</div>
                </div>
              </div>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile01.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['patientNumber']}>#Patient0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Email</h6>
                  <div><EMAIL></div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Điện thoại</h6>
                  <div>**********</div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Ngày sinh</h6>
                  <div>14/02/1978</div>
                </div>
              </div>
              <div className={styles['item']}>
                <div className={styles['infoProfile']}>
                  <div className={styles['avatar']}>
                    <Avatar
                      size={50}
                      shape='square'
                      src={'/images/profile01.jpg'}
                    />
                  </div>
                  <div className={styles['nameInfo']}>
                    <div className={styles['patientNumber']}>#Patient0004</div>
                    <h5>Catherine Griffin</h5>
                  </div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Email</h6>
                  <div><EMAIL></div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Điện thoại</h6>
                  <div>**********</div>
                </div>
                <div className={styles['infoPatient']}>
                  <h6>Ngày sinh</h6>
                  <div>14/02/1978</div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}

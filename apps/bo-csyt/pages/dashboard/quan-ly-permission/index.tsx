import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  MPPermissionManagement,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useClientSDK } from '../../../hooks/useClientSDK'
import { useAppSelector } from '../../../store/hooks'

export default function PermissionManagementPage(props: any) {
  const { session, heightContent } = props
  const { client } = useClientSDK(session)
  const loading = useAppSelector((state) => state?.total?.loading)
  const [permissionList, setPermissionList] = useState<any>(undefined)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    setFetchLoading(true)
  }

  const fetchPermissionList = useCallback(async () => {
    try {
      const response = await client?.permission.getPermissionList({
        ...filter
      })
      if (response && response.data) {
        setPermissionList(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.permission, filter])

  useEffect(() => {
    setFetchLoading(true)
    void fetchPermissionList()
  }, [fetchPermissionList])

  const onSubmitSearch = async (value: string) => {
    if (value) {
      setPermissionList(
        permissionList.filter((item: any) => item.name.includes(`${value}_`))
      )
    } else {
      await onRefresh()
    }
  }

  const onRefresh = async () => {
    setFetchLoading(true)
    await fetchPermissionList()
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.permission.create({
        ...values
      })
      openNotification('success', { message: 'Tạo mới thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitDelete = async (id: string) => {
    setFetchLoading(true)
    try {
      await client?.permission.delete({
        id
      })
      openNotification('success', { message: 'Xóa thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.permission.update({
        ...values
      })
      openNotification('success', { message: 'Cập nhật thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  const permissionListFilter = useMemo(
    () => [
      ...new Set(
        permissionList
          ? permissionList
              .filter((item: any) => item?.name.includes('_'))
              .map((item: any) => item?.name.split('_')[0])
          : []
      )
    ],
    [permissionList]
  )

  return (
    <MPPermissionManagement
      heightContent={heightContent}
      permissionList={permissionList?.rows || permissionList || []}
      permissionListFilter={permissionListFilter || []}
      loading={fetchLoading || loading?.status}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total:
          permissionList && permissionList?.totalRows
            ? permissionList?.totalRows
            : permissionList?.length
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
    />
  )
}

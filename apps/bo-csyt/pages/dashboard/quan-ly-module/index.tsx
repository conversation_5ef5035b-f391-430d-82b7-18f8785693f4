import { useCallback, useEffect, useState } from 'react'
import {
  MPModuleManagement,
  openNotification,
  showError
} from '@medpro-libs/medpro-component-libs'
import { useClientSDK } from '../../../hooks/useClientSDK'
import { useAppSelector } from '../../../store/hooks'

export default function ModuleManagementPage(props: any) {
  const { session, heightContent } = props
  const { client } = useClientSDK(session)
  const loading = useAppSelector((state) => state?.total?.loading)
  const [moduleList, setModuleList] = useState<any>(undefined)
  const [permissionList, setPermissionList] = useState<any>(undefined)
  const [moduleDetail, setModuleDetail] = useState<any>(undefined)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<any>({
    pageIndex: 0,
    pageSize: 10
  })

  const handleFilter = (newFilter: any) => {
    setFilter((prevFilter: any) => {
      return {
        ...prevFilter,
        ...newFilter
      }
    })
    setFetchLoading(true)
  }

  const fetchModuleList = useCallback(async () => {
    try {
      const response = await client?.module.getModuleList({
        ...filter
      })
      if (response && response.data) {
        setModuleList(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.module, filter])

  const fetchPermissionList = useCallback(async () => {
    try {
      const response = await client?.permission.getPermissionList({})
      if (response && response.data) {
        setPermissionList(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client?.permission])

  useEffect(() => {
    setFetchLoading(true)
    void fetchModuleList()
  }, [fetchModuleList])

  const onSubmitSearch = async (value: string) => {
    if (value) {
      setModuleList(
        moduleList.filter((item: any) => {
          return item.name.toLowerCase().includes(value.toLowerCase())
        })
      )
    } else {
      await onRefresh()
    }
  }

  const onRefresh = async () => {
    setFetchLoading(true)
    await fetchModuleList()
  }

  const onPressViewDetail = async (id: string) => {
    setFetchLoading(true)
    try {
      const response = await client?.module.getModuleInfo({
        id
      })
      if (response && response.data) {
        setModuleDetail(response.data)
      }
      await fetchPermissionList()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitCreate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.module.create({
        ...values
      })
      openNotification('success', { message: 'Tạo mới thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitDelete = async (id: string) => {
    setFetchLoading(true)
    try {
      await client?.module.delete({
        id
      })
      openNotification('success', { message: 'Xóa thành công' })
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitUpdate = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.module.update({
        ...values
      })
      openNotification('success', { message: 'Cập nhật thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onSubmitTransferModule = async (values: any, cancelModal: any) => {
    setFetchLoading(true)
    try {
      await client?.module.updateModulePermission({
        ...values
      })
      openNotification('success', { message: 'Cập nhật thành công' })
      cancelModal()
      await onRefresh()
    } catch (error) {
      setFetchLoading(false)
      showError(error)
    }
  }

  const onChangePageEvent = (value: any) => {
    handleFilter({
      pageIndex: value - 1
    })
  }

  const onChangeSizeEvent = (value: any) => {
    handleFilter({
      pageSize: value,
      pageIndex: 0
    })
  }

  return (
    <MPModuleManagement
      heightContent={heightContent}
      moduleList={moduleList?.rows || moduleList || []}
      permissionList={permissionList || []}
      moduleDetail={moduleDetail}
      loading={fetchLoading || loading?.status}
      onPressViewDetail={onPressViewDetail}
      onSubmitSearch={onSubmitSearch}
      onSubmitCreate={onSubmitCreate}
      onSubmitUpdate={onSubmitUpdate}
      onSubmitDelete={onSubmitDelete}
      onRefresh={onRefresh}
      onSubmitTransferModule={onSubmitTransferModule}
      pagination={{
        current: filter.pageIndex > 0 ? filter.pageIndex + 1 : 1,
        pageSize: filter.pageSize,
        total:
          moduleList && moduleList?.totalRows
            ? moduleList?.totalRows
            : moduleList?.length
      }}
      onChangePageEvent={onChangePageEvent}
      onChangeSizeEvent={onChangeSizeEvent}
    />
  )
}

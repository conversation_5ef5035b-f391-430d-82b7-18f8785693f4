/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react'
import { MPFeatureSettings } from '@medpro-libs/medpro-component-libs'
import { useDispatch } from 'react-redux'
import { getUploadProps } from '../../../utils/method'
import { useAppSelector } from '../../../store/hooks'
import { featureActions } from '../../../store/feature/slice'

export default function FeatureSettingsPage({ session }: any) {
  const dispatch = useDispatch()
  const [uploadProps, setUploadProps] = useState<any>(null)
  const hospitalInfo = useAppSelector(
    (state) => state.hospital.selectedHospital
  )
  const listFeatureByPartner = useAppSelector(
    (state) => state?.feature?.listFeatureByPartner
  )
  const loading = useAppSelector((state) => state?.total.loading)

  useEffect(() => {
    getUploadProps(session).then(setUploadProps)
    hospitalInfo && fetchListFeature()
  }, [])

  // const fetchAllGlobalFeature = () => {
  //   dispatch(resourceFeatureActions.getListResourceFeature({}))
  // }

  const fetchListFeature = () => {
    dispatch(
      featureActions.getListFeature({
        partnerId: hospitalInfo?.partnerId,
        ...session
      })
    )
  }

  const onEdit = (values: any) => {
    dispatch(featureActions.updateFeature({ ...values, ...session }))
  }

  return (
    <MPFeatureSettings
      uploadProps={uploadProps}
      listFeatureByPartner={listFeatureByPartner}
      loading={loading}
      fetchListFeature={fetchListFeature}
      onEdit={onEdit}
    />
  )
}

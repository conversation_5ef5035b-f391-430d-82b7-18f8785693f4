import { MPReview, showError } from '@medpro-libs/medpro-component-libs'
import { useAppSelector } from '../../../store/hooks'
import React, { useCallback, useEffect, useState } from 'react'
import { useClientSDK } from '../../../hooks/useClientSDK'

function ReviewPage({ session }: any) {
  const { client } = useClientSDK(session)
  const hospitalInfo = useAppSelector(
    (state) => state.hospital.selectedHospital
  )
  const [evaluatePartnerList, setEvaluatePartnerList] = useState<any>([])
  const [fetchLoading, setFetchLoading] = useState(false)

  const fetchEvaluatePartnerList = useCallback(async () => {
    try {
      const response = await client?.evaluatePartner.getEvaluatePartnerList({
        hospitalId: hospitalInfo?._id || ''
      })
      if (response && response.data) {
        setEvaluatePartnerList(response.data)
      }
      setFetchLoading(false)
    } catch (error: any) {
      setFetchLoading(false)
      showError(error)
    }
  }, [client, hospitalInfo])

  useEffect(() => {
    setFetchLoading(true)
    void fetchEvaluatePartnerList()
  }, [fetchEvaluatePartnerList])

  return (
    <div>
      <MPReview
        evaluatePartnerList={evaluatePartnerList}
        fetchLoading={fetchLoading}
      />
    </div>
  )
}

export default ReviewPage

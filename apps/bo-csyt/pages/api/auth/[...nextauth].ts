import NextAuth from 'next-auth'
import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from 'next-auth/providers/credentials'
import { Client } from '@medpro-sdk-v2'
import clientSDK from '../../../config/medproSdk'
import { PageRoutes } from '../../../shared/routes'

export const authOptions: any = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        phone: {
          label: 'Phone Number',
          type: 'text',
          placeholder: 'Phone Number'
        },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        const client: Client = await clientSDK({ access_token: '' })
        const { phone, password } = credentials as Record<
          'phone' | 'password',
          string
        >
        // Call your external API for authentication
        const response = (await client.medproId
          .loginWithEmailPassword({
            username: phone,
            password
          })
          .catch((e: any) => {
            throw new Error(e?.response?.data?.message || e.message)
          })) as any
        if (response.data) {
          return {
            id: response.data.userName,
            access_token: response.data.access_token,
            userName: response.data.userName,
            partnerId: response.data.partnerId
          }
        }
        return null
      }
    })
  ],
  pages: {
    signIn: PageRoutes.login.path
  },
  secret: 'secret',
  callbacks: {
    async jwt({ token, user, account }: any) {
      if (user && account) {
        return { ...token, ...user, ...account }
      }
      return token
    },
    async session({ session, token }: any) {
      session.expires = '9999-12-31T23:59:59.999Z'
      return { ...session, ...token }
    }
  }
}

// Export NextAuth handler using authOptions
export default NextAuth(authOptions)

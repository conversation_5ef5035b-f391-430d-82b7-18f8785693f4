.pageContainer {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  cursor: auto;
  background-image: linear-gradient(to top, #fff1eb 0%, #ace0f9 100%);

  .cardWrapper {
    width: 470px;
    height: 100%;
    display: flex;
    align-items: center;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    > div {
      // box-shadow: @boxshadow;
      width: 100%;
      padding: 40px 20px;
      backdrop-filter: blur(3px);
      background: rgba(255, 255, 255, 0.5);
      background-image: linear-gradient(
        to top,
        #c7dbefbd 0%,
        rgba(234, 253, 253, 0.915) 100%
      );
      border-radius: 10px;
      @media only screen and (max-width: 1024px) {
        width: 100%;
      }
    }

    .formWrapper {
      width: 85%;
      margin-left: auto;
      margin-right: auto;

      .logo {
        width: 170px;
        margin-left: auto;
        margin-right: auto;

        img {
          width: 100%;
        }
      }

      .errorMessage {
        color: red;
        font-style: italic;
        margin-bottom: 14px;
      }

      .btnItem {
        button {
          text-transform: uppercase;
          font-weight: 400;
          background: #074085;
          border: #074085;
          width: 100%;

          &:hover {
            background: #1166ce;
          }
        }
      }
    }
  }
}

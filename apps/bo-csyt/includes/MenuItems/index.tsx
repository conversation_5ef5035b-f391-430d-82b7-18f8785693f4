import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import type { MenuProps } from 'antd'
import { Menu } from 'antd'

export const MenuItems = (props: any) => {
  const { menus } = props
  const [selectedKey, setSelectedKey] = useState<string>('')
  const [openKeys, setOpenKeys] = useState([])
  const router = useRouter()
  const items: MenuProps['items'] = menus

  useEffect(() => {
    if (router.pathname) {
      setSelectedKey(router.pathname)
    }
  }, [router.pathname])

  useEffect(() => {
    setOpenKeys(menus
      .filter((item: any) => item.children)
      .map((item: any) => item.key));
  }, [menus])

  const handleMenuClick = ({ key }: any) => {
    setSelectedKey(key)
    return router.push(key)
  }

  return (
    <Menu
      theme='light'
      mode='inline'
      selectedKeys={[selectedKey]}
      defaultSelectedKeys={[selectedKey]}
      items={items}
      onClick={handleMenuClick}
      openKeys={openKeys}
    />
  )
}

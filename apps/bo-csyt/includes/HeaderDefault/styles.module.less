.headerDefault {
  position: relative;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  box-shadow: 3px 0 10px #b7c0ce33;

  .rightHeader {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 75px;
  }

  .logo {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .btnMenu {
    margin: 16px 0;
    color: #ffffff;
    border: none;
    background: linear-gradient(86.64deg, #28b1e6 0%, #007bff 137.73%);
    background-size: 200% 100%;
    transition: background-position 0.5s ease-in-out;

    &:hover,
    &:active {
      background: linear-gradient(86.64deg, #28b1e6 0%, #007bff 137.73%) -100% 0 !important;
    }
  }
}

.menuDrawerWrapper {
  :global {
    .ant-drawer-body {
      padding: 0;

      .ant-menu {
        &.ant-menu-sub {
          background: transparent;
        }
      }

      .ant-menu {
        padding-left: 5px;
        padding-right: 5px;

        .ant-menu-item {
          &:hover {
            background: #0e82fd;
            color: #ffffff;
          }
        }

        .ant-menu-item-selected {
          background: #0e82fd;
          color: #ffffff;
        }
      }
    }
  }

  .widgetProfile {
    position: relative;
    z-index: 1;

    :global {
      .ant-btn-variant-solid {
        background: #ffffff;
        color: #333333;
      }
    }

    &.widgetProfileCollapsed {
      padding: 10px;
      display: flex;
      justify-content: center;

      :global {
        .ant-btn-variant-solid {
          color: #ffffff;
          background: linear-gradient(86.64deg, #28b1e6 0%, #007bff 137.73%);
        }
      }
    }

    .widgetProfileAvatar {
      padding: 70px 20px 0;
      position: relative;
      display: block !important;
      text-align: center !important;

      &:after {
        content: '';
        background-image: url('/images/bgDoctorSidebar.jpg');
        width: 100%;
        height: 148px;
        border-radius: 0;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
      }

      .avatar {
        display: inline-block;
        width: auto;
        background-color: transparent;
        margin: 0 0 20px !important;
        padding: 5px;
        border-radius: 50%;
        position: relative;

        &:after {
          content: '';
          width: 22px;
          height: 22px;
          position: absolute;
          right: 19px;
          bottom: 2px;
          background-image: url(/images/checkBoxGreen.png);
          background-size: 18px;
          background-repeat: no-repeat;
          border: 2px solid #fff;
          background-color: #fff;
          border-radius: 50%;
        }

        img {
          height: 120px;
          width: 120px;
          border-radius: 50%;
        }
      }
    }

    .widgetProfileInfo {
      position: relative;
      display: block !important;
      text-align: center !important;
      background: #f9fbff;
      padding: 15px 20px;
      margin-bottom: 15px;
      min-height: 150px;

      .detInfo {
        h3 {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .address {
          font-size: 14px;
          color: #465d7c;
          font-weight: 400;
        }
      }

      .env {
        font-size: 14px;
        margin-top: 15px;
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.45rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        border-radius: 4px;
        min-width: 90px;
        justify-content: center;

        &.testing {
          background: #ffe6e6;
          color: #ff0000;
          border: 1px solid #ff0000;
        }

        &.development {
          background: #acbcff;
          color: #002bd2;
          border: 1px solid #002bd2;
        }

        &.production,
        &.hotfix {
          background: #00d22e29;
          color: #00d22e;
          border: 1px solid #00d22e;
        }

        &.beta {
          background: #f1e8c4;
          color: #f1c40f;
          border: 1px solid #f1c40f;
        }
      }
    }

    .btnMenu {
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 3;

      &.collapsed {
        position: relative;
        right: unset;
        top: unset;
      }
    }
  }
}

.dropdownBadgeNotify {
  padding-right: 5px;
  padding-left: 5px;

  :global {
    .ant-dropdown-menu {
      padding: 0;
      overflow-y: unset;

      .ant-dropdown-menu-item {
        padding: 0;
      }
    }
  }
}

.badgeNotify {
  display: flex;
  cursor: pointer;
  width: 28px;
  height: 28px;
  align-items: center;
  justify-content: center;
  border: 1px solid #E6E8EE;
  background: #F9F9F9;
  border-radius: 50%;

  :global {
    .anticon-bell {
      font-size: 14px !important;
    }
  }
}

.dropdownMenu {
  min-width: 380px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  @media only screen and (max-width: 991px) {
    min-width: unset;
    width: 100%;
  }

  .card {
    border: none;
    padding: 0;

    :global {
      .ant-card-head {
        padding: 0 10px 0 10px;
        margin-bottom: 1px;
        border-bottom: 1px solid #e7e7e7;
      }

      .ant-card-body {
        padding: 0 0 10px;
        max-height: 350px;
        overflow: auto;
      }
    }

    .notificationItem {
      display: flex;
      padding: 10px;
      border-bottom: 1px solid #e7e7e7;
      cursor: pointer;
      min-height: 90px;

      &.notRead {
        background: #f1f1f1;
      }


      &:last-child {
        border-bottom: none;
      }

      .notificationContent {
        margin-left: 10px;
        flex: 1;

        .notificationHeader {
          display: flex;
          justify-content: space-between;
          font-weight: bold;

          .name {
            color: #000;
          }

          .time {
            color: gray;
            font-size: 12px;
          }
        }

        .message {
          font-size: 14px;
          color: #555;
          max-width: 315px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .doctor {
          color: #007bff;
          font-weight: bold;
        }
      }
    }
  }
}


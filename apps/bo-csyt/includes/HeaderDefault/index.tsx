import {
  MenuFoldOutlined,
  MenuOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons'
import { <PERSON><PERSON>, Drawer, Layout, Menu } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { MPImageFallback } from '@medpro-libs/medpro-component-libs'
import React, { forwardRef, useEffect, useState } from 'react'
import { nameEnv } from '../../config/envs/environment'
import { BadgeNotify } from './BadgeNotify'
import { DropdownAvatar } from './DropdownAvatar'
import styles from './styles.module.less'

export const HeaderDefault = forwardRef<HTMLDivElement, any>(
  (props: any, ref) => {
    const { collapsed, device, menus, hospitalInfo, environment, session } =
      props
    const [selectedKey, setSelectedKey] = useState<string>('')
    const [isOpenMenu, setIsOpenMenu] = useState(false)
    const [openKeys, setOpenKeys] = useState([])
    const router = useRouter()
    const { Header } = Layout

    useEffect(() => {
      if (router.pathname) {
        setSelectedKey(router.pathname)
      }
    }, [router.pathname])

    useEffect(() => {
      setOpenKeys(
        menus.filter((item: any) => item.children).map((item: any) => item.key)
      )
    }, [menus])

    const onOpenMenu = () => {
      setIsOpenMenu(!isOpenMenu)
    }

    const onCloseMenu = () => {
      setIsOpenMenu(!isOpenMenu)
    }

    const handleMenuClick = ({ key }: any) => {
      setSelectedKey(key)
      setIsOpenMenu(false)
      return router.push(key)
    }

    return (
      <Header ref={ref} className={styles['headerDefault']}>
        {['desktop'].includes(device) ? (
          <div className={styles['logoDesktop']}>
            {!collapsed ? (
              <Image
                src={
                  'https://bo-api.medpro.com.vn/static/images/medpro/web/header_logo.svg'
                }
                alt={'Medpro'}
                width={150}
                height={50}
                priority
              />
            ) : (
              <Image
                src={'/logo.png'}
                alt={'Medpro'}
                width={60}
                height={60}
                priority
              />
            )}
          </div>
        ) : (
          <>
            <Button
              type='primary'
              icon={<MenuOutlined />}
              onClick={onOpenMenu}
              className={styles['btnMenu']}
            />
            <div className={styles['logo']}>
              <Image
                src={'/logo.png'}
                alt={'Medpro'}
                width={60}
                height={60}
                priority
              />
            </div>
          </>
        )}
        <div className={styles['rightHeader']}>
          {/*<BadgeNotify />*/}
          <DropdownAvatar session={session} />
        </div>
        <Drawer
          title={''}
          placement={'left'}
          closable={false}
          onClose={onCloseMenu}
          open={isOpenMenu}
          className={styles['menuDrawerWrapper']}
        >
          <div className={cx(styles['widgetProfile'])}>
            <Button
              type='primary'
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setIsOpenMenu(!isOpenMenu)}
              className={cx(styles['btnMenu'])}
            />
            <div className={styles['widgetProfileAvatar']}>
              <div className={styles['avatar']}>
                <MPImageFallback
                  src={hospitalInfo?.circleLogo}
                  fallbackSrc={'/images/defaultSubject.jpg'}
                  width={126}
                  height={126}
                  alt={'Hospital'}
                  priority
                />
              </div>
            </div>
            <div className={styles['widgetProfileInfo']}>
              <div className={styles['detInfo']}>
                <h3>{hospitalInfo?.name}</h3>
                <div className={styles['address']}>{hospitalInfo?.address}</div>
              </div>
              {environment?.name !== 'production' && (
                <div
                  className={cx(
                    styles['env'],
                    nameEnv ? styles[nameEnv] : null
                  )}
                >
                  {environment?.name}
                </div>
              )}
            </div>
          </div>
          <Menu
            mode='inline'
            items={menus}
            onClick={handleMenuClick}
            selectedKeys={[selectedKey]}
            defaultSelectedKeys={[selectedKey]}
            openKeys={openKeys}
          />
        </Drawer>
      </Header>
    )
  }
)

HeaderDefault.displayName = 'HeaderDefault'

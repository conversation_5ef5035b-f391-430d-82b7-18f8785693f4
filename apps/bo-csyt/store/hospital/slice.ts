import type { PayloadAction } from '@reduxjs/toolkit'
import { createAction, createSlice } from '@reduxjs/toolkit'
import { HospitalState } from './interface'

const initialState: HospitalState = {
  list: [],
  priceList: [],
  faqMenu: [],
  questionList: [],
  faqCategories: []
}
createAction<any>('persist/REHYDRATE')
export const hospitalSlice = createSlice({
  name: 'hospital',
  initialState,
  reducers: {
    // Redux Toolkit allows us to write "mutating" logic in reducers. It
    // doesn't actually mutate the state because it uses the Immer library,
    // which detects changes to a "draft state" and produces a brand new
    // immutable state based off those changes
    getHospitalList: (_state, _action?: PayloadAction<any>) => {
      // run saga
    },
    setHospitalList: (state, action: PayloadAction<any[]>) => {
      state.list = action.payload
    },
    setHospitalInfo: (state, action: PayloadAction<any>) => {
      state.selectedHospital = action.payload
    },
    getHospitalInfo: (state, action: PayloadAction<any>) => {
      // run saga
    },
    setPriceList: (state, action: PayloadAction<any>) => {
      state.priceList = action.payload
    },
    getPriceList: (state, action: PayloadAction<any>) => {
      // run saga
    },
    getFAQList: (_state, _action: PayloadAction<any>) => {
      // run saga
    },
    setFAQList: (_state, _action: PayloadAction<any[]>) => {
      _state.questionList = _action.payload
    },
    setHospitalSelected: (state, action: PayloadAction<any>) => {
      state.selected = action.payload
    },
    getHospitalSelected: (state, action: PayloadAction<any>) => {
      // run saga
    },
  }
})

export const hospitalActions = hospitalSlice.actions
export const hospitalReducer = hospitalSlice.reducer

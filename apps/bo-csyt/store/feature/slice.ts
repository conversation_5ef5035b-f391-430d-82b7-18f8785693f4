/* eslint-disable @typescript-eslint/no-explicit-any */
import { createAction, createSlice } from '@reduxjs/toolkit'
import { featureState } from './interface'
import type { PayloadAction } from '@reduxjs/toolkit'

const initialState: featureState = {
  listFeatureByPartner: []
}

interface LoadingPayload {
  description?: string
}

const reHydrate = createAction<any>('persist/REHYDRATE')
export const featureSlice = createSlice({
  name: 'feature',
  initialState,
  reducers: {
    getListFeature: (_state, _action: PayloadAction<any>) => {
      // run saga
    },
    setListFeature: (state, action: PayloadAction<any>) => {
      state.listFeatureByPartner = action.payload
    },
    updateFeature: (_state, _action: PayloadAction<any>) => {
      // run saga
    }
    // addFeature: (_state, _action: PayloadAction<any>) => {
    //   // run saga
    // },
    // deleteFeature: (_state, _action: PayloadAction<any>) => {
    //   // run saga
    // },

    // setPageSize: (state, action: PayloadAction<number>) => {
    //   state.pageSize = action.payload
    // },
    // getListFeatureForVietUser: (_state, _action: PayloadAction<any>) => {
    //   // run saga
    // },
    // setListFeatureForVietUser: (state, action: PayloadAction<any>) => {
    //   state.listFeatureForVietUser = action.payload
    // }
  }
})

export const featureActions = featureSlice.actions
export const featureReducer = featureSlice.reducer

import { combineReducers, configureStore } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import createSagaMiddleware from 'redux-saga'
import storage from 'redux-persist/lib/storage'
import { createWrapper } from 'next-redux-wrapper'
import rootSaga from './rootSaga'
import { siteReducer } from './site/slice'
import { totalReducer } from './total/slice'
import { hospitalReducer } from './hospital/slice'
import { roleReducer } from './role/slice'
import { featureReducer } from './feature/slice'

const rootReducer = combineReducers({
  total: totalReducer,
  hospital: persistReducer({ key: 'hospital', storage }, hospitalReducer),
  site: persistReducer({ key: 'site', storage }, siteReducer),
  role: persistReducer({ key: 'role', storage }, roleReducer),
  feature: persistReducer({ key: 'feature', storage }, featureReducer)
})
const rootPersistConfig = {
  key: 'root',
  version: 1,
  storage,
  whitelist: [],
  blacklist: ['site', 'total', 'hospital', 'role', 'feature']
}
const persistedReducer = persistReducer(rootPersistConfig, rootReducer)
const makeStore = () => {
  const sagaMiddleware = createSagaMiddleware()
  const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false
      }).concat(sagaMiddleware),
    devTools: true
  })
  ;(store as any).sagaTask = sagaMiddleware.run(rootSaga)
  return store
}

export const wrapper = createWrapper(makeStore)
export type AppStore = ReturnType<typeof makeStore>
export type RootState = ReturnType<AppStore['getState']>
export type AppDispatch = AppStore['dispatch']

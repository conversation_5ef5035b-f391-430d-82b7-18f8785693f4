import React, { PropsWithChildren, ReactElement } from 'react'
import styles from './styles.module.less'

export interface LayoutProps extends PropsWithChildren {}

export const NotAuthLayout = (props: LayoutProps) => {
  const { children } = props
  return (
    <section className={styles['mainLayout']}>
      {React.Children.map(children, (child: any) =>
        React.isValidElement(child)
          ? React.cloneElement(child as ReactElement<any>, {})
          : child
      )}
    </section>
  )
}

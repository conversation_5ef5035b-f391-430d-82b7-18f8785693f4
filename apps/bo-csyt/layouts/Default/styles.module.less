.mainLayout {
  overflow: hidden;

  :global {
    .ant-layout {
      background: #ffffff;
    }
  }

  .siderScrollbarWrapper {
    overflow: hidden auto;
    padding-right: 2px;
    margin-top: 5px;
    margin-bottom: 5px;

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      border-radius: 50px;
      background-color: #f5f5f5;
    }

    &::-webkit-scrollbar {
      width: 5px;
      background-color: #f5f5f5;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 50px;
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      background-color: #cbcbcb;
      cursor: pointer;
    }
  }

  .siderWrapper {
    position: sticky;
    inset-inline-start: 0;
    top: 0;
    bottom: 0;
    background: #ffffff;
    margin-left: 10px;
    border: 1px solid #e6e8ee;
    box-shadow: 0 4px 14px 0 rgba(226, 237, 255, 0.25);
    border-radius: 10px;
    min-height: 100%;

    :global {
      .ant-menu-light {
        background: transparent;
      }

      .ant-menu {
        padding-left: 5px;
        padding-right: 5px;

        .ant-menu-item {
          &:hover {
            background: #0e82fd;
            color: #ffffff;
          }
        }

        .ant-menu-item-selected {
          background: #0e82fd;
          color: #ffffff;
        }
      }

      .ant-menu-light.ant-menu-root.ant-menu-inline,
      .ant-menu-light.ant-menu-root.ant-menu-vertical {
        border-inline-end: 0;
      }

      .ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
        background: transparent;
      }
    }

    .widgetProfile {
      position: relative;
      z-index: 1;

      :global {
        .ant-btn-variant-solid {
          background: #ffffff;
          color: #333333;
        }
      }

      &.widgetProfileCollapsed {
        padding: 10px;
        display: flex;
        justify-content: center;

        :global {
          .ant-btn-variant-solid {
            color: #ffffff;
            background: linear-gradient(86.64deg, #28b1e6 0%, #007bff 137.73%);
          }
        }
      }

      .widgetProfileAvatar {
        padding: 70px 20px 0;
        position: relative;
        display: block !important;
        text-align: center !important;

        &:after {
          content: '';
          background-image: url('/images/bgDoctorSidebar.jpg');
          width: 100%;
          height: 148px;
          border-radius: 10px 10px 0 0;
          position: absolute;
          top: 0;
          left: 0;
          z-index: -1;
        }

        .avatar {
          display: inline-block;
          width: 126px;
          height: 126px;
          background-color: #ffffff;
          margin: 0 0 20px !important;
          padding: 5px;
          border-radius: 50%;
          position: relative;

          &:after {
            content: '';
            width: 22px;
            height: 22px;
            position: absolute;
            right: 19px;
            bottom: 2px;
            background-image: url(/images/checkBoxGreen.png);
            background-size: 18px;
            background-repeat: no-repeat;
            border: 2px solid #fff;
            background-color: #fff;
            border-radius: 50%;
          }

          img {
            height: 120px;
            width: 120px;
            border-radius: 50%;
          }
        }
      }

      .widgetProfileInfo {
        position: relative;
        display: block !important;
        text-align: center !important;
        background: #e2ebff;
        padding: 15px 20px;
        margin-bottom: 15px;
        min-height: 150px;

        .detInfo {
          h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
          }

          .address {
            font-size: 14px;
            color: #465d7c;
            font-weight: 400;
          }
        }

        .env {
          font-size: 14px;
          margin-top: 15px;
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.45rem;
          font-weight: 600;
          letter-spacing: 0.5px;
          border-radius: 4px;
          min-width: 90px;
          justify-content: center;

          &.testing {
            background: #ffe6e6;
            color: #ff0000;
            border: 1px solid #ff0000;
          }

          &.development {
            background: #acbcff;
            color: #002bd2;
            border: 1px solid #002bd2;
          }

          &.production,
          &.hotfix {
            background: #00d22e29;
            color: #00d22e;
            border: 1px solid #00d22e;
          }

          &.beta {
            background: #f1e8c4;
            color: #f1c40f;
            border: 1px solid #f1c40f;
          }
        }
      }

      .btnMenu {
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 3;

        &.collapsed {
          position: relative;
          right: unset;
          top: unset;
        }
      }
    }

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      margin-top: 10px;
    }
  }

  .contentWrapper {
    overflow: hidden auto;
    margin: 5px 0 5px 5px;
    padding-right: 5px;

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      border-radius: 50px;
      background-color: #f5f5f5;
    }

    &::-webkit-scrollbar {
      width: 8px;
      background-color: #f5f5f5;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 50px;
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      background-color: #cbcbcb;
      cursor: pointer;
    }
  }
}

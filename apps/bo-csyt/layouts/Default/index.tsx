import React, {
  PropsWithChildren,
  ReactElement,
  useEffect,
  useRef,
  useState
} from 'react'
import { Layout, Button } from 'antd'
import { debounce } from 'lodash'
import cx from 'classnames'
import {
  MPImageFallback,
  useDynamicContentHeight,
  useWindow
} from '@medpro-libs/medpro-component-libs'
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons'
import { useSelector, useDispatch } from 'react-redux'
import { menus } from './data'
import { HeaderDefault } from '../../includes/HeaderDefault'
import { MenuItems } from '../../includes/MenuItems'
import { hospitalActions } from '../../store/hospital/slice'
import { useAppSelector } from '../../store/hooks'
import { nameEnv } from '../../config/envs/environment'
import styles from './styles.module.less'

const getDeviceType = () => (window.innerWidth <= 1000 ? 'mobile' : 'desktop')

export interface LayoutProps extends PropsWithChildren {}

// Cấu hình hiển thị cho từng môi trường
const environmentConfig = {
  development: { name: 'Development', color: 'blue' },
  testing: { name: 'Testing', color: 'orange' },
  beta: { name: 'Staging', color: 'purple' },
  production: { name: 'Live', color: 'green' },
  hotfix: { name: 'Live', color: 'red' }
}

export const DefaultLayout = (props: LayoutProps) => {
  const headerRef = useRef<HTMLDivElement>(null)
  const { height } = useWindow()
  const { heightContent } = useDynamicContentHeight([headerRef.current])
  const { children } = props
  const { Content, Sider } = Layout
  const [device, setDevice] = useState<'mobile' | 'desktop'>(getDeviceType)
  const [collapsed, setCollapsed] = useState(false)
  const [session, setSession] = useState(null)
  const dispatch = useDispatch()
  const hospitalInfo = useSelector(
    (state: any) => state.hospital.selectedHospital
  )
  const hospitalSelected = useAppSelector((state) => state.hospital.selected)

  // Lấy cấu hình môi trường
  const environment = environmentConfig[nameEnv] || {
    name: 'Unknown',
    color: 'gray'
  }

  useEffect(() => {
    if (React.isValidElement(children)) {
      const session = (children as ReactElement<{ session?: any }>).props
        .session
      if (session?.partnerId || hospitalSelected?.partnerId) {
        setSession(session)
        dispatch(
          hospitalActions.getHospitalInfo({
            partnerId: hospitalSelected?.partnerId || session.partnerId,
            ...session
          })
        )
        dispatch(
          hospitalActions.setHospitalSelected({
            partnerId: hospitalSelected?.partnerId || session.partnerId
          })
        )
      }
    }
  }, [])

  useEffect(() => {
    const checkDevice = debounce(() => {
      setDevice(getDeviceType())
    }, 100) as any

    window.addEventListener('resize', checkDevice)
    return () => window.removeEventListener('resize', checkDevice)
  }, [])

  return (
    <section className={styles['mainLayout']} style={{ height: height }}>
      <Layout>
        <HeaderDefault
          ref={headerRef}
          menus={menus}
          device={device}
          setCollapsed={setCollapsed}
          collapsed={collapsed}
          hospitalInfo={hospitalInfo}
          environment={environment}
          session={session}
        />
        <Layout hasSider style={{ height: heightContent }}>
          {['desktop'].includes(device) && (
            <div className={styles['siderScrollbarWrapper']}>
              <Sider
                width={300}
                collapsed={collapsed}
                onCollapse={setCollapsed}
                className={styles['siderWrapper']}
              >
                <div
                  className={cx(
                    styles['widgetProfile'],
                    collapsed ? styles['widgetProfileCollapsed'] : null
                  )}
                >
                  {['desktop'].includes(device) ? (
                    <Button
                      type='primary'
                      icon={
                        collapsed ? (
                          <MenuUnfoldOutlined />
                        ) : (
                          <MenuFoldOutlined />
                        )
                      }
                      onClick={() => setCollapsed(!collapsed)}
                      className={cx(
                        styles['btnMenu'],
                        collapsed ? styles['collapsed'] : null
                      )}
                    />
                  ) : null}
                  {!collapsed ? (
                    <>
                      <div className={styles['widgetProfileAvatar']}>
                        <div className={styles['avatar']}>
                          <MPImageFallback
                            src={hospitalInfo?.circleLogo}
                            fallbackSrc={'/images/defaultSubject.jpg'}
                            width={126}
                            height={126}
                            alt={'Hospital'}
                            priority
                          />
                        </div>
                      </div>
                      <div className={styles['widgetProfileInfo']}>
                        <div className={styles['detInfo']}>
                          <h3>{hospitalInfo?.name}</h3>
                          <div className={styles['address']}>
                            {hospitalInfo?.address}
                          </div>
                        </div>
                        <div
                          className={cx(
                            styles['env'],
                            nameEnv ? styles[nameEnv] : null
                          )}
                        >
                          {environment.name}
                        </div>
                      </div>
                    </>
                  ) : null}
                </div>
                <MenuItems menus={menus} />
              </Sider>
            </div>
          )}
          <Content
            // style={{
            //   // overflow: 'auto'
            //   // minHeight: !['desktop'].includes(device) ? '100vh' : 'auto'
            // }}
            className={styles['contentWrapper']}
          >
            {React.Children.map(children, (child: any) =>
              React.isValidElement(child)
                ? React.cloneElement(child as ReactElement<any>, {
                    heightContent,
                    hospitalInfo
                  })
                : child
            )}
          </Content>
        </Layout>
      </Layout>
    </section>
  )
}

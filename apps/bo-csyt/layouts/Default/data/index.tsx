import {
  SettingOutlined,
  ToolOutlined,
  AppstoreOutlined
} from '@ant-design/icons'
import { VscPreview } from 'react-icons/vsc'
import { GiSettingsKnobs } from 'react-icons/gi'
import { MdOutlineManageAccounts, MdListAlt } from 'react-icons/md'
import { PageRoutes } from '../../../shared/routes'

export const menus = [
  {
    key: PageRoutes.dashboard.overview.path,
    icon: <AppstoreOutlined />,
    label: 'Tổng quan'
  },
  {
    key: PageRoutes.dashboard.generalSettings.path,
    icon: <SettingOutlined />,
    label: 'Cấu hình thông tin'
  },
  {
    key: PageRoutes.dashboard.featureSettings.path,
    icon: <ToolOutlined />,
    label: '<PERSON>h sách tính năng'
  },
  {
    key: PageRoutes.dashboard.review.path,
    icon: <VscPreview />,
    label: 'Đánh giá'
  }
  // {
  //   key: PageRoutes.dashboard.userManagement.path,
  //   icon: <MdOutlineManageAccounts />,
  //   label: 'Quản lý người dùng'
  // }
  // {
  //   key: PageRoutes.dashboard.permissionManagement.path,
  //   icon: <MdListAlt />,
  //   label: 'Quản lý permission'
  // },
  // {
  //   key: PageRoutes.dashboard.moduleManagement.path,
  //   icon: <MdListAlt />,
  //   label: 'Quản lý module'
  // },
  // {
  //   key: PageRoutes.dashboard.roleManagement.path,
  //   icon: <MdListAlt />,
  //   label: 'Quản lý quyền'
  // }
]

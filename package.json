{"name": "@medpro-nx-react-libs/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:bo-csyt": "nx dev bo-csyt --port=3002", "start:bo-mkt": "nx dev bo-mkt --port=3003"}, "private": true, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@ckeditor/ckeditor5-react": "^9.5.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@eslint/compat": "^1.1.1", "@eslint/js": "^9.8.0", "@nrwl/next": "^19.8.4", "@nx/devkit": "20.2.2", "@nx/eslint": "20.2.2", "@nx/eslint-plugin": "20.2.2", "@nx/jest": "20.2.2", "@nx/js": "20.2.2", "@nx/next": "20.2.2", "@nx/react": "20.2.2", "@nx/rollup": "20.2.2", "@nx/workspace": "20.2.2", "@reduxjs/toolkit": "^2.5.0", "@rollup/plugin-url": "^8.0.2", "@svgr/rollup": "^8.1.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.16", "@types/node": "18.16.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/validator": "^13.12.2", "antd": "^5.23.1", "antd-img-crop": "^4.24.0", "babel-jest": "^29.7.0", "chart.js": "^4.4.8", "ckeditor5-custom-build": "file:ckeditor5", "core-js": "^3.36.1", "eslint": "^9.8.0", "eslint-config-next": "14.2.16", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "less": "3.12.2", "less-loader": "11.1.0", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "^14.2.16", "next-auth": "^4.24.11", "next-redux-wrapper": "^8.1.0", "next-transpile-modules": "^10.0.1", "nx": "20.2.2", "prettier": "^2.6.2", "rc-collapse": "^4.0.0", "rc-form": "^2.4.12", "rc-input": "^1.7.2", "rc-motion": "^2.9.5", "rc-pagination": "^5.0.0", "rc-picker": "^4.9.2", "rc-resize-observer": "^1.4.3", "rc-table": "^7.50.4", "rc-tabs": "^15.6.1", "rc-tooltip": "^6.4.0", "rc-tree": "^5.13.1", "rc-trigger": "^5.3.4", "rc-util": "^5.44.3", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "rollup": "^4.14.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.6.2", "typescript-eslint": "^8.13.0", "validator": "^13.12.0", "vanilla-jsoneditor": "^3.3.1"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/compat": "^1.1.1", "@eslint/js": "^9.8.0", "@nrwl/next": "^19.8.4", "@nx/devkit": "20.2.2", "@nx/eslint": "20.2.2", "@nx/eslint-plugin": "20.2.2", "@nx/jest": "20.2.2", "@nx/js": "20.2.2", "@nx/next": "20.2.2", "@nx/react": "20.2.2", "@nx/rollup": "20.2.2", "@nx/workspace": "20.2.2", "@rollup/plugin-url": "^8.0.2", "@svgr/rollup": "^8.1.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.16", "@types/node": "18.16.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.0", "@types/validator": "^13.12.2", "babel-jest": "^29.7.0", "core-js": "^3.36.1", "eslint": "^9.8.0", "eslint-config-next": "14.2.16", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "less": "3.12.2", "less-loader": "11.1.0", "nx": "20.2.2", "prettier": "^2.6.2", "rollup": "^4.14.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.6.2", "typescript-eslint": "^8.13.0"}}
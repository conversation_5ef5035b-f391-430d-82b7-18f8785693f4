{"version": 3, "file": "ckeditor.js", "mappings": ";;;;AAAA", "sources": ["webpack://DecoupledDocumentEditor/webpack/universalModuleDefinition"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"DecoupledDocumentEditor\"] = factory();\n\telse\n\t\troot[\"DecoupledDocumentEditor\"] = factory();\n})(self, () => {\nreturn "], "names": [], "sourceRoot": ""}
{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "plugins": [{"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/rollup/plugin", "options": {"buildTargetName": "build"}}], "generators": {"@nx/next": {"application": {"style": "less", "linter": "eslint"}}, "@nx/react": {"library": {"unitTestRunner": "none"}}}}